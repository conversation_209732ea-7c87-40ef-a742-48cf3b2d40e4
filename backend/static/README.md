# Static Files Directory

This directory contains static files for the Django backend application.

## Structure

- `admin/`: Contains admin-specific static files
  - `css/`: CSS files for the admin interface
  - `js/`: JavaScript files for the admin interface

## Adding Static Files

When adding new static files:

1. Place them in the appropriate subdirectory
2. Run `python manage.py collectstatic` to collect them into the `staticfiles` directory
3. In development, the files will be served directly from this directory

## Frontend Integration

For frontend static files:

1. The frontend application should be built separately
2. The build output should be placed in `frontend/dist`
3. The `fix_static_files` management command will handle any issues with missing directories

## Troubleshooting

If you encounter static files issues:

1. Run `python manage.py fix_static_files` to fix common issues
2. Check the Django settings to ensure `STATICFILES_DIRS` includes the correct directories
3. Verify that the `collectstatic` command is running successfully
