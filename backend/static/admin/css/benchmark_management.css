/* ACTIVE_FILE - 29-05-2025 */
.card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    margin-bottom: 20px;
    padding: 20px;
}

.tab-content {
    padding: 20px 0;
}

.nav-tabs {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
}

.btn-primary {
    color: #fff;
    background-color: #447e9b;
    border-color: #447e9b;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.table th {
    background-color: #f5f5f5;
}

.hidden {
    display: none;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.json-editor {
    height: 300px;
    width: 100%;
    font-family: monospace;
}

.filter-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.filter-item {
    flex: 1;
    min-width: 200px;
}

.action-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 90%;
    max-width: 1200px;
    border-radius: 5px;
    max-height: 90vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

/* Core Concepts Help Modal Styles */
.concept-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.concept-section h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.concept-section p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.concept-section ul {
    margin-left: 20px;
    margin-bottom: 10px;
}

.concept-section li {
    margin-bottom: 5px;
    line-height: 1.4;
}

.concept-section code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

#concept-help-btn {
    font-size: 0.9em;
    padding: 6px 12px;
}

#concept-help-btn i {
    margin-right: 5px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.help-text {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
    font-style: italic;
}

/* Enhanced Template Modal Styles */
.template-tab-nav {
    display: flex;
    border-bottom: 2px solid #ddd;
    margin-bottom: 20px;
    gap: 5px;
}

.template-tab-btn {
    padding: 10px 20px;
    border: none;
    background: #f8f9fa;
    color: #495057;
    cursor: pointer;
    border-radius: 5px 5px 0 0;
    transition: all 0.3s ease;
    font-weight: 500;
}

.template-tab-btn:hover {
    background: #e9ecef;
    color: #212529;
}

.template-tab-btn.active {
    background: #007bff;
    color: white;
    border-bottom: 2px solid #007bff;
}

.template-tab-content {
    display: none;
    padding: 20px 0;
}

.template-tab-content.active {
    display: block;
}

/* Ensure variable ranges builder is visible when active */
#variable-ranges-builder {
    display: none;
}

#variable-ranges-builder.active {
    display: block !important;
}

/* Contextual Criteria Builder Styles */
.contextual-builder {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.variable-sections {
    margin-bottom: 20px;
}

.variable-section {
    margin-bottom: 30px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.variable-section h5 {
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.range-definitions {
    display: grid;
    gap: 15px;
}

.range-def {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.range-def label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.criteria-inputs {
    display: grid;
    gap: 8px;
}

.criteria-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.criteria-input-group label {
    min-width: 80px;
    font-weight: 500;
    margin-bottom: 0;
}

.criteria-input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9em;
}

.criteria-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Variable Ranges Builder Styles */
.variable-ranges-builder {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.variable-range-sections {
    margin-bottom: 20px;
}

.range-section {
    margin-bottom: 25px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.range-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 15px;
    align-items: end;
}

.range-inputs label {
    font-weight: 500;
    color: #495057;
}

.range-inputs input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9em;
}

.sub-range {
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.sub-range h6 {
    color: #495057;
    margin-bottom: 10px;
}

/* Context Preview Styles */
.context-preview {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.context-simulator {
    background: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.context-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-range {
    width: 100%;
    height: 6px;
    background: #dee2e6;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.preview-results {
    background: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.criteria-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.context-summary {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    border-left: 4px solid #2196f3;
}

.context-summary h6 {
    color: #1976d2;
    margin-bottom: 10px;
}

.context-summary p {
    margin-bottom: 5px;
    font-size: 0.9em;
}

.adapted-criteria {
    background: #f1f8e9;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #4caf50;
}

.adapted-criteria h6 {
    color: #388e3c;
    margin-bottom: 10px;
}

.criteria-dimension {
    margin-bottom: 15px;
}

.criteria-dimension strong {
    color: #495057;
}

/* Help System Styles */
.help-modal {
    display: none;
    position: fixed;
    z-index: 2000; /* Higher than main modal */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.help-modal.show {
    opacity: 1;
}

.help-modal-content {
    background-color: #ffffff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    transition: transform 0.2s ease;
}

.help-modal.show .help-modal-content {
    transform: translateY(0);
}

.help-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.help-modal-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.help-modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.help-modal-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

.help-modal-body {
    padding: 20px;
    line-height: 1.6;
}

.help-modal-body h6 {
    color: #495057;
    font-weight: 600;
    margin-top: 15px;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.help-modal-body p {
    margin-bottom: 12px;
    color: #6c757d;
}

.help-modal-body ul {
    margin-bottom: 15px;
    padding-left: 20px;
}

.help-modal-body li {
    margin-bottom: 6px;
    color: #6c757d;
}

.help-modal-body pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    font-size: 0.85rem;
    overflow-x: auto;
    margin: 10px 0;
}

.help-modal-body strong {
    color: #495057;
    font-weight: 600;
}

/* Help Icon Styles */
.help-icon {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 0.9rem;
    margin-left: 8px;
    padding: 2px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.help-icon:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.1);
}

.help-icon.section-help {
    font-size: 1rem;
    margin-left: 10px;
    width: 24px;
    height: 24px;
}

.help-icon.variable-help {
    font-size: 0.9rem;
    margin-left: 8px;
    width: 20px;
    height: 20px;
}

/* Variable section headers with help icons */
.variable-section h5 {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Form group help icon positioning */
.form-group label .help-icon {
    margin-left: 6px;
    font-size: 0.85rem;
    width: 18px;
    height: 18px;
}

/* Builder header help icon positioning */
.builder-header .help-icon {
    margin-left: auto;
    margin-right: 0;
}

.builder-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Responsive adjustments for help modal */
@media (max-width: 768px) {
    .help-modal-content {
        width: 95%;
        margin: 10% auto;
        max-height: 70vh;
    }

    .help-modal-body {
        padding: 15px;
    }
}

.criteria-dimension ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.criteria-dimension li {
    margin-bottom: 3px;
    font-size: 0.9em;
}

/* Enhanced Contextual Criteria Builder Styles */
.contextual-builder.enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.builder-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.builder-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    gap: 20px;
}

.preset-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preset-selector select {
    min-width: 200px;
}

.builder-actions {
    display: flex;
    gap: 10px;
}

.variable-section.enhanced {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.variable-section.enhanced:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.range-visual {
    flex: 1;
    margin-left: 20px;
}

.range-bar {
    display: flex;
    height: 30px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.range-segment {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.range-segment:hover {
    filter: brightness(1.1);
}

.range-segment.foundation {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.range-segment.expansion {
    background: linear-gradient(45deg, #ffd93d, #ffed4e);
    color: #333;
}

.range-segment.integration {
    background: linear-gradient(45deg, #6bcf7f, #8dd99c);
}

.mood-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 2px;
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mood-quadrant {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7em;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mood-quadrant:hover {
    filter: brightness(1.1);
}

.mood-quadrant[data-valence="positive"][data-arousal="high"] {
    background: linear-gradient(45deg, #ff9500, #ffb347);
}

.mood-quadrant[data-valence="positive"][data-arousal="low"] {
    background: linear-gradient(45deg, #4caf50, #66bb6a);
}

.mood-quadrant[data-valence="negative"][data-arousal="high"] {
    background: linear-gradient(45deg, #f44336, #ef5350);
}

.mood-quadrant[data-valence="negative"][data-arousal="low"] {
    background: linear-gradient(45deg, #2196f3, #42a5f5);
}

.env-indicators {
    display: flex;
    gap: 15px;
}

.indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.indicator.stress {
    color: #ff6b6b;
}

.indicator.time {
    color: #ffd93d;
}

.range-def.enhanced {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.range-def.enhanced:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.range-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.range-description {
    font-size: 0.85em;
    color: #6c757d;
    font-style: italic;
}

.range-actions {
    display: flex;
    gap: 5px;
}

.criteria-input-group.enhanced {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.criteria-input-group.enhanced label {
    min-width: 80px;
    font-weight: 500;
    margin-bottom: 0;
}

.input-container {
    flex: 1;
    position: relative;
}

.criteria-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.criteria-tag {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

.coverage-indicator {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.coverage-bars {
    display: grid;
    gap: 10px;
}

.coverage-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.coverage-item span {
    min-width: 100px;
    font-weight: 500;
}

.progress {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

/* Enhanced Variable Ranges Builder Styles */
.variable-ranges-builder.enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 400px; /* Ensure minimum height for visibility */
}

/* Ensure variable ranges builder is always visible when its tab is active */
#variable-ranges-builder.template-tab-content {
    display: none;
}

#variable-ranges-builder.template-tab-content.active {
    display: block !important;
}

.range-section.enhanced {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.range-section.enhanced:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.range-visual-bar {
    margin-left: 20px;
    flex: 1;
}

.visual-range {
    height: 25px;
    border-radius: 12px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.range-label {
    font-size: 0.75em;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.range-label.left {
    color: #333;
}

.range-inputs.enhanced {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: start;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group.full-width {
    grid-column: 1 / -1;
}

.input-group label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.input-help {
    font-size: 0.75em;
    color: #6c757d;
    font-style: italic;
}

/* Enhanced input validation styles */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Character counter styles */
.char-counter {
    font-size: 0.75em;
    color: #6c757d;
    text-align: right;
    margin-top: 2px;
}

/* Range tester styles */
.range-tester, .mood-tester {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.tester-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.test-result {
    font-size: 0.85em;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    margin-top: 5px;
}

.test-result .text-success {
    color: #28a745;
}

.test-result .text-danger {
    color: #dc3545;
}

.test-result .text-warning {
    color: #ffc107;
}

.mood-space-visual {
    margin-left: 20px;
    position: relative;
}

.mood-quadrants {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 2px;
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quadrant {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7em;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.quadrant:hover {
    filter: brightness(1.1);
}

.quadrant.q1 {
    background: linear-gradient(45deg, #ff9500, #ffb347);
}

.quadrant.q2 {
    background: linear-gradient(45deg, #4caf50, #66bb6a);
}

.quadrant.q3 {
    background: linear-gradient(45deg, #2196f3, #42a5f5);
}

.quadrant.q4 {
    background: linear-gradient(45deg, #f44336, #ef5350);
}

.axes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.axis-label {
    position: absolute;
    font-size: 0.7em;
    font-weight: 600;
    color: #495057;
}

.axis-label.valence {
    bottom: -20px;
    right: 0;
}

.axis-label.arousal {
    top: 0;
    right: -50px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.sub-range.enhanced {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.ranges-summary {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.summary-grid {
    display: grid;
    gap: 10px;
}

.summary-item {
    display: grid;
    grid-template-columns: 150px 120px 1fr;
    gap: 15px;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.summary-name {
    font-weight: 600;
    color: #495057;
}

.summary-range {
    font-family: monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
}

.summary-desc {
    font-size: 0.85em;
    color: #6c757d;
}

/* Preset preview styles */
.preset-preview {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    position: relative;
}

.preset-preview-content h6 {
    color: #1976d2;
    margin-bottom: 10px;
}

.preset-stats {
    margin-top: 10px;
}

/* Section controls styling */
.section-controls {
    display: flex;
    gap: 5px;
}

.btn-xs {
    padding: 2px 8px;
    font-size: 0.75em;
    line-height: 1.2;
}

/* Guidance content styling */
.guidance-content {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.guidance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

.guidance-item h7 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

/* Suggestion buttons */
.suggestion-buttons {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.suggestion-btn {
    padding: 4px 8px;
    font-size: 0.75em;
}

/* Enhanced button styles */
.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* Enhanced Context Preview Styles */
.context-preview.enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.preview-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.preview-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.context-simulator {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.context-controls {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.value-display {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

.form-range {
    width: 100%;
    height: 6px;
    background: #dee2e6;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.form-range::-webkit-slider-thumb:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.form-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.form-range::-moz-range-thumb:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75em;
    color: #6c757d;
    margin-top: 5px;
}

.simulator-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.preview-results {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.criteria-display.enhanced {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    min-height: 200px;
}

.placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: #6c757d;
    text-align: center;
}

.placeholder i {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.5;
}

.scenario-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.scenario-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-height: 80px;
}

.scenario-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.scenario-btn.active {
    background: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
}

.scenario-btn i {
    font-size: 1.2em;
}

.scenario-btn span {
    font-weight: 600;
}

.scenario-btn small {
    font-size: 0.75em;
    opacity: 0.8;
}

.context-analysis {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.analysis-label {
    font-weight: 500;
    color: #495057;
}

.analysis-value {
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
}

.analysis-value.foundation {
    background: #ff6b6b;
    color: white;
}

.analysis-value.expansion {
    background: #ffd93d;
    color: #333;
}

.analysis-value.integration {
    background: #6bcf7f;
    color: white;
}

.analysis-value.low {
    background: #28a745;
    color: white;
}

.analysis-value.medium {
    background: #ffc107;
    color: #333;
}

.analysis-value.high {
    background: #dc3545;
    color: white;
}

.analysis-value.relaxed {
    background: #17a2b8;
    color: white;
}

.analysis-value.moderate {
    background: #fd7e14;
    color: white;
}

.analysis-value.urgent {
    background: #e83e8c;
    color: white;
}

.context-summary.enhanced {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.context-summary.enhanced h6 {
    color: #1976d2;
    margin-bottom: 15px;
}

.context-details {
    display: grid;
    gap: 10px;
}

.context-item {
    display: grid;
    grid-template-columns: 100px 120px 1fr;
    gap: 10px;
    align-items: center;
    font-size: 0.9em;
}

.context-item .label {
    font-weight: 600;
    color: #495057;
}

.context-item .value {
    font-family: monospace;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
}

.context-item .phase,
.context-item .quadrant,
.context-item .level {
    font-size: 0.75em;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    color: #495057;
}

.adapted-criteria.enhanced {
    background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
    border: 1px solid #4caf50;
    border-radius: 8px;
    padding: 15px;
}

.adapted-criteria.enhanced h6 {
    color: #388e3c;
    margin-bottom: 15px;
}

.criteria-dimension.enhanced {
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 10px;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.criteria-count {
    font-size: 0.75em;
    background: #4caf50;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.criteria-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.criterion-tag {
    background: #4caf50;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

/* Validation styles */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.validation-status {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.preset-scenarios {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.scenario-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.scenario-buttons .btn {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* Builder Actions */
.builder-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.builder-actions .btn {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* Validation Status */
.validation-status {
    margin-top: 15px;
}

.validation-status .alert {
    margin-bottom: 0;
    padding: 10px 15px;
    font-size: 0.9em;
}

.validation-status ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.nav-tabs .nav-link {
    display: inline-block;
    padding: 10px 15px;
    text-decoration: none;
    color: #447e9b;
    border: 1px solid transparent;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* Benchmark Testing Styles */
.benchmark-testing {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.benchmark-testing h5 {
    color: #495057;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.benchmark-testing .help-text {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 20px;
}

.test-configuration {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.config-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    align-items: end;
}

.config-row:last-child {
    margin-bottom: 0;
}

.config-group {
    flex: 1;
}

.config-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.config-group input[type="checkbox"] {
    margin-right: 8px;
}

.test-status {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.status-indicator i {
    color: #856404;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #28a745;
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.test-results {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
}

.test-results h6 {
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.results-summary {
    margin-bottom: 20px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.summary-label {
    font-weight: 500;
    color: #495057;
}

.summary-value {
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
}

.summary-value.success {
    background: #28a745;
    color: white;
}

.summary-value.warning {
    background: #ffc107;
    color: #333;
}

.results-table-container {
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.results-table th {
    background: #f8f9fa;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.results-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.results-table tr.success-row {
    background: rgba(40, 167, 69, 0.05);
}

.results-table tr.failure-row {
    background: rgba(220, 53, 69, 0.05);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-weight: bold;
    font-size: 0.9em;
}

.status-badge.success {
    background: #28a745;
    color: white;
}

.status-badge.failure {
    background: #dc3545;
    color: white;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .config-row {
        flex-direction: column;
        gap: 10px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .results-table-container {
        font-size: 0.85em;
    }

    .results-table th,
    .results-table td {
        padding: 8px 4px;
    }
}

/* Enhanced Contextual Criteria Builder Styles */
.contextual-builder.enhanced {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.builder-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.builder-header h4 {
    color: #495057;
    margin-bottom: 5px;
}

.builder-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.preset-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preset-selector label {
    font-weight: 500;
    margin-bottom: 0;
}

.preset-preview {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 12px;
    margin-top: 10px;
    position: relative;
}

.preset-preview-content h6 {
    color: #1976d2;
    margin-bottom: 8px;
}

.preset-preview-content p {
    color: #424242;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.preset-stats {
    margin-top: 8px;
}

.smart-suggestions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.suggestions-header h6 {
    color: #856404;
    margin-bottom: 0;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.suggestion-item {
    background: white;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
}

.suggestion-item h7 {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.suggestion-item p {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 8px;
}

.section-controls {
    display: flex;
    gap: 5px;
}

.trust-guidance, .mood-guidance, .env-guidance {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.guidance-content h6 {
    color: #0c5460;
    margin-bottom: 10px;
}

.guidance-content ul {
    margin-bottom: 8px;
}

.guidance-content li {
    margin-bottom: 5px;
    font-size: 0.9em;
}

.guidance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.guidance-item h7 {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.range-bar.interactive {
    cursor: pointer;
}

.range-segment.highlighted {
    box-shadow: 0 0 15px rgba(0,123,255,0.5);
    transform: scale(1.05);
}

.range-label {
    font-size: 0.8em;
    font-weight: 600;
}

.range-values {
    font-size: 0.7em;
    opacity: 0.9;
}

.mood-visualization {
    margin-top: 15px;
}

.mood-space {
    position: relative;
}

.mood-grid.interactive .mood-quadrant {
    cursor: pointer;
}

.mood-quadrant.highlighted {
    box-shadow: inset 0 0 20px rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.quadrant-label {
    font-size: 0.9em;
    font-weight: 600;
}

.quadrant-coords {
    font-size: 0.7em;
    opacity: 0.8;
    margin-top: 2px;
}

.mood-axes {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 0.8em;
    color: #6c757d;
}

.axis-label.valence {
    text-align: center;
    flex: 1;
}

.axis-label.arousal {
    position: absolute;
    left: -60px;
    top: 50%;
    transform: rotate(-90deg);
    transform-origin: center;
}

.mood-tester, .range-tester {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.tester-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.test-result {
    font-size: 0.9em;
    font-weight: 500;
}

.env-visualization {
    margin-top: 15px;
}

.env-indicators.enhanced {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.indicator.interactive {
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.interactive:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.indicator {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #e9ecef;
    border-radius: 6px;
    font-size: 0.9em;
}

.indicator.stress {
    color: #dc3545;
    border-left: 4px solid #dc3545;
}

.indicator.time {
    color: #fd7e14;
    border-left: 4px solid #fd7e14;
}

.indicator-bar {
    display: flex;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.bar-segment {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7em;
    font-weight: 600;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bar-segment:hover {
    transform: scaleY(1.1);
}

.bar-segment.highlighted {
    box-shadow: 0 0 10px rgba(0,123,255,0.5);
    transform: scaleY(1.2);
}

.bar-segment.low {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.bar-segment.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.bar-segment.high {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.range-def.enhanced.highlighted {
    border-color: #007bff;
    box-shadow: 0 0 10px rgba(0,123,255,0.2);
}

.range-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-title label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.range-badge {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7em;
    font-weight: 500;
}

.range-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.criteria-count {
    font-size: 0.8em;
    color: #6c757d;
}

.completeness-indicator {
    flex: 1;
    max-width: 100px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-left: 10px;
}

.completeness-bar {
    height: 100%;
    background: #28a745;
    transition: width 0.3s ease;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dimension-header label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9em;
    margin-bottom: 0;
}

.input-feedback {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 0.75em;
    color: #6c757d;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    font-size: 0.9em;
    cursor: pointer;
    padding: 0;
    margin-left: 4px;
}

.tag-remove:hover {
    color: #ffcccc;
}

.dimension-suggestions {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-top: 5px;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.suggestion-tag {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-tag:hover {
    background: #007bff;
    color: white;
}

.coverage-percentage {
    min-width: 40px;
    text-align: right;
    font-size: 0.8em;
    font-weight: 600;
    color: #495057;
}

.coverage-summary {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

/* Enhanced Variable Ranges Builder Styles */
.variable-ranges-builder.enhanced {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.range-testing {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.testing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.testing-header h6 {
    color: #856404;
    margin-bottom: 0;
}

.testing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.test-section {
    background: white;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
}

.test-section h7 {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 8px;
}

.test-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ranges-summary.enhanced {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.85em;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: #495057;
}

.range-section.enhanced {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.trust-ranges-guidance, .mood-ranges-guidance, .env-ranges-guidance {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.range-visual-bar.enhanced {
    margin-top: 15px;
}

.visual-range.interactive {
    cursor: pointer;
    position: relative;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    color: white;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.range-label.center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.range-tester {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.range-inputs.enhanced {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group.full-width {
    grid-column: 1 / -1;
}

.input-group label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9em;
    margin-bottom: 0;
}

.input-help {
    font-size: 0.75em;
    color: #6c757d;
    font-style: italic;
}

.input-validation {
    font-size: 0.75em;
    margin-top: 2px;
}

.char-counter {
    font-size: 0.75em;
    color: #6c757d;
    text-align: right;
    margin-top: 2px;
}

.range-suggestions {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 8px;
}

.range-suggestions h7 {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 8px;
    font-size: 0.85em;
}

.suggestion-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.suggestion-btn {
    font-size: 0.75em;
    padding: 4px 8px;
}

.mood-space-visual.enhanced {
    margin-top: 15px;
}

.mood-quadrants.interactive {
    position: relative;
}

.mood-quadrants.interactive .quadrant {
    cursor: pointer;
    transition: all 0.3s ease;
}

.mood-quadrants.interactive .quadrant:hover {
    transform: scale(1.05);
}

.current-range-indicator {
    margin-top: 8px;
    padding: 6px 10px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.85em;
    text-align: center;
}

.range-track {
    display: flex;
    height: 30px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #dee2e6;
    margin-top: 8px;
}

.range-track .range-segment {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75em;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.segment-label {
    font-size: 0.8em;
    font-weight: 600;
}

/* Button size variants */
.btn-xs {
    padding: 2px 6px;
    font-size: 0.75em;
    line-height: 1.2;
    border-radius: 3px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.85em;
    line-height: 1.3;
    border-radius: 3px;
}

/* Button color variants */
.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-success {
    color: #28a745;
    border-color: #28a745;
    background-color: transparent;
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8;
    background-color: transparent;
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;
    background-color: transparent;
}

.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
    background-color: transparent;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Form control size variants */
.form-control-sm {
    padding: 4px 8px;
    font-size: 0.85em;
    line-height: 1.3;
    border-radius: 3px;
}

/* Text color utilities */
.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-muted {
    color: #6c757d !important;
}

/* Test Error Display Styles */
.test-error-display {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.test-error-display h6 {
    color: #721c24;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.test-error-display h6 i {
    color: #dc3545;
}

.error-message {
    background: #fff;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.error-message pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #721c24;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

.error-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.error-actions .btn {
    padding: 6px 12px;
    font-size: 0.9em;
}

/* Progress bar error state */
.progress-fill {
    transition: all 0.3s ease;
}

.progress-fill.error {
    background-color: #dc3545 !important;
}

/* Test status error styling */
.test-status.error .status-indicator i {
    color: #dc3545;
}

.test-status.error .status-indicator span {
    color: #721c24;
}

/* Multi-Range Results Styles */
.multi-range-summary {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.multi-range-summary h6 {
    color: #1976d2;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.multi-range-details {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
}

.multi-range-details h6 {
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-results-container {
    overflow-x: auto;
}

.range-results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.range-results-table th,
.range-results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.range-results-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.range-results-table tbody tr:hover {
    background-color: #f8f9fa;
}

.range-results-table .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
}

.range-results-table .status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.range-results-table .status-badge.failure {
    background-color: #f8d7da;
    color: #721c24;
}

/* Multi-Range Combinations Styles */
.multi-range-combinations {
    background: white;
    padding: 20px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #e9ecef;
}

.multi-range-combinations h6 {
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.combinations-summary {
    background: #e3f2fd;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    border-left: 4px solid #2196f3;
}

.combinations-summary p {
    margin: 0;
    font-weight: 500;
    color: #1976d2;
}

.combinations-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.combinations-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.combinations-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 10px 8px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.combinations-table td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    font-weight: 500;
}

.combination-index {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #6c757d;
    width: 50px;
}

.combination-cell {
    transition: all 0.2s ease;
    cursor: help;
}

.combination-cell:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Trust Level Colors */
.trust-foundation {
    background-color: #fff3cd;
    color: #856404;
    border-left: 3px solid #ffc107;
}

.trust-expansion {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 3px solid #17a2b8;
}

.trust-integration {
    background-color: #d4edda;
    color: #155724;
    border-left: 3px solid #28a745;
}

/* Mood Valence Colors */
.mood-negative {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 3px solid #dc3545;
}

.mood-positive {
    background-color: #d4edda;
    color: #155724;
    border-left: 3px solid #28a745;
}

/* Arousal Colors */
.arousal-calm {
    background-color: #e2e3e5;
    color: #383d41;
    border-left: 3px solid #6c757d;
}

.arousal-excited {
    background-color: #ffeaa7;
    color: #856404;
    border-left: 3px solid #fd79a8;
}

/* Stress Level Colors */
.stress-low {
    background-color: #d4edda;
    color: #155724;
    border-left: 3px solid #28a745;
}

.stress-medium {
    background-color: #fff3cd;
    color: #856404;
    border-left: 3px solid #ffc107;
}

.stress-high {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 3px solid #dc3545;
}

/* Time Pressure Colors */
.pressure-relaxed {
    background-color: #d4edda;
    color: #155724;
    border-left: 3px solid #28a745;
}

.pressure-moderate {
    background-color: #fff3cd;
    color: #856404;
    border-left: 3px solid #ffc107;
}

.pressure-urgent {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 3px solid #dc3545;
}

/* Default Variable Color */
.variable-default {
    background-color: #f8f9fa;
    color: #495057;
    border-left: 3px solid #dee2e6;
}

/* Combination Set Management Styles */
.combination-set-controls {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.control-row {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    align-items: end;
}

.control-row:last-child {
    margin-bottom: 0;
}

.control-group {
    flex: 1;
    min-width: 200px;
}

.control-group label {
    display: block;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.control-group input,
.control-group select {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9em;
}

.control-group input:focus,
.control-group select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Combination Row Selection Styles */
.combination-row {
    transition: all 0.2s ease;
}

.combination-row:hover {
    background-color: #f8f9fa;
}

.combination-row.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.combination-row.selected:hover {
    background-color: #bbdefb;
}

.combination-checkbox {
    cursor: pointer;
    transform: scale(1.1);
}

.combination-checkbox:checked {
    accent-color: #007bff;
}

/* Select All Checkbox Styles */
#select-all-checkbox {
    cursor: pointer;
    transform: scale(1.1);
}

#select-all-checkbox:indeterminate {
    accent-color: #ffc107;
}

/* Selected Count Indicator */
.selected-count {
    color: #007bff;
    font-weight: 600;
    background: #e3f2fd;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    margin-left: 8px;
}

/* Button Size Variants */
.btn-sm {
    padding: 4px 8px;
    font-size: 0.8em;
    line-height: 1.3;
    border-radius: 3px;
}

/* Responsive Adjustments for Combination Controls */
@media (max-width: 768px) {
    .control-row {
        flex-direction: column;
        gap: 10px;
    }

    .control-group {
        min-width: auto;
    }

    .combinations-table-container {
        font-size: 0.8em;
    }

    .combinations-table th,
    .combinations-table td {
        padding: 6px 4px;
    }
}

/* User Profile Management Styles */
.trust-level-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.trust-level-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
}

.trust-phase {
    font-size: 0.9em;
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 2px;
}

.trust-phase.trust-foundation {
    background-color: #fff3cd;
    color: #856404;
}

.trust-phase.trust-expansion {
    background-color: #d1ecf1;
    color: #0c5460;
}

.trust-phase.trust-integration {
    background-color: #d4edda;
    color: #155724;
}

.trust-level-guide {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.demographics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.demo-item {
    display: flex;
    flex-direction: column;
}

.demo-item label {
    font-weight: bold;
    margin-bottom: 5px;
}

.hexaco-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hexaco-trait {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.hexaco-trait label {
    display: block;
    font-weight: bold;
    margin-bottom: 8px;
}

.hexaco-trait small {
    display: block;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

.hexaco-trait .form-range {
    width: 100%;
    margin: 8px 0;
}

.profile-preview .preview-sections {
    display: grid;
    gap: 20px;
}

.profile-preview .preview-section {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.profile-preview .preview-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
}

.profile-preview .hexaco-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.profile-preview pre {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-size: 0.9em;
    max-height: 200px;
    overflow-y: auto;
}

.template-selector {
    padding: 20px;
}

.template-selector h4 {
    margin-top: 0;
    color: #495057;
}

.template-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.template-info p {
    margin-bottom: 8px;
}

.template-info p:last-child {
    margin-bottom: 0;
}

/* User Profiles Table Styles */
#user-profiles-table {
    font-size: 0.9em;
}

#user-profiles-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

#user-profiles-table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.profile-trust-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.profile-trust-badge.foundation {
    background-color: #fff3cd;
    color: #856404;
}

.profile-trust-badge.expansion {
    background-color: #d1ecf1;
    color: #0c5460;
}

.profile-trust-badge.integration {
    background-color: #d4edda;
    color: #155724;
}

.profile-archetype-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.75em;
    background-color: #e9ecef;
    color: #495057;
}

.profile-demographics-summary {
    font-size: 0.8em;
    color: #6c757d;
}

.profile-personality-summary {
    font-size: 0.8em;
    color: #6c757d;
    max-width: 150px;
}

.profile-status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.profile-status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.profile-status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.profile-usage-count {
    font-weight: bold;
    color: #007bff;
}

/* Responsive adjustments for user profiles */
@media (max-width: 768px) {
    .demographics-grid {
        grid-template-columns: 1fr;
    }

    .hexaco-grid {
        grid-template-columns: 1fr;
    }

    .trust-level-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .trust-level-display {
        min-width: auto;
    }
}

/* Quick Test Modal Styles */
.quick-test-modal {
    z-index: 1100;
}

.quick-test-modal .modal-content {
    max-width: 800px;
    width: 90%;
}

.quick-test-form {
    display: grid;
    gap: 20px;
}

.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.form-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.criteria-list {
    display: grid;
    gap: 10px;
    margin-bottom: 15px;
}

.criteria-item {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 10px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.criteria-item input,
.criteria-item select {
    margin: 0;
}

.criteria-item .btn {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1;
}

.add-criteria-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.add-criteria-btn:hover {
    background: #218838;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.modal-actions .btn {
    padding: 10px 20px;
    font-size: 14px;
}

/* Quick Test Results Styles */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.result-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.result-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.result-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-value.excellent {
    color: #28a745;
}

.result-value.good {
    color: #17a2b8;
}

.result-value.fair {
    color: #ffc107;
}

.result-value.poor {
    color: #dc3545;
}

.result-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.result-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
}
