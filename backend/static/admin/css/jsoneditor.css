/* Place this file in your static/admin/css/jsoneditor.css */

textarea.json-schema {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    white-space: pre;
    tab-size: 2;
    -moz-tab-size: 2;
    min-height: 120px;
    max-height: 500px;
}

.json-editor-toolbar {
    margin-bottom: 5px;
    padding: 5px 0;
}

.json-format-btn, .json-validate-btn {
    padding: 5px 10px;
    margin-right: 8px;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: background-color 0.2s;
}

.json-format-btn {
    background-color: #417690;
}

.json-validate-btn {
    background-color: #79aec8;
}

.json-format-btn:hover, .json-validate-btn:hover {
    opacity: 0.9;
}

.json-validation-status {
    display: inline-block;
    margin-left: 10px;
    font-weight: bold;
    transition: color 0.3s;
}

/* JSON syntax highlighting (basic) */
.json-string { color: #008000; }
.json-number { color: #0000ff; }
.json-boolean { color: #b22222; }
.json-null { color: #808080; }
.json-key { color: #a52a2a; }

/* Field-specific customizations */
#id_input_schema, #id_output_schema {
    border-left: 3px solid #417690;
}

#id_state_schema, #id_memory_schema {
    border-left: 3px solid #79aec8;
}

#id_read_models {
    border-left: 3px solid #5cb85c;
}

#id_write_models {
    border-left: 3px solid #d9534f;
}

#id_recommend_models {
    border-left: 3px solid #f0ad4e;
}

/* Admin improvements */
.field-get_processing_stats ul {
    margin-left: 20px;
    padding: 0;
}

.field-get_processing_stats li {
    margin-bottom: 5px;
}

/* Make headers more prominent in fieldsets */
fieldset h2 {
    margin-top: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
    color: #417690;
}

/* Add some spacing between inline elements */
.inline-related {
    margin-top: 20px;
    border-top: 2px solid #f5f5f5;
    padding-top: 10px;
}

.inline-related h3 {
    color: #417690;
    margin-bottom: 10px;
}