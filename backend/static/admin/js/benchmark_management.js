// ACTIVE_FILE - 29-05-2025
/**
 * JavaScript for the benchmark management page.
 *
 * This file contains the JavaScript code for the benchmark management page,
 * including event handlers for buttons and AJAX calls to the API.
 *
 * Note: This file is being refactored to split evaluation template functionality
 * into separate modules for better maintainability.
 */

// Function to get CSRF token from cookies
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Core Concepts Help Modal Functions
function showConceptHelpModal() {
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function hideConceptHelpModal() {
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Function to show success message
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success';
    alertDiv.textContent = message;

    // Insert at the top of the content
    const contentMain = document.querySelector('.content-main');
    if (contentMain) {
        contentMain.insertBefore(alertDiv, contentMain.firstChild);
    } else {
        document.body.insertBefore(alertDiv, document.body.firstChild);
    }


    // Remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Function to show error message
function showError(message, targetElementId) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger';
    alertDiv.textContent = message;

    let targetElement;
    if (targetElementId) {
        targetElement = document.getElementById(targetElementId);
        if (targetElement) {
            targetElement.innerHTML = ''; // Clear previous errors
            targetElement.appendChild(alertDiv);
            targetElement.classList.remove('hidden');
        } else {
            console.error(`Error target element with ID "${targetElementId}" not found.`);
            // Fallback to main window if target element not found
            targetElement = document.querySelector('.content-main');
            if (targetElement) {
                targetElement.insertBefore(alertDiv, targetElement.firstChild);
            } else {
                document.body.insertBefore(alertDiv, document.body.firstChild);
            }
        }
    } else {
        // Default to inserting at the top of the content
        targetElement = document.querySelector('.content-main');
        if (targetElement) {
            targetElement.insertBefore(alertDiv, targetElement.firstChild);
        } else {
            document.body.insertBefore(alertDiv, document.body.firstChild);
        }
    }

    // Remove after 5 seconds if not in a modal (modals handle their own clearing)
    if (!targetElementId || (targetElement && !targetElement.closest('.modal-content'))) {
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}


// Scenarios functions
async function loadScenarios() {
    const scenariosTable = document.getElementById('scenarios-table');
    const scenariosLoading = document.getElementById('scenarios-loading');
    const scenariosEmpty = document.getElementById('scenarios-empty');

    if (!scenariosTable || !scenariosLoading || !scenariosEmpty) {
        console.warn('Scenarios table elements not found. Skipping loadScenarios.');
        return;
    }
    const tableBody = scenariosTable.querySelector('tbody');
    if (!tableBody) {
        console.warn('Scenarios table body not found. Skipping loadScenarios.');
        return;
    }


    // Show loading, hide table and empty message
    scenariosTable.classList.add('hidden');
    scenariosEmpty.classList.add('hidden');
    scenariosLoading.classList.remove('hidden');

    // Get filter values
    const agentRole = document.getElementById('agent-role-filter').value;
    const workflowType = document.getElementById('workflow-type-filter').value;
    const tagId = document.getElementById('tag-filter').value;
    const isActive = document.getElementById('active-filter').value;

    // Build query string
    let queryParams = [];
    if (agentRole) queryParams.push(`agent_role=${encodeURIComponent(agentRole)}`);
    if (workflowType) queryParams.push(`workflow_type=${encodeURIComponent(workflowType)}`);
    if (tagId) queryParams.push(`tag=${encodeURIComponent(tagId)}`);
    if (isActive) queryParams.push(`is_active=${encodeURIComponent(isActive)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

    try {
        const response = await fetch(`${window.BENCHMARK_SCENARIOS_API_URL}${queryString}`);

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const data = await response.json();

        // Clear table body
        tableBody.innerHTML = '';

        if (data.scenarios && data.scenarios.length > 0) {
            // Populate table
            data.scenarios.forEach(scenario => {
                const row = document.createElement('tr');

                // Format tags
                const tagsList = scenario.tags.map(tag => tag.name).join(', ');

                row.innerHTML = `
                    <td><input type="checkbox" class="scenario-checkbox" data-id="${scenario.id}"></td>
                    <td>${scenario.name}</td>
                    <td>${scenario.agent_role}</td>
                    <td>${scenario.workflow_type}</td>
                    <td>${tagsList}</td>
                    <td>${scenario.is_active ? 'Active' : 'Inactive'}</td>
                    <td>
                        <button class="btn btn-secondary edit-scenario" data-id="${scenario.id}">Edit</button>
                        <button class="btn btn-danger delete-scenario" data-id="${scenario.id}">Delete</button>
                        <button class="btn btn-primary validate-scenario" data-id="${scenario.id}">Validate</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to buttons
            document.querySelectorAll('.edit-scenario').forEach(btn => {
                btn.addEventListener('click', function() {
                    const scenarioId = this.getAttribute('data-id');
                    editScenario(scenarioId);
                });
            });

            document.querySelectorAll('.delete-scenario').forEach(btn => {
                btn.addEventListener('click', function() {
                    const scenarioId = this.getAttribute('data-id');
                    deleteScenario(scenarioId);
                });
            });

            document.querySelectorAll('.validate-scenario').forEach(btn => {
                btn.addEventListener('click', function() {
                    const scenarioId = this.getAttribute('data-id');
                    validateScenario(scenarioId);
                });
            });

            // Add event listeners for checkboxes
            document.querySelectorAll('.scenario-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedScenariosCount);
            });

            // Add event listener for select all checkbox
            const selectAllCheckbox = document.getElementById('select-all-scenarios-checkbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.addEventListener('change', function() {
                    document.querySelectorAll('.scenario-checkbox').forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedScenariosCount();
                });
            }

            // Update selected count
            updateSelectedScenariosCount();

            // Show table, hide loading and empty message
            scenariosTable.classList.remove('hidden');
            scenariosLoading.classList.add('hidden');
            scenariosEmpty.classList.add('hidden');
        } else {
            // Show empty message, hide table and loading
            scenariosTable.classList.add('hidden');
            scenariosLoading.classList.add('hidden');
            scenariosEmpty.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading scenarios:', error);
        showError(`Error loading scenarios: ${error.message}`);

        // Hide loading, show empty message
        scenariosLoading.classList.add('hidden');
        scenariosEmpty.classList.remove('hidden');
    }
}

function resetFilters() {
    const agentRoleFilter = document.getElementById('agent-role-filter');
    const workflowTypeFilter = document.getElementById('workflow-type-filter');
    const tagFilter = document.getElementById('tag-filter');
    const activeFilter = document.getElementById('active-filter');

    if (agentRoleFilter) agentRoleFilter.value = '';
    if (workflowTypeFilter) workflowTypeFilter.value = '';
    if (tagFilter) tagFilter.value = '';
    if (activeFilter) activeFilter.value = '';

    loadScenarios();
}

// Templates functions
async function loadTemplates() {
    const templatesTable = document.getElementById('templates-table');
    const templatesLoading = document.getElementById('templates-loading');
    const templatesEmpty = document.getElementById('templates-empty');

    if (!templatesTable || !templatesLoading || !templatesEmpty) {
        console.warn('Templates table elements not found. Skipping loadTemplates.');
        return;
    }
    const tableBody = templatesTable.querySelector('tbody');
    if (!tableBody) {
        console.warn('Templates table body not found. Skipping loadTemplates.');
        return;
    }

    // Show loading, hide table and empty message
    templatesTable.classList.add('hidden');
    templatesEmpty.classList.add('hidden');
    templatesLoading.classList.remove('hidden');

    // Get filter values
    const workflowType = document.getElementById('template-workflow-type-filter')?.value || '';
    const status = document.getElementById('template-status-filter')?.value || '';
    const category = document.getElementById('template-category-filter')?.value || '';

    // Build query string
    let queryParams = [];
    if (workflowType) queryParams.push(`workflow_type=${encodeURIComponent(workflowType)}`);
    if (status) queryParams.push(`status=${encodeURIComponent(status)}`);
    if (category) queryParams.push(`category=${encodeURIComponent(category)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

    try {
        // Use the real API endpoint
        const response = await fetch(`${window.TEMPLATES_API_URL}${queryString}`);

        let data;
        try {
            data = await response.json();
        } catch (parseError) {
            throw new Error(`Failed to parse server response: ${parseError.message}`);
        }

        if (!response.ok) {
            // Extract detailed error message from the response
            const errorMessage = data.error || `HTTP error ${response.status}`;
            throw new Error(errorMessage);
        }
        const filteredTemplates = data.templates || [];

        // Clear table body
        tableBody.innerHTML = '';

        if (filteredTemplates && filteredTemplates.length > 0) {
            // Populate table
            filteredTemplates.forEach(template => {
                const row = document.createElement('tr');

                // Format date
                const createdDate = new Date(template.created_at).toLocaleDateString();

                row.innerHTML = `
                    <td><input type="checkbox" class="template-checkbox" data-id="${template.id}"></td>
                    <td>${template.name}</td>
                    <td>${template.description || 'No description'}</td>
                    <td>${template.workflow_type || 'All Types'}</td>
                    <td>${template.category}</td>
                    <td>${template.is_active ? 'Active' : 'Inactive'}</td>
                    <td>${createdDate}</td>
                    <td>
                        <button class="btn btn-secondary edit-template" data-id="${template.id}">Edit</button>
                        <button class="btn btn-danger delete-template" data-id="${template.id}">Delete</button>
                        <button class="btn btn-info export-template" data-id="${template.id}">Export</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to buttons
            document.querySelectorAll('.edit-template').forEach(btn => {
                btn.addEventListener('click', function() {
                    const templateId = this.getAttribute('data-id');
                    editTemplate(templateId);
                });
            });

            document.querySelectorAll('.delete-template').forEach(btn => {
                btn.addEventListener('click', function() {
                    const templateId = this.getAttribute('data-id');
                    deleteTemplate(templateId);
                });
            });

            document.querySelectorAll('.export-template').forEach(btn => {
                btn.addEventListener('click', function() {
                    const templateId = this.getAttribute('data-id');
                    exportTemplate(templateId);
                });
            });

            // Add event listeners for checkboxes
            document.querySelectorAll('.template-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedTemplatesCount);
            });

            // Add event listener for select all checkbox
            const selectAllCheckbox = document.getElementById('select-all-templates-checkbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.addEventListener('change', function() {
                    document.querySelectorAll('.template-checkbox').forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedTemplatesCount();
                });
            }

            // Update selected count
            updateSelectedTemplatesCount();

            // Show table, hide loading and empty message
            templatesTable.classList.remove('hidden');
            templatesLoading.classList.add('hidden');
            templatesEmpty.classList.add('hidden');
        } else {
            // Show empty message, hide table and loading
            templatesTable.classList.add('hidden');
            templatesLoading.classList.add('hidden');
            templatesEmpty.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading templates:', error);
        showError(`Error loading templates: ${error.message}`);

        // Hide loading, show empty message
        templatesLoading.classList.add('hidden');
        templatesEmpty.classList.remove('hidden');
    }
}

function resetTemplateFilters() {
    const workflowTypeFilter = document.getElementById('template-workflow-type-filter');
    const statusFilter = document.getElementById('template-status-filter');
    const categoryFilter = document.getElementById('template-category-filter');

    if (workflowTypeFilter) workflowTypeFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (categoryFilter) categoryFilter.value = '';

    loadTemplates();
}

function loadValidationScenarios() { console.log('loadValidationScenarios called'); }
function loadWorkflowTypeStats() { console.log('loadWorkflowTypeStats called'); }

function showCreateScenarioModal() {
    console.log('showCreateScenarioModal called');
    const modal = document.getElementById('scenario-modal');
    if (modal) {
        // Reset form fields if needed
        const form = document.getElementById('scenario-form');
        if (form) form.reset();
        document.getElementById('scenario-modal-title').textContent = 'Create New Scenario';
        document.getElementById('scenario-id').value = '';
        modal.style.display = 'block';
    }
}

function showCreateTemplateModal() {
    console.log('showCreateTemplateModal called - attempting to show modal');

    // Use the new modular evaluation template modal
    if (window.evaluationTemplateModal) {
        window.evaluationTemplateModal.show();
    } else {
        console.error('EvaluationTemplateModal not available');
        showError('Template editor not available. Please refresh the page.');
    }
}

function showImportTemplateModal() {
    const importForm = document.getElementById('import-template-form');
    if (importForm) importForm.reset();

    const importPreview = document.getElementById('template-import-preview');
    if (importPreview) importPreview.textContent = 'No file selected';

    // Clear previous validation errors
    const importErrorMsg = document.getElementById('import-template-error-message');
    if (importErrorMsg) {
        importErrorMsg.innerHTML = '';
        importErrorMsg.classList.add('hidden');
    }

    // Show modal
    const importModal = document.getElementById('import-template-modal');
    if (importModal) importModal.style.display = 'block';
}

// Enhanced Template Modal Functions

function initializeTemplateModalTabs() {
    const modal = document.getElementById('template-modal');
    if (!modal) return;

    // Create tab navigation if it doesn't exist
    let tabNav = modal.querySelector('.template-tab-nav');
    if (!tabNav) {
        tabNav = document.createElement('div');
        tabNav.className = 'template-tab-nav';
        tabNav.innerHTML = `
            <button type="button" class="template-tab-btn active" data-tab="basic">Basic Info</button>
            <button type="button" class="template-tab-btn" data-tab="criteria">Base Criteria</button>
            <button type="button" class="template-tab-btn" data-tab="contextual">Contextual Criteria</button>
            <button type="button" class="template-tab-btn" data-tab="variables">Variable Ranges</button>
            <button type="button" class="template-tab-btn" data-tab="preview">Preview</button>
        `;

        // Insert after the modal title
        const title = modal.querySelector('#template-modal-title');
        if (title) {
            title.parentNode.insertBefore(tabNav, title.nextSibling);
        }
    }

    // Add tab click handlers
    tabNav.querySelectorAll('.template-tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            switchTemplateTab(this.dataset.tab);
        });
    });

    // Initialize with basic tab active
    switchTemplateTab('basic');
}

function switchTemplateTab(tabName) {
    const modal = document.getElementById('template-modal');
    if (!modal) return;

    // Update tab buttons
    modal.querySelectorAll('.template-tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Show/hide form sections
    const sections = {
        'basic': ['template-name', 'template-description', 'template-workflow-type-select', 'template-category-select', 'template-is-active'],
        'criteria': ['template-criteria'],
        'contextual': ['contextual-criteria-builder'],
        'variables': ['variable-ranges-builder'],
        'preview': ['context-preview-container']
    };

    // Hide all form groups first
    modal.querySelectorAll('.form-group').forEach(group => {
        group.style.display = 'none';
    });

    // Show relevant form groups for current tab
    if (sections[tabName]) {
        sections[tabName].forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                const formGroup = field.closest('.form-group');
                if (formGroup) {
                    formGroup.style.display = 'block';
                }
            }
        });
    }

    // Special handling for custom containers
    const containers = modal.querySelectorAll('.template-tab-content');
    containers.forEach(container => {
        container.style.display = container.id === `${tabName}-tab-content` ? 'block' : 'none';
    });
}

function initializeContextualCriteriaBuilder() {
    const modal = document.getElementById('template-modal');
    if (!modal) return;

    // Create contextual criteria builder container
    let builderContainer = modal.querySelector('#contextual-criteria-builder');
    if (!builderContainer) {
        builderContainer = document.createElement('div');
        builderContainer.id = 'contextual-criteria-builder';
        builderContainer.className = 'template-tab-content';
        builderContainer.innerHTML = `
            <div class="contextual-builder">
                <h4>Contextual Criteria Builder</h4>
                <p class="help-text">Define how evaluation criteria adapt based on contextual variables.</p>

                <div class="variable-sections">
                    <div class="variable-section" data-variable="trust_level">
                        <h5>Trust Level Adaptations</h5>
                        <div class="range-definitions">
                            <div class="range-def" data-range="0-39">
                                <label>Foundation (0-39):</label>
                                <div class="criteria-inputs"></div>
                            </div>
                            <div class="range-def" data-range="40-69">
                                <label>Expansion (40-69):</label>
                                <div class="criteria-inputs"></div>
                            </div>
                            <div class="range-def" data-range="70-100">
                                <label>Integration (70-100):</label>
                                <div class="criteria-inputs"></div>
                            </div>
                        </div>
                    </div>

                    <div class="variable-section" data-variable="mood">
                        <h5>Mood Adaptations</h5>
                        <div class="mood-subsections">
                            <div class="subsection" data-subsection="valence">
                                <h6>Valence (Emotional Positivity)</h6>
                                <div class="range-definitions">
                                    <div class="range-def" data-range="-1.0-0.0">
                                        <label>Negative (-1.0 to 0.0):</label>
                                        <div class="criteria-inputs"></div>
                                    </div>
                                    <div class="range-def" data-range="0.0-1.0">
                                        <label>Positive (0.0 to 1.0):</label>
                                        <div class="criteria-inputs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="variable-section" data-variable="environment">
                        <h5>Environment Adaptations</h5>
                        <div class="env-subsections">
                            <div class="subsection" data-subsection="stress_level">
                                <h6>Stress Level</h6>
                                <div class="range-definitions">
                                    <div class="range-def" data-range="0-30">
                                        <label>Low Stress (0-30):</label>
                                        <div class="criteria-inputs"></div>
                                    </div>
                                    <div class="range-def" data-range="31-70">
                                        <label>Medium Stress (31-70):</label>
                                        <div class="criteria-inputs"></div>
                                    </div>
                                    <div class="range-def" data-range="71-100">
                                        <label>High Stress (71-100):</label>
                                        <div class="criteria-inputs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="builder-actions">
                    <button type="button" class="btn btn-secondary" onclick="loadSampleContextualCriteria()">Load Sample</button>
                    <button type="button" class="btn btn-info" onclick="validateContextualCriteria()">Validate</button>
                    <button type="button" class="btn btn-primary" onclick="updateContextualCriteriaJSON()">Update JSON</button>
                </div>
            </div>
        `;

        // Insert into modal form
        const form = modal.querySelector('#template-form');
        if (form) {
            form.appendChild(builderContainer);
        }
    }

    // Initialize criteria inputs
    initializeCriteriaInputs();
}

function initializeCriteriaInputs() {
    const builder = document.getElementById('contextual-criteria-builder');
    if (!builder) return;

    // Add input fields for each criteria dimension
    const dimensions = ['Tone', 'Content', 'Approach', 'Structure'];

    builder.querySelectorAll('.criteria-inputs').forEach(container => {
        container.innerHTML = '';

        dimensions.forEach(dimension => {
            const inputGroup = document.createElement('div');
            inputGroup.className = 'criteria-input-group';
            inputGroup.innerHTML = `
                <label>${dimension}:</label>
                <input type="text" class="form-control criteria-input"
                       data-dimension="${dimension}"
                       placeholder="Enter criteria separated by commas">
            `;
            container.appendChild(inputGroup);
        });
    });
}


function initializeContextPreview() {
    const modal = document.getElementById('template-modal');
    if (!modal) return;

    // Create context preview container
    let previewContainer = modal.querySelector('#context-preview-container');
    if (!previewContainer) {
        previewContainer = document.createElement('div');
        previewContainer.id = 'context-preview-container';
        previewContainer.className = 'template-tab-content';
        previewContainer.innerHTML = `
            <div class="context-preview">
                <h4>Context Preview & Testing</h4>
                <p class="help-text">Test how your template adapts to different contextual scenarios.</p>

                <div class="context-simulator">
                    <h5>Context Simulator</h5>
                    <div class="context-controls">
                        <div class="control-group">
                            <label>Trust Level: <span id="trust-level-value">50</span></label>
                            <input type="range" id="trust-level-slider" min="0" max="100" value="50" class="form-range">
                        </div>

                        <div class="control-group">
                            <label>Mood Valence: <span id="valence-value">0.0</span></label>
                            <input type="range" id="valence-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                        </div>

                        <div class="control-group">
                            <label>Mood Arousal: <span id="arousal-value">0.0</span></label>
                            <input type="range" id="arousal-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                        </div>

                        <div class="control-group">
                            <label>Stress Level: <span id="stress-level-value">30</span></label>
                            <input type="range" id="stress-level-slider" min="0" max="100" value="30" class="form-range">
                        </div>

                        <div class="control-group">
                            <label>Time Pressure: <span id="time-pressure-value">30</span></label>
                            <input type="range" id="time-pressure-slider" min="0" max="100" value="30" class="form-range">
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="updateContextPreview()">Update Preview</button>
                </div>

                <div class="preview-results">
                    <h5>Adapted Criteria Preview</h5>
                    <div id="adapted-criteria-display" class="criteria-display">
                        <p class="text-muted">Adjust context variables and click "Update Preview" to see adapted criteria.</p>
                    </div>
                </div>

                <div class="preset-scenarios">
                    <h5>Preset Scenarios</h5>
                    <div class="scenario-buttons">
                        <button type="button" class="btn btn-outline-secondary" onclick="loadPresetScenario('new-user')">New User</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="loadPresetScenario('stressed-user')">Stressed User</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="loadPresetScenario('confident-user')">Confident User</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="loadPresetScenario('low-mood')">Low Mood</button>
                    </div>
                </div>
            </div>
        `;

        // Insert into modal form
        const form = modal.querySelector('#template-form');
        if (form) {
            form.appendChild(previewContainer);
        }
    }

    // Initialize slider event listeners
    initializeContextSliders();
}

function initializeContextSliders() {
    const sliders = [
        { id: 'trust-level-slider', valueId: 'trust-level-value' },
        { id: 'valence-slider', valueId: 'valence-value' },
        { id: 'arousal-slider', valueId: 'arousal-value' },
        { id: 'stress-level-slider', valueId: 'stress-level-value' },
        { id: 'time-pressure-slider', valueId: 'time-pressure-value' }
    ];

    sliders.forEach(({ id, valueId }) => {
        const slider = document.getElementById(id);
        const valueDisplay = document.getElementById(valueId);

        if (slider && valueDisplay) {
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }
    });
}

function validateSelectedScenarios() { console.log('validateSelectedScenarios called'); }
function validateAllScenarios() { console.log('validateAllScenarios called'); }
function batchValidateScenarios() { console.log('batchValidateScenarios called'); }
function batchActivateScenarios() { console.log('batchActivateScenarios called'); }
function batchDeactivateScenarios() { console.log('batchDeactivateScenarios called'); }
function batchAddTagToScenarios() { console.log('batchAddTagToScenarios called'); }
function viewScenariosForWorkflowType(workflowType) { console.log(`viewScenariosForWorkflowType called for ${workflowType}`); }

// Helper functions for contextual criteria builder

function loadSampleContextualCriteria() {
    const sampleData = {
        "trust_level": {
            "0-39": {
                "Tone": ["Simple", "Clear", "Reassuring"],
                "Content": ["Safe options", "Low-risk activities"],
                "Approach": ["Step-by-step", "Basic"]
            },
            "40-69": {
                "Tone": ["Encouraging", "Supportive", "Motivating"],
                "Content": ["Balanced options", "Some stretch goals"],
                "Approach": ["Collaborative", "Guided"]
            },
            "70-100": {
                "Tone": ["Collaborative", "Empowering", "Challenging"],
                "Content": ["Ambitious goals", "Creative challenges"],
                "Approach": ["Advanced", "Independent"]
            }
        },
        "mood": {
            "valence": {
                "-1.0-0.0": {
                    "Tone": ["Gentle", "Understanding", "Patient"]
                },
                "0.0-1.0": {
                    "Tone": ["Enthusiastic", "Energetic", "Positive"]
                }
            }
        },
        "environment": {
            "stress_level": {
                "0-30": {
                    "Approach": ["Detailed", "Comprehensive"]
                },
                "31-70": {
                    "Approach": ["Balanced", "Focused"]
                },
                "71-100": {
                    "Approach": ["Concise", "Essential-only"]
                }
            }
        }
    };

    populateContextualCriteriaBuilder(sampleData);
    updateContextualCriteriaJSON();
}

function populateContextualCriteriaBuilder(data) {
    const builder = document.getElementById('contextual-criteria-builder');
    if (!builder) return;

    // Populate trust level
    if (data.trust_level) {
        Object.entries(data.trust_level).forEach(([range, criteria]) => {
            const rangeContainer = builder.querySelector(`[data-variable="trust_level"] [data-range="${range}"]`);
            if (rangeContainer) {
                populateRangeCriteria(rangeContainer, criteria);
            }
        });
    }

    // Populate mood valence
    if (data.mood && data.mood.valence) {
        Object.entries(data.mood.valence).forEach(([range, criteria]) => {
            const rangeContainer = builder.querySelector(`[data-variable="mood"] [data-subsection="valence"] [data-range="${range}"]`);
            if (rangeContainer) {
                populateRangeCriteria(rangeContainer, criteria);
            }
        });
    }

    // Populate environment stress level
    if (data.environment && data.environment.stress_level) {
        Object.entries(data.environment.stress_level).forEach(([range, criteria]) => {
            const rangeContainer = builder.querySelector(`[data-variable="environment"] [data-subsection="stress_level"] [data-range="${range}"]`);
            if (rangeContainer) {
                populateRangeCriteria(rangeContainer, criteria);
            }
        });
    }
}

function populateRangeCriteria(rangeContainer, criteria) {
    Object.entries(criteria).forEach(([dimension, values]) => {
        const input = rangeContainer.querySelector(`[data-dimension="${dimension}"]`);
        if (input && Array.isArray(values)) {
            input.value = values.join(', ');
        }
    });
}

function validateContextualCriteria() {
    const builder = document.getElementById('contextual-criteria-builder');
    if (!builder) return;

    const errors = [];
    const data = collectContextualCriteriaData();

    // Validate structure
    if (!data.trust_level && !data.mood && !data.environment) {
        errors.push('At least one contextual variable must be defined');
    }

    // Validate trust level ranges
    if (data.trust_level) {
        const ranges = Object.keys(data.trust_level);
        const expectedRanges = ['0-39', '40-69', '70-100'];
        const missingRanges = expectedRanges.filter(r => !ranges.includes(r));
        if (missingRanges.length > 0) {
            errors.push(`Missing trust level ranges: ${missingRanges.join(', ')}`);
        }
    }

    // Display validation results
    const validationStatus = builder.querySelector('.validation-status') || createValidationStatus(builder);
    if (errors.length > 0) {
        validationStatus.innerHTML = `<div class="alert alert-danger">Validation Errors:<ul>${errors.map(e => `<li>${e}</li>`).join('')}</ul></div>`;
    } else {
        validationStatus.innerHTML = '<div class="alert alert-success">Contextual criteria are valid!</div>';
    }
}

function createValidationStatus(builder) {
    const status = document.createElement('div');
    status.className = 'validation-status';
    builder.appendChild(status);
    return status;
}

function collectContextualCriteriaData() {
    const builder = document.getElementById('contextual-criteria-builder');
    if (!builder) return {};

    const data = {};

    // Collect trust level data
    const trustSection = builder.querySelector('[data-variable="trust_level"]');
    if (trustSection) {
        data.trust_level = {};
        trustSection.querySelectorAll('.range-def').forEach(rangeDef => {
            const range = rangeDef.dataset.range;
            const criteria = {};

            rangeDef.querySelectorAll('.criteria-input').forEach(input => {
                const dimension = input.dataset.dimension;
                const value = input.value.trim();
                if (value) {
                    criteria[dimension] = value.split(',').map(v => v.trim()).filter(v => v);
                }
            });

            if (Object.keys(criteria).length > 0) {
                data.trust_level[range] = criteria;
            }
        });
    }

    // Collect mood data
    const moodSection = builder.querySelector('[data-variable="mood"]');
    if (moodSection) {
        data.mood = {};

        const valenceSection = moodSection.querySelector('[data-subsection="valence"]');
        if (valenceSection) {
            data.mood.valence = {};
            valenceSection.querySelectorAll('.range-def').forEach(rangeDef => {
                const range = rangeDef.dataset.range;
                const criteria = {};

                rangeDef.querySelectorAll('.criteria-input').forEach(input => {
                    const dimension = input.dataset.dimension;
                    const value = input.value.trim();
                    if (value) {
                        criteria[dimension] = value.split(',').map(v => v.trim()).filter(v => v);
                    }
                });

                if (Object.keys(criteria).length > 0) {
                    data.mood.valence[range] = criteria;
                }
            });
        }
    }

    // Collect environment data
    const envSection = builder.querySelector('[data-variable="environment"]');
    if (envSection) {
        data.environment = {};

        const stressSection = envSection.querySelector('[data-subsection="stress_level"]');
        if (stressSection) {
            data.environment.stress_level = {};
            stressSection.querySelectorAll('.range-def').forEach(rangeDef => {
                const range = rangeDef.dataset.range;
                const criteria = {};

                rangeDef.querySelectorAll('.criteria-input').forEach(input => {
                    const dimension = input.dataset.dimension;
                    const value = input.value.trim();
                    if (value) {
                        criteria[dimension] = value.split(',').map(v => v.trim()).filter(v => v);
                    }
                });

                if (Object.keys(criteria).length > 0) {
                    data.environment.stress_level[range] = criteria;
                }
            });
        }
    }

    return data;
}

function updateContextualCriteriaJSON() {
    const data = collectContextualCriteriaData();
    const textarea = document.getElementById('template-contextual-criteria');
    if (textarea) {
        textarea.value = JSON.stringify(data, null, 2);
    }
}

// Helper functions for variable ranges builder

function loadDefaultVariableRanges() {
    const defaultRanges = {
        "trust_level": {
            "min": 0,
            "max": 100,
            "description": "User's trust level in the system"
        },
        "mood": {
            "valence": {
                "min": -1.0,
                "max": 1.0,
                "description": "Emotional valence from negative to positive"
            },
            "arousal": {
                "min": -1.0,
                "max": 1.0,
                "description": "Emotional arousal from calm to excited"
            }
        },
        "environment": {
            "stress_level": {
                "min": 0,
                "max": 100,
                "description": "Environmental stress level"
            },
            "time_pressure": {
                "min": 0,
                "max": 100,
                "description": "Time pressure in the environment"
            }
        }
    };

    populateVariableRangesBuilder(defaultRanges);
    updateVariableRangesJSON();
}

function populateVariableRangesBuilder(data) {
    const builder = document.getElementById('variable-ranges-builder');
    if (!builder) return;

    // Populate trust level
    if (data.trust_level) {
        const trustSection = builder.querySelector('[data-variable="trust_level"]');
        if (trustSection) {
            trustSection.querySelector('.range-min').value = data.trust_level.min;
            trustSection.querySelector('.range-max').value = data.trust_level.max;
            trustSection.querySelector('.range-desc').value = data.trust_level.description || '';
        }
    }

    // Populate mood variables
    if (data.mood) {
        if (data.mood.valence) {
            const valenceSection = builder.querySelector('[data-variable="mood"] [data-sub="valence"]');
            if (valenceSection) {
                valenceSection.querySelector('.range-min').value = data.mood.valence.min;
                valenceSection.querySelector('.range-max').value = data.mood.valence.max;
                valenceSection.querySelector('.range-desc').value = data.mood.valence.description || '';
            }
        }
        if (data.mood.arousal) {
            const arousalSection = builder.querySelector('[data-variable="mood"] [data-sub="arousal"]');
            if (arousalSection) {
                arousalSection.querySelector('.range-min').value = data.mood.arousal.min;
                arousalSection.querySelector('.range-max').value = data.mood.arousal.max;
                arousalSection.querySelector('.range-desc').value = data.mood.arousal.description || '';
            }
        }
    }

    // Populate environment variables
    if (data.environment) {
        if (data.environment.stress_level) {
            const stressSection = builder.querySelector('[data-variable="environment"] [data-sub="stress_level"]');
            if (stressSection) {
                stressSection.querySelector('.range-min').value = data.environment.stress_level.min;
                stressSection.querySelector('.range-max').value = data.environment.stress_level.max;
                stressSection.querySelector('.range-desc').value = data.environment.stress_level.description || '';
            }
        }
        if (data.environment.time_pressure) {
            const timeSection = builder.querySelector('[data-variable="environment"] [data-sub="time_pressure"]');
            if (timeSection) {
                timeSection.querySelector('.range-min').value = data.environment.time_pressure.min;
                timeSection.querySelector('.range-max').value = data.environment.time_pressure.max;
                timeSection.querySelector('.range-desc').value = data.environment.time_pressure.description || '';
            }
        }
    }
}

function validateVariableRanges() {
    const builder = document.getElementById('variable-ranges-builder');
    if (!builder) return;

    const errors = [];
    const data = collectVariableRangesData();

    // Validate ranges
    Object.entries(data).forEach(([variable, config]) => {
        if (variable === 'mood' || variable === 'environment') {
            Object.entries(config).forEach(([subVar, subConfig]) => {
                if (subConfig.min >= subConfig.max) {
                    errors.push(`${variable}.${subVar}: min value must be less than max value`);
                }
            });
        } else {
            if (config.min >= config.max) {
                errors.push(`${variable}: min value must be less than max value`);
            }
        }
    });

    // Display validation results
    const validationStatus = builder.querySelector('.validation-status') || createValidationStatus(builder);
    if (errors.length > 0) {
        validationStatus.innerHTML = `<div class="alert alert-danger">Validation Errors:<ul>${errors.map(e => `<li>${e}</li>`).join('')}</ul></div>`;
    } else {
        validationStatus.innerHTML = '<div class="alert alert-success">Variable ranges are valid!</div>';
    }
}

function collectVariableRangesData() {
    const builder = document.getElementById('variable-ranges-builder');
    if (!builder) return {};

    const data = {};

    // Collect trust level
    const trustSection = builder.querySelector('[data-variable="trust_level"]');
    if (trustSection) {
        data.trust_level = {
            min: parseFloat(trustSection.querySelector('.range-min').value),
            max: parseFloat(trustSection.querySelector('.range-max').value),
            description: trustSection.querySelector('.range-desc').value
        };
    }

    // Collect mood variables
    data.mood = {};
    const valenceSection = builder.querySelector('[data-variable="mood"] [data-sub="valence"]');
    if (valenceSection) {
        data.mood.valence = {
            min: parseFloat(valenceSection.querySelector('.range-min').value),
            max: parseFloat(valenceSection.querySelector('.range-max').value),
            description: valenceSection.querySelector('.range-desc').value
        };
    }
    const arousalSection = builder.querySelector('[data-variable="mood"] [data-sub="arousal"]');
    if (arousalSection) {
        data.mood.arousal = {
            min: parseFloat(arousalSection.querySelector('.range-min').value),
            max: parseFloat(arousalSection.querySelector('.range-max').value),
            description: arousalSection.querySelector('.range-desc').value
        };
    }

    // Collect environment variables
    data.environment = {};
    const stressSection = builder.querySelector('[data-variable="environment"] [data-sub="stress_level"]');
    if (stressSection) {
        data.environment.stress_level = {
            min: parseFloat(stressSection.querySelector('.range-min').value),
            max: parseFloat(stressSection.querySelector('.range-max').value),
            description: stressSection.querySelector('.range-desc').value
        };
    }
    const timeSection = builder.querySelector('[data-variable="environment"] [data-sub="time_pressure"]');
    if (timeSection) {
        data.environment.time_pressure = {
            min: parseFloat(timeSection.querySelector('.range-min').value),
            max: parseFloat(timeSection.querySelector('.range-max').value),
            description: timeSection.querySelector('.range-desc').value
        };
    }

    return data;
}

function updateVariableRangesJSON() {
    const data = collectVariableRangesData();
    const textarea = document.getElementById('template-variable-ranges');
    if (textarea) {
        textarea.value = JSON.stringify(data, null, 2);
    }
}

// Helper functions for context preview

function updateContextPreview() {
    const context = getCurrentContextValues();
    const baseCriteria = getBaseCriteria();
    const contextualCriteria = getContextualCriteria();

    const adaptedCriteria = adaptCriteriaForContext(baseCriteria, contextualCriteria, context);
    displayAdaptedCriteria(adaptedCriteria, context);
}

function getCurrentContextValues() {
    return {
        trust_level: parseInt(document.getElementById('trust-level-slider')?.value || 50),
        mood: {
            valence: parseFloat(document.getElementById('valence-slider')?.value || 0),
            arousal: parseFloat(document.getElementById('arousal-slider')?.value || 0)
        },
        environment: {
            stress_level: parseInt(document.getElementById('stress-level-slider')?.value || 30),
            time_pressure: parseInt(document.getElementById('time-pressure-slider')?.value || 30)
        }
    };
}

function getBaseCriteria() {
    const criteriaTextarea = document.getElementById('template-criteria');
    if (!criteriaTextarea) return {};

    try {
        return JSON.parse(criteriaTextarea.value || '{}');
    } catch (e) {
        console.warn('Invalid base criteria JSON:', e);
        return {};
    }
}

function getContextualCriteria() {
    const contextualTextarea = document.getElementById('template-contextual-criteria');
    if (!contextualTextarea) return {};

    try {
        return JSON.parse(contextualTextarea.value || '{}');
    } catch (e) {
        console.warn('Invalid contextual criteria JSON:', e);
        return {};
    }
}

function adaptCriteriaForContext(baseCriteria, contextualCriteria, context) {
    const adapted = { ...baseCriteria };

    // Apply trust level adaptations
    if (contextualCriteria.trust_level && context.trust_level !== undefined) {
        const trustRange = getTrustLevelRange(context.trust_level);
        if (contextualCriteria.trust_level[trustRange]) {
            Object.entries(contextualCriteria.trust_level[trustRange]).forEach(([dimension, criteria]) => {
                if (!adapted[dimension]) adapted[dimension] = [];
                adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
            });
        }
    }

    // Apply mood valence adaptations
    if (contextualCriteria.mood?.valence && context.mood?.valence !== undefined) {
        const valenceRange = getValenceRange(context.mood.valence);
        if (contextualCriteria.mood.valence[valenceRange]) {
            Object.entries(contextualCriteria.mood.valence[valenceRange]).forEach(([dimension, criteria]) => {
                if (!adapted[dimension]) adapted[dimension] = [];
                adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
            });
        }
    }

    // Apply stress level adaptations
    if (contextualCriteria.environment?.stress_level && context.environment?.stress_level !== undefined) {
        const stressRange = getStressLevelRange(context.environment.stress_level);
        if (contextualCriteria.environment.stress_level[stressRange]) {
            Object.entries(contextualCriteria.environment.stress_level[stressRange]).forEach(([dimension, criteria]) => {
                if (!adapted[dimension]) adapted[dimension] = [];
                adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
            });
        }
    }

    return adapted;
}

function getTrustLevelRange(trustLevel) {
    if (trustLevel <= 39) return '0-39';
    if (trustLevel <= 69) return '40-69';
    return '70-100';
}

function getValenceRange(valence) {
    return valence < 0 ? '-1.0-0.0' : '0.0-1.0';
}

function getStressLevelRange(stressLevel) {
    if (stressLevel <= 30) return '0-30';
    if (stressLevel <= 70) return '31-70';
    return '71-100';
}

function displayAdaptedCriteria(adaptedCriteria, context) {
    const display = document.getElementById('adapted-criteria-display');
    if (!display) return;

    let html = `
        <div class="context-summary">
            <h6>Current Context:</h6>
            <p><strong>Trust Level:</strong> ${context.trust_level} (${getTrustLevelRange(context.trust_level)})</p>
            <p><strong>Mood Valence:</strong> ${context.mood.valence} (${getValenceRange(context.mood.valence)})</p>
            <p><strong>Stress Level:</strong> ${context.environment.stress_level} (${getStressLevelRange(context.environment.stress_level)})</p>
        </div>
        <div class="adapted-criteria">
            <h6>Adapted Criteria:</h6>
    `;

    if (Object.keys(adaptedCriteria).length === 0) {
        html += '<p class="text-muted">No criteria defined</p>';
    } else {
        Object.entries(adaptedCriteria).forEach(([dimension, criteria]) => {
            html += `
                <div class="criteria-dimension">
                    <strong>${dimension}:</strong>
                    <ul>
                        ${criteria.map(criterion => `<li>${criterion}</li>`).join('')}
                    </ul>
                </div>
            `;
        });
    }

    html += '</div>';
    display.innerHTML = html;
}

function loadPresetScenario(scenarioType) {
    const presets = {
        'new-user': {
            trust_level: 25,
            mood: { valence: -0.2, arousal: 0.3 },
            environment: { stress_level: 40, time_pressure: 20 }
        },
        'stressed-user': {
            trust_level: 60,
            mood: { valence: -0.5, arousal: 0.8 },
            environment: { stress_level: 85, time_pressure: 90 }
        },
        'confident-user': {
            trust_level: 85,
            mood: { valence: 0.7, arousal: 0.2 },
            environment: { stress_level: 15, time_pressure: 25 }
        },
        'low-mood': {
            trust_level: 45,
            mood: { valence: -0.8, arousal: -0.4 },
            environment: { stress_level: 60, time_pressure: 40 }
        }
    };

    const preset = presets[scenarioType];
    if (!preset) return;

    // Update sliders
    document.getElementById('trust-level-slider').value = preset.trust_level;
    document.getElementById('trust-level-value').textContent = preset.trust_level;

    document.getElementById('valence-slider').value = preset.mood.valence;
    document.getElementById('valence-value').textContent = preset.mood.valence;

    document.getElementById('arousal-slider').value = preset.mood.arousal;
    document.getElementById('arousal-value').textContent = preset.mood.arousal;

    document.getElementById('stress-level-slider').value = preset.environment.stress_level;
    document.getElementById('stress-level-value').textContent = preset.environment.stress_level;

    document.getElementById('time-pressure-slider').value = preset.environment.time_pressure;
    document.getElementById('time-pressure-value').textContent = preset.environment.time_pressure;

    // Update preview
    updateContextPreview();
}
function validateWorkflowType(workflowType) { console.log(`validateWorkflowType called for ${workflowType}`); }
function saveScenario(event) {
    event.preventDefault();
    console.log('saveScenario called');
    // Actual save logic would go here
    // For now, just close the modal
    const modal = document.getElementById('scenario-modal');
    if (modal) modal.style.display = 'none';
    loadScenarios(); // Reload scenarios after saving
}
// Template save functionality moved to template_modal_utils.js

// Template action functions
async function editTemplate(templateId) {
    console.log(`editTemplate called for ID: ${templateId} - attempting to show modal`);

    // Use the new modular evaluation template modal
    if (window.evaluationTemplateModal) {
        window.evaluationTemplateModal.show(templateId);
    } else {
        console.error('EvaluationTemplateModal not available');
        showError('Template editor not available. Please refresh the page.');
    }
}

function deleteTemplate(templateId) {
    if (confirm('Are you sure you want to delete this template?')) {
        console.log(`deleteTemplate called for ID: ${templateId}`);
        // Simulate delete operation
        showSuccess('Template deleted successfully');
        loadTemplates();
    }
}

function exportTemplate(templateId) {
    console.log(`exportTemplate called for ID: ${templateId}`);

    // Simulate template data
    const templateData = {
        name: "Sample Template",
        description: "Sample description",
        category: "quality",
        workflow_type: "wheel_generation",
        criteria: {
            "Content": ["Relevance", "Accuracy", "Completeness"],
            "Tone": ["Appropriate", "Supportive", "Professional"]
        },
        is_active: true,
        version: "1.0.0"
    };

    // Create and download JSON file
    const blob = new Blob([JSON.stringify(templateData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `template_${templateData.name.replace(/\s+/g, '_').toLowerCase()}.json`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }, 0);
}

// Template batch operations
function updateSelectedTemplatesCount() {
    const selectedCheckboxes = document.querySelectorAll('.template-checkbox:checked');
    const countSpan = document.getElementById('selected-templates-count');
    const batchOpsDiv = document.getElementById('template-batch-operations');

    if (countSpan) {
        countSpan.textContent = selectedCheckboxes.length;
    }
    if (batchOpsDiv) {
        if (selectedCheckboxes.length > 0) {
            batchOpsDiv.classList.remove('hidden');
        } else {
            batchOpsDiv.classList.add('hidden');
        }
    }
}

function batchActivateTemplates() {
    const selectedIds = Array.from(document.querySelectorAll('.template-checkbox:checked'))
        .map(cb => cb.getAttribute('data-id'));

    if (selectedIds.length === 0) {
        showError('No templates selected');
        return;
    }

    console.log('Batch activating templates:', selectedIds);
    showSuccess(`${selectedIds.length} templates activated`);
    loadTemplates();
}

function batchDeactivateTemplates() {
    const selectedIds = Array.from(document.querySelectorAll('.template-checkbox:checked'))
        .map(cb => cb.getAttribute('data-id'));

    if (selectedIds.length === 0) {
        showError('No templates selected');
        return;
    }

    console.log('Batch deactivating templates:', selectedIds);
    showSuccess(`${selectedIds.length} templates deactivated`);
    loadTemplates();
}

function batchDeleteTemplates() {
    const selectedIds = Array.from(document.querySelectorAll('.template-checkbox:checked'))
        .map(cb => cb.getAttribute('data-id'));

    if (selectedIds.length === 0) {
        showError('No templates selected');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selectedIds.length} templates?`)) {
        console.log('Batch deleting templates:', selectedIds);
        showSuccess(`${selectedIds.length} templates deleted`);
        loadTemplates();
    }
}

function batchExportTemplates() {
    const selectedIds = Array.from(document.querySelectorAll('.template-checkbox:checked'))
        .map(cb => cb.getAttribute('data-id'));

    if (selectedIds.length === 0) {
        showError('No templates selected');
        return;
    }

    console.log('Batch exporting templates:', selectedIds);

    // Simulate batch export
    const exportData = {
        templates: selectedIds.map(id => ({
            id: id,
            name: `Template ${id}`,
            category: "quality",
            criteria: { "Content": ["Relevance"] }
        })),
        exported_at: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `templates_export_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();

    setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }, 0);

    showSuccess(`${selectedIds.length} templates exported`);
}

// Template import functionality
function handleTemplateFileSelect(event) {
    const file = event.target.files[0];
    const importPreview = document.getElementById('template-import-preview');
    const importErrorMsg = document.getElementById('import-template-error-message');

    if (!importPreview || !importErrorMsg) return;

    if (!file) {
        importPreview.textContent = 'No file selected';
        importErrorMsg.innerHTML = '';
        importErrorMsg.classList.add('hidden');
        return;
    }

    // Clear previous validation errors
    importErrorMsg.innerHTML = '';
    importErrorMsg.classList.add('hidden');

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const content = e.target.result;
            const json = JSON.parse(content);

            // Format and display the JSON
            importPreview.textContent = JSON.stringify(json, null, 2);

            // Perform initial validation
            const validationErrors = validateEvaluationTemplate(json);

            if (validationErrors.length > 0) {
                // Display validation errors
                const errorList = document.createElement('ul');
                validationErrors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });

                importErrorMsg.innerHTML = '<strong>Validation Errors:</strong>';
                importErrorMsg.appendChild(errorList);
                importErrorMsg.classList.remove('hidden');
            } else {
                // Show success message for valid JSON
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success';
                successMessage.textContent = 'JSON is valid according to the evaluation template schema. Click Import to save it.';

                importErrorMsg.innerHTML = '';
                importErrorMsg.appendChild(successMessage);
                importErrorMsg.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Error parsing JSON:', error);

            importErrorMsg.innerHTML = `<strong>JSON Parse Error:</strong> ${error.message}`;
            importErrorMsg.classList.remove('hidden');

            // Still show the raw content
            importPreview.textContent = e.target.result;
        }
    };
    reader.readAsText(file);
}

async function importTemplate(event) {
    event.preventDefault();

    const fileInput = document.getElementById('template-file');
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        showError('Please select a file to import', 'import-template-error-message');
        return;
    }

    const file = fileInput.files[0];
    const reader = new FileReader();
    const importErrorMsg = document.getElementById('import-template-error-message');

    reader.onload = async function(e) {
        try {
            const content = e.target.result;
            const templateData = JSON.parse(content);

            // Clear previous validation errors
            if (importErrorMsg) {
                importErrorMsg.innerHTML = '';
                importErrorMsg.classList.add('hidden');
            }

            // Client-side validation
            const validationErrors = validateEvaluationTemplate(templateData);

            if (validationErrors.length > 0) {
                // Display validation errors
                const errorList = document.createElement('ul');
                validationErrors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });
                if (importErrorMsg) {
                    importErrorMsg.innerHTML = '<strong>Validation Errors:</strong>';
                    importErrorMsg.appendChild(errorList);
                    importErrorMsg.classList.remove('hidden');
                }
                return;
            }

            // Simulate server save (in real implementation, this would be an API call)
            console.log('Importing template:', templateData);

            // Close modal and show success message
            const importModal = document.getElementById('import-template-modal');
            if (importModal) importModal.style.display = 'none';
            showSuccess('Template imported successfully');

            // Reload templates list
            loadTemplates();

        } catch (error) {
            console.error('Error importing template:', error);
            showError(`Error importing template: ${error.message}`, 'import-template-error-message');
        }
    };

    reader.readAsText(file);
}

function validateEvaluationTemplate(templateData) {
    const errors = [];

    // Required top-level fields
    if (!templateData.name) {
        errors.push('Template name is required');
    } else if (typeof templateData.name !== 'string') {
        errors.push('Template name must be a string');
    }

    if (!templateData.category) {
        errors.push('Template category is required');
    } else if (typeof templateData.category !== 'string') {
        errors.push('Template category must be a string');
    } else if (!['semantic', 'quality', 'phase', 'custom'].includes(templateData.category)) {
        errors.push('Template category must be one of: semantic, quality, phase, custom');
    }

    if (!templateData.criteria) {
        errors.push('Template criteria is required');
    } else if (typeof templateData.criteria !== 'object' || templateData.criteria === null) {
        errors.push('Template criteria must be an object');
    } else {
        // Validate criteria structure based on category
        if (templateData.category === 'phase') {
            // Phase-based templates should have foundation, expansion, integration
            const phases = ['foundation', 'expansion', 'integration'];
            const hasPhases = phases.some(phase => templateData.criteria[phase]);

            if (!hasPhases) {
                errors.push('Phase-based templates should include at least one of: foundation, expansion, integration');
            }
        } else {
            // Other templates should have criteria as key-value pairs
            const criteriaKeys = Object.keys(templateData.criteria);
            if (criteriaKeys.length === 0) {
                errors.push('Template criteria cannot be empty');
            }
        }
    }

    // Optional fields validation
    if (templateData.description !== undefined && typeof templateData.description !== 'string') {
        errors.push('Description must be a string');
    }

    if (templateData.workflow_type !== undefined && typeof templateData.workflow_type !== 'string') {
        errors.push('Workflow type must be a string');
    }

    if (templateData.is_active !== undefined && typeof templateData.is_active !== 'boolean') {
        errors.push('is_active must be a boolean');
    }

    if (templateData.version !== undefined && typeof templateData.version !== 'string') {
        errors.push('Version must be a string');
    }

    return errors;
}
function editScenario(scenarioId) {
    console.log(`editScenario called for ID: ${scenarioId} - attempting to show modal`);

    // Function to show the modal
    function showModal() {
        if (window.scenarioEditingModal) {
            window.scenarioEditingModal.show(scenarioId);
        } else {
            console.error('ScenarioEditingModal not available');
            showError('Scenario editor not available. Please refresh the page.');
        }
    }

    // Check if modal is already available
    if (window.scenarioEditingModal) {
        showModal();
        return;
    }

    // If not available, try to initialize it
    if (window.ScenarioEditingModal && !window.scenarioEditingModal) {
        console.log('Initializing ScenarioEditingModal...');
        window.scenarioEditingModal = new window.ScenarioEditingModal();
        showModal();
        return;
    }

    // If still not available, wait for DOM content loaded
    if (document.readyState === 'loading') {
        console.log('DOM still loading, waiting for DOMContentLoaded...');
        document.addEventListener('DOMContentLoaded', function() {
            // Try again after DOM is loaded
            setTimeout(() => {
                if (window.scenarioEditingModal) {
                    window.scenarioEditingModal.show(scenarioId);
                } else if (window.ScenarioEditingModal) {
                    window.scenarioEditingModal = new window.ScenarioEditingModal();
                    window.scenarioEditingModal.show(scenarioId);
                } else if (window.initializeScenarioEditingModal) {
                    // Try manual initialization
                    window.initializeScenarioEditingModal();
                    if (window.scenarioEditingModal) {
                        window.scenarioEditingModal.show(scenarioId);
                    } else {
                        console.error('ScenarioEditingModal still not available after manual initialization');
                        showError('Scenario editor not available. Please refresh the page.');
                    }
                } else {
                    console.error('ScenarioEditingModal still not available after DOM loaded');
                    showError('Scenario editor not available. Please refresh the page.');
                }
            }, 100); // Small delay to ensure initialization
        });
    } else {
        // DOM is already loaded but modal is not available
        // Try manual initialization as last resort
        if (window.initializeScenarioEditingModal) {
            console.log('Attempting manual initialization...');
            window.initializeScenarioEditingModal();
            if (window.scenarioEditingModal) {
                window.scenarioEditingModal.show(scenarioId);
                return;
            }
        }

        console.error('DOM loaded but ScenarioEditingModal not available');
        showError('Scenario editor not available. Please refresh the page.');
    }
}
function deleteScenario(scenarioId) { console.log(`deleteScenario called for ID: ${scenarioId}`); /* Implement actual delete logic */ }
function validateScenario(scenarioId) { console.log(`validateScenario called for ID: ${scenarioId}`); /* Implement actual validation logic */ }
function updateSelectedScenariosCount() {
    const selectedCheckboxes = document.querySelectorAll('.scenario-checkbox:checked');
    const countSpan = document.getElementById('selected-scenarios-count');
    const batchOpsDiv = document.getElementById('batch-operations');

    if (countSpan) {
        countSpan.textContent = selectedCheckboxes.length;
    }
    if (batchOpsDiv) {
        if (selectedCheckboxes.length > 0) {
            batchOpsDiv.classList.remove('hidden');
        } else {
            batchOpsDiv.classList.add('hidden');
        }
    }
}


// Initialize the page when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Benchmark management page loaded');

    // Tab navigation
    const tabLinks = document.querySelectorAll('.nav-tabs .nav-link');
    const tabPanes = document.querySelectorAll('.tab-content .tab-pane');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all tabs and panes
            tabLinks.forEach(l => l.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            const target = this.getAttribute('href');
            if (target && target.startsWith('#')) {
                const targetPane = document.getElementById(target.substring(1));
                if (targetPane) {
                    targetPane.classList.add('active');
                }

                // Load data for the specific tab
                const tabName = target.substring(1);
                if (tabName === 'benchmark-runs') {
                    loadBenchmarkRuns();
                }
            }
        });
    });

    // Load initial data
    loadScenarios();
    loadTemplates();
    loadValidationScenarios();
    loadWorkflowTypeStats();
    loadUserProfiles();


    // Button event listeners
    const applyFiltersBtn = document.getElementById('apply-filters-btn');
    if (applyFiltersBtn) applyFiltersBtn.addEventListener('click', loadScenarios);

    const resetFiltersBtn = document.getElementById('reset-filters-btn');
    if (resetFiltersBtn) resetFiltersBtn.addEventListener('click', resetFilters);

    const createScenarioBtn = document.getElementById('create-scenario-btn');
    if (createScenarioBtn) createScenarioBtn.addEventListener('click', showCreateScenarioModal);

    const importScenarioBtn = document.getElementById('import-scenario-btn');
    if (importScenarioBtn) importScenarioBtn.addEventListener('click', showImportScenarioModal);

    const createTemplateBtn = document.getElementById('create-template-btn');
    if (createTemplateBtn) createTemplateBtn.addEventListener('click', showCreateTemplateModal);

    const importTemplateBtn = document.getElementById('import-template-btn');
    if (importTemplateBtn) importTemplateBtn.addEventListener('click', showImportTemplateModal);

    // Template filter buttons
    const applyTemplateFiltersBtn = document.getElementById('apply-template-filters-btn');
    if (applyTemplateFiltersBtn) applyTemplateFiltersBtn.addEventListener('click', loadTemplates);

    const resetTemplateFiltersBtn = document.getElementById('reset-template-filters-btn');
    if (resetTemplateFiltersBtn) resetTemplateFiltersBtn.addEventListener('click', resetTemplateFilters);

    // Benchmark runs filter buttons
    const applyRunsFiltersBtn = document.getElementById('apply-runs-filters-btn');
    if (applyRunsFiltersBtn) applyRunsFiltersBtn.addEventListener('click', loadBenchmarkRuns);

    const resetRunsFiltersBtn = document.getElementById('reset-runs-filters-btn');
    if (resetRunsFiltersBtn) resetRunsFiltersBtn.addEventListener('click', resetRunsFilters);

    // User Profile Management buttons
    const createProfileBtn = document.getElementById('create-profile-btn');
    if (createProfileBtn) createProfileBtn.addEventListener('click', createUserProfile);

    const applyProfileFiltersBtn = document.getElementById('apply-profile-filters-btn');
    if (applyProfileFiltersBtn) applyProfileFiltersBtn.addEventListener('click', loadUserProfiles);

    const resetProfileFiltersBtn = document.getElementById('reset-profile-filters-btn');
    if (resetProfileFiltersBtn) resetProfileFiltersBtn.addEventListener('click', function() {
        // Reset profile filters
        const trustPhaseFilter = document.getElementById('profile-trust-phase-filter');
        const archetypeFilter = document.getElementById('profile-archetype-filter');
        const statusFilter = document.getElementById('profile-status-filter');

        if (trustPhaseFilter) trustPhaseFilter.value = '';
        if (archetypeFilter) archetypeFilter.value = '';
        if (statusFilter) statusFilter.value = '';

        loadUserProfiles();
    });

    // Template batch operation buttons
    const batchActivateTemplatesBtn = document.getElementById('batch-activate-templates-btn');
    if (batchActivateTemplatesBtn) batchActivateTemplatesBtn.addEventListener('click', batchActivateTemplates);

    const batchDeactivateTemplatesBtn = document.getElementById('batch-deactivate-templates-btn');
    if (batchDeactivateTemplatesBtn) batchDeactivateTemplatesBtn.addEventListener('click', batchDeactivateTemplates);

    const batchDeleteTemplatesBtn = document.getElementById('batch-delete-templates-btn');
    if (batchDeleteTemplatesBtn) batchDeleteTemplatesBtn.addEventListener('click', batchDeleteTemplates);

    const batchExportTemplatesBtn = document.getElementById('batch-export-templates-btn');
    if (batchExportTemplatesBtn) batchExportTemplatesBtn.addEventListener('click', batchExportTemplates);

    const validateSelectedBtn = document.getElementById('validate-selected-btn');
    if (validateSelectedBtn) validateSelectedBtn.addEventListener('click', validateSelectedScenarios);

    const validateAllBtn = document.getElementById('validate-all-btn');
    if (validateAllBtn) validateAllBtn.addEventListener('click', validateAllScenarios);


    // Add event listeners for batch operations
    const batchValidateBtn = document.getElementById('batch-validate-btn');
    if (batchValidateBtn) batchValidateBtn.addEventListener('click', batchValidateScenarios);

    const batchActivateBtn = document.getElementById('batch-activate-btn');
    if (batchActivateBtn) batchActivateBtn.addEventListener('click', batchActivateScenarios);

    const batchDeactivateBtn = document.getElementById('batch-deactivate-btn');
    if (batchDeactivateBtn) batchDeactivateBtn.addEventListener('click', batchDeactivateScenarios);

    const batchAddTagBtn = document.getElementById('batch-add-tag-btn');
    if (batchAddTagBtn) batchAddTagBtn.addEventListener('click', batchAddTagToScenarios);


    // Add event listeners for workflow type buttons
    document.querySelectorAll('.view-scenarios-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const workflowType = this.getAttribute('data-workflow-type');
            viewScenariosForWorkflowType(workflowType);
        });
    });

    document.querySelectorAll('.validate-workflow-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const workflowType = this.getAttribute('data-workflow-type');
            validateWorkflowType(workflowType);
        });
    });

    // Back button for workflow type scenarios
    const backToWorkflowTypesBtn = document.getElementById('back-to-workflow-types-btn');
    if (backToWorkflowTypesBtn) {
        backToWorkflowTypesBtn.addEventListener('click', function() {
            const wfScenariosContainer = document.getElementById('workflow-type-scenarios-container');
            const wfTableContainer = document.getElementById('workflow-types-table-container');
            if (wfScenariosContainer) wfScenariosContainer.classList.add('hidden');
            if (wfTableContainer) wfTableContainer.classList.remove('hidden');
        });
    }


    // Close buttons for modals
    document.querySelectorAll('.close, .close-modal').forEach(el => {
        el.addEventListener('click', function() {
            const scenarioModal = document.getElementById('scenario-modal');
            const templateModal = document.getElementById('template-modal');
            const importModal = document.getElementById('import-scenario-modal');
            const importTemplateModal = document.getElementById('import-template-modal');

            if (scenarioModal) scenarioModal.style.display = 'none';
            if (templateModal) templateModal.style.display = 'none';
            if (importModal) importModal.style.display = 'none';
            if (importTemplateModal) importTemplateModal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const scenarioModal = document.getElementById('scenario-modal');
        const templateModal = document.getElementById('template-modal');
        const importModal = document.getElementById('import-scenario-modal');
        const importTemplateModal = document.getElementById('import-template-modal');

        if (event.target === scenarioModal) {
            scenarioModal.style.display = 'none';
        }
        if (event.target === templateModal) {
            templateModal.style.display = 'none';
        }
        if (event.target === importModal) {
            importModal.style.display = 'none';
        }
        if (event.target === importTemplateModal) {
            importTemplateModal.style.display = 'none';
        }
    });

    // Form submissions
    const scenarioForm = document.getElementById('scenario-form');
    if (scenarioForm) scenarioForm.addEventListener('submit', saveScenario);

    const templateForm = document.getElementById('template-form');
    // Template save functionality is now handled by evaluation_template_modal.js
    // if (templateForm) templateForm.addEventListener('submit', saveTemplate);

    const importScenarioForm = document.getElementById('import-scenario-form');
    if (importScenarioForm) importScenarioForm.addEventListener('submit', importScenario);

    const importTemplateForm = document.getElementById('import-template-form');
    if (importTemplateForm) importTemplateForm.addEventListener('submit', importTemplate);

    // Scenario generate sample button
    const scenarioGenerateSampleBtn = document.getElementById('scenario-generate-sample-btn');
    if (scenarioGenerateSampleBtn) {
        scenarioGenerateSampleBtn.addEventListener('click', function() {
            if (window.scenarioEditingModal) {
                window.scenarioEditingModal.generateSampleData();
            }
        });
    }

    // Initialize modals
    if (window.initializeEvaluationTemplateModal) {
        window.initializeEvaluationTemplateModal();
    }

    if (window.initializeScenarioEditingModal) {
        window.initializeScenarioEditingModal();
    }

    if (window.initializeUserProfileModal) {
        window.initializeUserProfileModal();
    }


    // Initialize JSON editors
    const jsonEditors = document.querySelectorAll('.json-editor');
    jsonEditors.forEach(editor => {
        // Add toolbar
        const toolbar = document.createElement('div');
        toolbar.className = 'json-editor-toolbar';
        toolbar.style.marginBottom = '5px'; // Add some space below toolbar

        const formatBtn = document.createElement('button');
        formatBtn.className = 'btn btn-sm btn-secondary json-format-btn';
        formatBtn.textContent = 'Format JSON';
        formatBtn.type = 'button';
        formatBtn.style.marginRight = '5px';


        const validateBtn = document.createElement('button');
        validateBtn.className = 'btn btn-sm btn-info json-validate-btn';
        validateBtn.textContent = 'Validate JSON';
        validateBtn.type = 'button';
        validateBtn.style.marginRight = '5px';

        const validationStatus = document.createElement('span');
        validationStatus.className = 'json-validation-status';
        validationStatus.style.marginLeft = '10px';


        // Add buttons to toolbar
        toolbar.appendChild(formatBtn);
        toolbar.appendChild(validateBtn);
        toolbar.appendChild(validationStatus);

        // Insert toolbar before the editor
        editor.parentNode.insertBefore(toolbar, editor);

        // Format JSON button click handler
        formatBtn.addEventListener('click', function() {
            try {
                const json = JSON.parse(editor.value);
                editor.value = JSON.stringify(json, null, 2);
                validationStatus.textContent = 'Formatted. Valid JSON.';
                validationStatus.style.color = 'green';
            } catch (e) {
                validationStatus.textContent = 'Invalid JSON: ' + e.message;
                validationStatus.style.color = 'red';
            }
        });

        // Validate JSON button click handler
        validateBtn.addEventListener('click', function() {
            try {
                JSON.parse(editor.value);
                validationStatus.textContent = 'Valid JSON';
                validationStatus.style.color = 'green';
            } catch (e) {
                validationStatus.textContent = 'Invalid JSON: ' + e.message;
                validationStatus.style.color = 'red';
            }
        });
    });

    // Import Scenario functions
    function showImportScenarioModal() {
        const importForm = document.getElementById('import-scenario-form');
        if (importForm) importForm.reset();

        const importPreview = document.getElementById('import-preview');
        if (importPreview) importPreview.textContent = 'No file selected';

        // Clear previous validation errors
        const importErrorMsg = document.getElementById('import-scenario-error-message');
        if (importErrorMsg) {
            importErrorMsg.innerHTML = '';
            importErrorMsg.classList.add('hidden');
        }

        // Show modal
        const importModal = document.getElementById('import-scenario-modal');
        if (importModal) importModal.style.display = 'block';
    }

    /**
     * Generates a template JSON for a new benchmark scenario.
     * This helps users create valid scenarios more easily.
     *
     * @param {string} workflowType - The workflow type for the scenario
     * @returns {string} - JSON string for the template
     */
    function generateScenarioTemplate(workflowType = '') {
        // Get available workflow types from the page
        const workflowTypeSelect = document.getElementById('scenario-workflow-type'); // or 'template-workflow-type'
        const availableWorkflowTypes = Array.from(workflowTypeSelect?.options || [])
            .map(option => option.value)
            .filter(value => value !== '');

        // Use the first available workflow type if none specified
        if (!workflowType && availableWorkflowTypes.length > 0) {
            workflowType = availableWorkflowTypes[0];
        }

        // Create a template scenario
        const template = {
            "name": `New ${workflowType || 'Workflow'} Scenario`,
            "description": `A benchmark scenario for ${workflowType || 'workflow'} testing`,
            "agent_role": "mentor", // Default role
            "input_data": {
                "user_message": "Example user message",
                "context_packet": {
                    "workflow_type": workflowType,
                    "trust_level": 50
                }
            },
            "metadata": {
                "workflow_type": workflowType,
                "user_profile_context": {
                    "trust_level": 50,
                    "preferences": {
                        "activity_types": ["outdoor", "creative"]
                    }
                },
                "expected_quality_criteria": {
                    "Content": ["Should be relevant and helpful"],
                    "Tone": ["Should be supportive and encouraging"]
                },
                "mock_tool_responses": {
                    "get_user_profile": {
                        "response": "{ \"id\": \"123\", \"name\": \"Test User\" }"
                    }
                },
                "warmup_runs": 1,
                "benchmark_runs": 3
            },
            "is_active": true
        };

        return JSON.stringify(template, null, 2);
    }

    function handleFileSelect(event) {
        const file = event.target.files[0];
        const importPreview = document.getElementById('import-preview');
        const importErrorMsg = document.getElementById('import-scenario-error-message');

        if (!importPreview || !importErrorMsg) return;


        if (!file) {
            importPreview.textContent = 'No file selected';
            importErrorMsg.innerHTML = '';
            importErrorMsg.classList.add('hidden');
            return;
        }

        // Clear previous validation errors
        importErrorMsg.innerHTML = '';
        importErrorMsg.classList.add('hidden');

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const content = e.target.result;
                const json = JSON.parse(content);

                // Format and display the JSON
                importPreview.textContent = JSON.stringify(json, null, 2);

                // Perform initial validation
                const validationErrors = validateBenchmarkScenario(json);

                if (validationErrors.length > 0) {
                    // Display validation errors
                    const errorList = document.createElement('ul');
                    validationErrors.forEach(error => {
                        const li = document.createElement('li');
                        li.textContent = error;
                        errorList.appendChild(li);
                    });

                    importErrorMsg.innerHTML = '<strong>Validation Errors:</strong>';
                    importErrorMsg.appendChild(errorList);
                    importErrorMsg.classList.remove('hidden');
                } else {
                    // Show success message for valid JSON
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success';
                    successMessage.textContent = 'JSON is valid according to the BenchmarkScenario schema. Click Import to save it.';

                    importErrorMsg.innerHTML = '';
                    importErrorMsg.appendChild(successMessage);
                    importErrorMsg.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error parsing JSON:', error);

                importErrorMsg.innerHTML = `<strong>JSON Parse Error:</strong> ${error.message}`;
                importErrorMsg.classList.remove('hidden');

                // Still show the raw content
                importPreview.textContent = e.target.result;
            }
        };
        reader.readAsText(file);
    }

    async function importScenario(event) {
        event.preventDefault();

        const fileInput = document.getElementById('scenario-file');
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            showError('Please select a file to import', 'import-scenario-error-message');
            return;
        }

        const file = fileInput.files[0];
        const reader = new FileReader();
        const importErrorMsg = document.getElementById('import-scenario-error-message');


        reader.onload = async function(e) {
            try {
                const content = e.target.result;
                const scenarioData = JSON.parse(content);

                // Clear previous validation errors
                if (importErrorMsg) {
                    importErrorMsg.innerHTML = '';
                    importErrorMsg.classList.add('hidden');
                }


                // Client-side validation based on BenchmarkScenario Pydantic model
                const validationErrors = validateBenchmarkScenario(scenarioData);

                if (validationErrors.length > 0) {
                    // Display validation errors
                    const errorList = document.createElement('ul');
                    validationErrors.forEach(error => {
                        const li = document.createElement('li');
                        li.textContent = error;
                        errorList.appendChild(li);
                    });
                    if (importErrorMsg) {
                        importErrorMsg.innerHTML = '<strong>Validation Errors:</strong>';
                        importErrorMsg.appendChild(errorList);
                        importErrorMsg.classList.remove('hidden');
                    }
                    return;
                }

                // Send to server for full Pydantic validation
                const response = await fetch(window.BENCHMARK_SCENARIOS_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(scenarioData)
                });

                if (!response.ok) {
                    const errorData = await response.json();

                    // Handle structured validation errors from Pydantic
                    if (errorData.validation_errors) {
                        const errorList = document.createElement('ul');

                        // Process nested validation errors
                        const processErrors = (errors, path = '') => {
                            for (const [key, value] of Object.entries(errors)) {
                                const fieldPath = path ? `${path}.${key}` : key;

                                if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                                    // Nested object errors
                                    processErrors(value, fieldPath);
                                } else {
                                    // Field errors
                                    const messages = Array.isArray(value) ? value : [value];
                                    messages.forEach(msg => {
                                        const li = document.createElement('li');
                                        li.textContent = `${fieldPath}: ${msg}`;
                                        errorList.appendChild(li);
                                    });
                                }
                            }
                        };

                        processErrors(errorData.validation_errors);
                        if (importErrorMsg) {
                            importErrorMsg.innerHTML = '<strong>Server Validation Errors:</strong>';
                            importErrorMsg.appendChild(errorList);
                            importErrorMsg.classList.remove('hidden');
                        }

                    } else {
                        throw new Error(errorData.error || `HTTP error ${response.status}`);
                    }
                    return;
                }

                const result = await response.json();

                // Close modal and show success message
                const importModal = document.getElementById('import-scenario-modal');
                if (importModal) importModal.style.display = 'none';
                showSuccess(result.message || 'Scenario imported successfully');

                // Reload scenarios list
                loadScenarios();

            } catch (error) {
                console.error('Error importing scenario:', error);
                showError(`Error importing scenario: ${error.message}`, 'import-scenario-error-message');
            }
        };

        reader.readAsText(file);
    }

    /**
     * Validates a benchmark scenario against the BenchmarkScenario Pydantic model structure.
     * This is a client-side validation to provide immediate feedback before sending to the server.
     *
     * @param {Object} scenarioData - The scenario data to validate
     * @returns {Array} - Array of validation error messages
     */
    function validateBenchmarkScenario(scenarioData) {
        const errors = [];

        // Required top-level fields
        if (!scenarioData.name) {
            errors.push('Scenario name is required');
        } else if (typeof scenarioData.name !== 'string') {
            errors.push('Scenario name must be a string');
        }

        if (!scenarioData.agent_role) {
            errors.push('Agent role is required');
        } else if (typeof scenarioData.agent_role !== 'string') {
            errors.push('Agent role must be a string');
        }

        if (!scenarioData.input_data) {
            errors.push('Input data is required');
        } else if (typeof scenarioData.input_data !== 'object' || scenarioData.input_data === null) {
            errors.push('Input data must be an object');
        }

        // Validate metadata
        if (!scenarioData.metadata) {
            errors.push('Metadata is required');
        } else {
            // Validate BenchmarkScenarioMetadata
            const metadata = scenarioData.metadata;

            if (!metadata.workflow_type) {
                errors.push('Workflow type is required in metadata');
            } else if (typeof metadata.workflow_type !== 'string') {
                errors.push('Workflow type must be a string');
            }

            // Validate tool expectations if present
            if (metadata.tool_expectations) {
                if (!Array.isArray(metadata.tool_expectations)) {
                    errors.push('Tool expectations must be an array');
                } else {
                    metadata.tool_expectations.forEach((toolExp, index) => {
                        if (!toolExp.tool_name) {
                            errors.push(`Tool expectation at index ${index} is missing tool_name`);
                        }
                    });
                }
            }

            // Validate mock_tool_responses if present
            if (metadata.mock_tool_responses) {
                if (typeof metadata.mock_tool_responses !== 'object' || metadata.mock_tool_responses === null) {
                    errors.push('mock_tool_responses must be an object');
                } else {
                    // Check structure of each tool response
                    for (const [toolName, response] of Object.entries(metadata.mock_tool_responses)) {
                        if (typeof response === 'object' && response !== null && !Array.isArray(response)) {
                            // If it's a structured response, it should have a 'response' field or conditional fields
                            if (!response.response && !Object.keys(response).some(k => k.startsWith('condition'))) {
                                errors.push(`Tool response for ${toolName} is missing 'response' field`);
                            }
                        } else if (typeof response !== 'string' && !Array.isArray(response)) {
                            errors.push(`Tool response for ${toolName} has invalid type: ${typeof response}`);
                        }
                    }
                }
            }

            // Validate evaluation criteria if present
            if (metadata.evaluation_criteria) {
                if (typeof metadata.evaluation_criteria !== 'object' || metadata.evaluation_criteria === null) {
                    errors.push('evaluation_criteria must be an object');
                }
            }

            // Validate phase-aware criteria if present
            if (metadata.evaluation_criteria_by_phase) {
                const phases = ['foundation', 'expansion', 'integration'];
                const phaseData = metadata.evaluation_criteria_by_phase;

                if (typeof phaseData !== 'object' || phaseData === null) {
                    errors.push('evaluation_criteria_by_phase must be an object');
                } else {
                    // Check that at least one phase is defined
                    if (!phases.some(phase => phaseData[phase])) {
                        errors.push('evaluation_criteria_by_phase must define at least one phase (foundation, expansion, or integration)');
                    }

                    // Check structure of each phase
                    phases.forEach(phase => {
                        if (phaseData[phase] && (typeof phaseData[phase] !== 'object' || phaseData[phase] === null)) {
                            errors.push(`Phase ${phase} in evaluation_criteria_by_phase must be an object`);
                        }
                    });
                }
            }
        }

        // Optional fields validation
        if (scenarioData.description !== undefined && typeof scenarioData.description !== 'string') {
            errors.push('Description must be a string');
        }

        if (scenarioData.is_active !== undefined && typeof scenarioData.is_active !== 'boolean') {
            errors.push('is_active must be a boolean');
        }

        if (scenarioData.version !== undefined && typeof scenarioData.version !== 'string') {
            errors.push('Version must be a string');
        }

        return errors;
    }

    // Add event listener for file input change
    const scenarioFileInput = document.getElementById('scenario-file');
    if (scenarioFileInput) {
        scenarioFileInput.addEventListener('change', handleFileSelect);
    }

    // Add event listener for template file input change
    const templateFileInput = document.getElementById('template-file');
    if (templateFileInput) {
        templateFileInput.addEventListener('change', handleTemplateFileSelect);
    }


    // Add event listener for generate template button
    const generateTemplateBtn = document.getElementById('generate-template-btn');
    if (generateTemplateBtn) {
        generateTemplateBtn.addEventListener('click', function() {
            const workflowTypeSelect = document.getElementById('template-workflow-type');
            const workflowType = workflowTypeSelect ? workflowTypeSelect.value : '';
            const templateJson = generateScenarioTemplate(workflowType);

            const importPreview = document.getElementById('import-preview');
            if (importPreview) importPreview.textContent = templateJson;

            // Clear any previous error messages
            const importErrorMsg = document.getElementById('import-scenario-error-message');
            if (importErrorMsg) importErrorMsg.innerHTML = '';


            // Show success message with download button
            const successMessage = document.createElement('div');
            successMessage.className = 'alert alert-success';

            // Create download button
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'btn btn-sm btn-primary';
            downloadBtn.style.marginLeft = '10px';
            downloadBtn.style.float = 'right';
            downloadBtn.textContent = 'Download Template';
            downloadBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Create a blob and download link
                const blob = new Blob([templateJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `benchmark_scenario_${workflowType || 'template'}.json`;
                document.body.appendChild(a);
                a.click();

                // Clean up
                setTimeout(function() {
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }, 0);
            });

            successMessage.textContent = 'Template generated successfully. You can edit it and then click Import to save.';
            successMessage.appendChild(downloadBtn);

            if (importErrorMsg) {
                importErrorMsg.innerHTML = '';
                importErrorMsg.appendChild(successMessage);
                importErrorMsg.classList.remove('hidden');
            }
        });
    }

    // Template generation and import functions
    function generateEvaluationTemplate(category = 'quality') {
        const templates = {
            semantic: {
                name: "Semantic Evaluation Template",
                description: "Template for semantic analysis and content evaluation",
                category: "semantic",
                criteria: {
                    "Semantic_Accuracy": [
                        "Factual correctness",
                        "Logical consistency",
                        "Contextual appropriateness"
                    ],
                    "Content_Quality": [
                        "Relevance to user query",
                        "Completeness of response",
                        "Clarity of expression"
                    ],
                    "Language_Use": [
                        "Grammar and syntax",
                        "Vocabulary appropriateness",
                        "Tone consistency"
                    ]
                }
            },
            quality: {
                name: "Quality Assessment Template",
                description: "General quality evaluation template",
                category: "quality",
                criteria: {
                    "Content": [
                        "Relevance",
                        "Accuracy",
                        "Completeness",
                        "Depth of analysis"
                    ],
                    "Tone": [
                        "Appropriate for context",
                        "Supportive and encouraging",
                        "Professional",
                        "Empathetic"
                    ],
                    "Structure": [
                        "Clear organization",
                        "Logical flow",
                        "Easy to follow",
                        "Well-formatted"
                    ]
                }
            },
            phase: {
                name: "Phase-based Evaluation Template",
                description: "Multi-phase evaluation template for complex workflows",
                category: "phase",
                criteria: {
                    "foundation": {
                        "Understanding": [
                            "Grasps user needs accurately",
                            "Identifies key context elements",
                            "Recognizes constraints and requirements"
                        ],
                        "Approach": [
                            "Systematic methodology",
                            "Thoughtful consideration",
                            "Appropriate scope"
                        ]
                    },
                    "expansion": {
                        "Creativity": [
                            "Novel and innovative ideas",
                            "Diverse range of options",
                            "Out-of-the-box thinking"
                        ],
                        "Feasibility": [
                            "Practical and achievable",
                            "Resource-conscious",
                            "Realistic timeline"
                        ]
                    },
                    "integration": {
                        "Synthesis": [
                            "Coherent final solution",
                            "Well-integrated components",
                            "Addresses all requirements"
                        ],
                        "Presentation": [
                            "Clear and compelling",
                            "Well-structured output",
                            "Actionable recommendations"
                        ]
                    }
                }
            },
            custom: {
                name: "Custom Evaluation Template",
                description: "Customizable template for specific evaluation needs",
                category: "custom",
                criteria: {
                    "Criterion_1": [
                        "Evaluation point 1",
                        "Evaluation point 2",
                        "Evaluation point 3"
                    ],
                    "Criterion_2": [
                        "Evaluation point 1",
                        "Evaluation point 2"
                    ],
                    "Criterion_3": [
                        "Evaluation point 1",
                        "Evaluation point 2",
                        "Evaluation point 3",
                        "Evaluation point 4"
                    ]
                }
            }
        };

        const template = templates[category] || templates.quality;
        return {
            ...template,
            is_active: true,
            version: "1.0.0",
            created_at: new Date().toISOString()
        };
    }

    // Add event listener for template generation in modal
    const templateGenerateSampleBtn = document.getElementById('template-generate-sample-btn');
    if (templateGenerateSampleBtn) {
        templateGenerateSampleBtn.addEventListener('click', function() {
            const categorySelect = document.getElementById('template-category-select');
            const category = categorySelect ? categorySelect.value : 'quality';

            if (!category) {
                showError('Please select a category first');
                return;
            }

            const template = generateEvaluationTemplate(category);
            const criteriaTextarea = document.getElementById('template-criteria');

            if (criteriaTextarea) {
                criteriaTextarea.value = JSON.stringify(template.criteria, null, 2);
            }

            // Also populate name and description if they're empty
            const nameInput = document.getElementById('template-name');
            const descInput = document.getElementById('template-description');

            if (nameInput && !nameInput.value) {
                nameInput.value = template.name;
            }
            if (descInput && !descInput.value) {
                descInput.value = template.description;
            }
        });
    }

    // Add event listener for template sample generation in import modal
    const generateTemplateSampleBtn = document.getElementById('generate-template-sample-btn');
    if (generateTemplateSampleBtn) {
        generateTemplateSampleBtn.addEventListener('click', function() {
            const categorySelect = document.getElementById('sample-template-category');
            const category = categorySelect ? categorySelect.value : 'quality';

            const template = generateEvaluationTemplate(category);
            const templateJson = JSON.stringify(template, null, 2);

            const importPreview = document.getElementById('template-import-preview');
            if (importPreview) importPreview.textContent = templateJson;

            // Clear any previous error messages
            const importErrorMsg = document.getElementById('import-template-error-message');
            if (importErrorMsg) importErrorMsg.innerHTML = '';

            // Show success message with download button
            const successMessage = document.createElement('div');
            successMessage.className = 'alert alert-success';

            // Create download button
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'btn btn-sm btn-primary';
            downloadBtn.style.marginLeft = '10px';
            downloadBtn.style.float = 'right';
            downloadBtn.textContent = 'Download Template';
            downloadBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Create a blob and download link
                const blob = new Blob([templateJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `evaluation_template_${category}.json`;
                document.body.appendChild(a);
                a.click();

                // Clean up
                setTimeout(function() {
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }, 0);
            });

            successMessage.textContent = 'Template generated successfully. You can edit it and then click Import to save.';
            successMessage.appendChild(downloadBtn);

            if (importErrorMsg) {
                importErrorMsg.innerHTML = '';
                importErrorMsg.appendChild(successMessage);
                importErrorMsg.classList.remove('hidden');
            }
        });
    }
});

// Benchmark Runs functions
async function loadBenchmarkRuns() {
    const runsTable = document.getElementById('benchmark-runs-table');
    const runsLoading = document.getElementById('benchmark-runs-loading');
    const runsEmpty = document.getElementById('benchmark-runs-empty');

    if (!runsTable || !runsLoading || !runsEmpty) {
        console.warn('Benchmark runs table elements not found. Skipping loadBenchmarkRuns.');
        return;
    }
    const tableBody = runsTable.querySelector('tbody');
    if (!tableBody) {
        console.warn('Benchmark runs table body not found. Skipping loadBenchmarkRuns.');
        return;
    }

    // Show loading, hide table and empty message
    runsTable.classList.add('hidden');
    runsEmpty.classList.add('hidden');
    runsLoading.classList.remove('hidden');

    // Get filter values
    const scenarioId = document.getElementById('runs-scenario-filter')?.value || '';
    const agentRole = document.getElementById('runs-agent-role-filter')?.value || '';
    const success = document.getElementById('runs-success-filter')?.value || '';
    const startDate = document.getElementById('runs-start-date')?.value || '';
    const endDate = document.getElementById('runs-end-date')?.value || '';

    // Build query string
    let queryParams = [];
    if (scenarioId) queryParams.push(`scenario_id=${encodeURIComponent(scenarioId)}`);
    if (agentRole) queryParams.push(`agent_role=${encodeURIComponent(agentRole)}`);
    if (success) queryParams.push(`success=${encodeURIComponent(success)}`);
    if (startDate) queryParams.push(`start_date=${encodeURIComponent(startDate)}`);
    if (endDate) queryParams.push(`end_date=${encodeURIComponent(endDate)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

    try {
        // Use the benchmark runs API endpoint
        const response = await fetch(`/admin/benchmarks/api/run/${queryString}`);

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const data = await response.json();

        // Clear table body
        tableBody.innerHTML = '';

        if (data.runs && data.runs.length > 0) {
            // Populate table
            data.runs.forEach(run => {
                const row = document.createElement('tr');

                // Format date
                const executionDate = new Date(run.execution_date).toLocaleString();

                // Format success rate
                const successRate = run.success_rate ? `${(run.success_rate * 100).toFixed(1)}%` : 'N/A';

                // Format semantic score
                const semanticScore = run.semantic_score ? `${(run.semantic_score * 100).toFixed(1)}%` : 'N/A';

                // Format duration
                const duration = run.mean_duration ? `${run.mean_duration.toFixed(2)}` : 'N/A';

                // Format evaluation variables - extract from context_variables if available
                let trustLevel = 'N/A';
                let valence = 'N/A';
                let arousal = 'N/A';
                let stressLevel = 'N/A';
                let timePressure = 'N/A';

                // Check if context_variables exist in the run object
                if (run.context_variables) {
                    // Extract trust level
                    if (run.context_variables.trust_level !== undefined) {
                        if (typeof run.context_variables.trust_level === 'object' && run.context_variables.trust_level.value !== undefined) {
                            trustLevel = run.context_variables.trust_level.value;
                        } else {
                            trustLevel = run.context_variables.trust_level;
                        }
                    }

                    // Extract valence - check direct first, then nested
                    if (run.context_variables.valence !== undefined) {
                        if (typeof run.context_variables.valence === 'object' && run.context_variables.valence.value !== undefined) {
                            valence = parseFloat(run.context_variables.valence.value).toFixed(1);
                        } else {
                            valence = parseFloat(run.context_variables.valence).toFixed(1);
                        }
                    } else if (run.context_variables.mood && run.context_variables.mood.valence !== undefined) {
                        if (typeof run.context_variables.mood.valence === 'object' && run.context_variables.mood.valence.value !== undefined) {
                            valence = parseFloat(run.context_variables.mood.valence.value).toFixed(1);
                        } else {
                            valence = parseFloat(run.context_variables.mood.valence).toFixed(1);
                        }
                    }

                    // Extract arousal - check direct first, then nested
                    if (run.context_variables.arousal !== undefined) {
                        if (typeof run.context_variables.arousal === 'object' && run.context_variables.arousal.value !== undefined) {
                            arousal = parseFloat(run.context_variables.arousal.value).toFixed(1);
                        } else {
                            arousal = parseFloat(run.context_variables.arousal).toFixed(1);
                        }
                    } else if (run.context_variables.mood && run.context_variables.mood.arousal !== undefined) {
                        if (typeof run.context_variables.mood.arousal === 'object' && run.context_variables.mood.arousal.value !== undefined) {
                            arousal = parseFloat(run.context_variables.mood.arousal.value).toFixed(1);
                        } else {
                            arousal = parseFloat(run.context_variables.mood.arousal).toFixed(1);
                        }
                    }

                    // Extract stress level - check direct first, then nested
                    if (run.context_variables.stress_level !== undefined) {
                        if (typeof run.context_variables.stress_level === 'object' && run.context_variables.stress_level.value !== undefined) {
                            stressLevel = run.context_variables.stress_level.value;
                        } else {
                            stressLevel = run.context_variables.stress_level;
                        }
                    } else if (run.context_variables.environment && run.context_variables.environment.stress_level !== undefined) {
                        if (typeof run.context_variables.environment.stress_level === 'object' && run.context_variables.environment.stress_level.value !== undefined) {
                            stressLevel = run.context_variables.environment.stress_level.value;
                        } else {
                            stressLevel = run.context_variables.environment.stress_level;
                        }
                    }

                    // Extract time pressure - check direct first, then nested
                    if (run.context_variables.time_pressure !== undefined) {
                        if (typeof run.context_variables.time_pressure === 'object' && run.context_variables.time_pressure.value !== undefined) {
                            timePressure = run.context_variables.time_pressure.value;
                        } else {
                            timePressure = run.context_variables.time_pressure;
                        }
                    } else if (run.context_variables.environment && run.context_variables.environment.time_pressure !== undefined) {
                        if (typeof run.context_variables.environment.time_pressure === 'object' && run.context_variables.environment.time_pressure.value !== undefined) {
                            timePressure = run.context_variables.environment.time_pressure.value;
                        } else {
                            timePressure = run.context_variables.environment.time_pressure;
                        }
                    }
                }

                // Fallback to direct properties if context_variables not available
                if (trustLevel === 'N/A' && run.trust_level !== undefined && run.trust_level !== 'N/A') {
                    trustLevel = run.trust_level;
                }
                if (valence === 'N/A' && run.valence !== undefined && run.valence !== 'N/A') {
                    valence = parseFloat(run.valence).toFixed(1);
                }
                if (arousal === 'N/A' && run.arousal !== undefined && run.arousal !== 'N/A') {
                    arousal = parseFloat(run.arousal).toFixed(1);
                }
                if (stressLevel === 'N/A' && run.stress_level !== undefined && run.stress_level !== 'N/A') {
                    stressLevel = run.stress_level;
                }
                if (timePressure === 'N/A' && run.time_pressure !== undefined && run.time_pressure !== 'N/A') {
                    timePressure = run.time_pressure;
                }

                // Format token usage and cost
                const tokenUsage = run.token_usage || 'N/A';
                const cost = run.cost || '$0.000000';

                row.innerHTML = `
                    <td>${run.scenario_name}</td>
                    <td>${run.agent_role}</td>
                    <td>${executionDate}</td>
                    <td>${duration}</td>
                    <td>${successRate}</td>
                    <td>${semanticScore}</td>
                    <td>${trustLevel}</td>
                    <td>${valence}</td>
                    <td>${arousal}</td>
                    <td>${stressLevel}</td>
                    <td>${timePressure}</td>
                    <td>${tokenUsage}</td>
                    <td>${cost}</td>
                    <td>
                        <button class="btn btn-secondary view-run-details" data-id="${run.id}">View Details</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to view details buttons
            document.querySelectorAll('.view-run-details').forEach(btn => {
                btn.addEventListener('click', function() {
                    const runId = this.getAttribute('data-id');
                    viewRunDetails(runId);
                });
            });

            // Show table, hide loading and empty message
            runsTable.classList.remove('hidden');
            runsLoading.classList.add('hidden');
            runsEmpty.classList.add('hidden');
        } else {
            // Show empty message, hide table and loading
            runsTable.classList.add('hidden');
            runsLoading.classList.add('hidden');
            runsEmpty.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading benchmark runs:', error);
        showError(`Error loading benchmark runs: ${error.message}`);

        // Hide loading, show empty message
        runsLoading.classList.add('hidden');
        runsEmpty.classList.remove('hidden');
    }
}

async function viewRunDetails(runId) {
    try {
        const response = await fetch(`/admin/benchmarks/api/run/${runId}/`);

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const runData = await response.json();

        // Create a simple modal to show run details
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Benchmark Run Details</h2>
                <div class="run-details">
                    <h3>${runData.scenario} (${runData.agent_role})</h3>
                    <p><strong>Execution Date:</strong> ${new Date(runData.execution_date).toLocaleString()}</p>
                    <p><strong>Success Rate:</strong> ${runData.success_rate ? (runData.success_rate * 100).toFixed(1) + '%' : 'N/A'}</p>
                    <p><strong>Semantic Score:</strong> ${runData.semantic_score ? (runData.semantic_score * 100).toFixed(1) + '%' : 'N/A'}</p>
                    <p><strong>Mean Duration:</strong> ${runData.mean_duration ? runData.mean_duration.toFixed(2) + 'ms' : 'N/A'}</p>
                    <p><strong>LLM Calls:</strong> ${runData.llm_calls || 'N/A'}</p>
                    <p><strong>Tool Calls:</strong> ${runData.tool_calls || 'N/A'}</p>
                    <p><strong>Token Usage:</strong> ${runData.token_usage || 'N/A'}</p>
                    <p><strong>Estimated Cost:</strong> $${runData.estimated_cost ? runData.estimated_cost.toFixed(4) : '0.0000'}</p>
                    ${runData.parameters ? `<h4>Parameters:</h4><pre>${JSON.stringify(runData.parameters, null, 2)}</pre>` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add close functionality
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });

        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

    } catch (error) {
        console.error('Error fetching run details:', error);
        showError(`Error fetching run details: ${error.message}`);
    }
}

function resetRunsFilters() {
    const scenarioFilter = document.getElementById('runs-scenario-filter');
    const agentRoleFilter = document.getElementById('runs-agent-role-filter');
    const successFilter = document.getElementById('runs-success-filter');
    const startDateFilter = document.getElementById('runs-start-date');
    const endDateFilter = document.getElementById('runs-end-date');

    if (scenarioFilter) scenarioFilter.value = '';
    if (agentRoleFilter) agentRoleFilter.value = '';
    if (successFilter) successFilter.value = '';
    if (startDateFilter) startDateFilter.value = '';
    if (endDateFilter) endDateFilter.value = '';

    loadBenchmarkRuns();
}

// User Profile Management Functions
function loadUserProfiles() {
    console.log('Loading user profiles...');

    // Show loading state
    const loadingElement = document.getElementById('user-profiles-loading');
    const emptyElement = document.getElementById('user-profiles-empty');

    if (loadingElement) loadingElement.classList.remove('hidden');
    if (emptyElement) emptyElement.classList.add('hidden');

    // TODO: Replace with actual API call when backend is implemented
    // For now, show sample data
    setTimeout(() => {
        const sampleProfiles = [
            {
                id: 1,
                name: 'New User - Foundation Phase',
                trust_level: 25,
                trust_phase: 'Foundation',
                archetype: 'new_user',
                demographics: { age: 28, gender: 'female', location: 'New York', occupation: 'Designer' },
                hexaco: { openness: 0.5, conscientiousness: 0.6, extraversion: 0.4 },
                is_active: true,
                usage_count: 5
            },
            {
                id: 2,
                name: 'Experienced User - Expansion Phase',
                trust_level: 65,
                trust_phase: 'Expansion',
                archetype: 'experienced_user',
                demographics: { age: 35, gender: 'male', location: 'San Francisco', occupation: 'Engineer' },
                hexaco: { openness: 0.7, conscientiousness: 0.7, extraversion: 0.6 },
                is_active: true,
                usage_count: 12
            },
            {
                id: 3,
                name: 'Confident User - Integration Phase',
                trust_level: 85,
                trust_phase: 'Integration',
                archetype: 'confident_user',
                demographics: { age: 42, gender: 'non-binary', location: 'Austin', occupation: 'Manager' },
                hexaco: { openness: 0.9, conscientiousness: 0.8, extraversion: 0.8 },
                is_active: true,
                usage_count: 8
            }
        ];

        renderUserProfilesTable(sampleProfiles);
        if (loadingElement) loadingElement.classList.add('hidden');
    }, 500);
}

function renderUserProfilesTable(profiles) {
    const tbody = document.querySelector('#user-profiles-table tbody');
    if (!tbody) return;

    const emptyElement = document.getElementById('user-profiles-empty');

    if (profiles.length === 0) {
        if (emptyElement) emptyElement.classList.remove('hidden');
        tbody.innerHTML = '';
        return;
    }

    tbody.innerHTML = profiles.map(profile => {
        const trustPhaseClass = profile.trust_phase.toLowerCase();
        const statusClass = profile.is_active ? 'active' : 'inactive';
        const statusText = profile.is_active ? 'Active' : 'Inactive';

        const demographicsSummary = [
            profile.demographics.age ? `${profile.demographics.age}y` : '',
            profile.demographics.gender || '',
            profile.demographics.location || ''
        ].filter(Boolean).join(', ') || 'Not specified';

        const personalitySummary = `O:${profile.hexaco.openness} C:${profile.hexaco.conscientiousness} E:${profile.hexaco.extraversion}`;

        return `
            <tr>
                <td><input type="checkbox" class="profile-checkbox" data-profile-id="${profile.id}"></td>
                <td><strong>${profile.name}</strong></td>
                <td><span class="profile-trust-badge ${trustPhaseClass}">${profile.trust_phase}</span></td>
                <td>${profile.trust_level}</td>
                <td><span class="profile-archetype-badge">${profile.archetype.replace('_', ' ')}</span></td>
                <td><div class="profile-demographics-summary">${demographicsSummary}</div></td>
                <td><div class="profile-personality-summary">${personalitySummary}</div></td>
                <td><span class="profile-status-badge ${statusClass}">${statusText}</span></td>
                <td><span class="profile-usage-count">${profile.usage_count}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editUserProfile(${profile.id})">Edit</button>
                    <button class="btn btn-sm btn-secondary" onclick="duplicateUserProfile(${profile.id})">Duplicate</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUserProfile(${profile.id})">Delete</button>
                </td>
            </tr>
        `;
    }).join('');
}

function createUserProfile() {
    console.log('Creating new user profile...');
    if (window.userProfileModal) {
        window.userProfileModal.show();
    } else {
        console.error('User profile modal not available');
        showError('User profile editor not available. Please refresh the page.');
    }
}

function editUserProfile(profileId) {
    console.log(`Editing user profile ${profileId}...`);
    if (window.userProfileModal) {
        window.userProfileModal.show(profileId);
    } else {
        console.error('User profile modal not available');
        showError('User profile editor not available. Please refresh the page.');
    }
}

function duplicateUserProfile(profileId) {
    console.log(`Duplicating user profile ${profileId}...`);
    // TODO: Implement duplication logic
    showSuccess('Profile duplication feature will be implemented with backend API');
}

function deleteUserProfile(profileId) {
    if (confirm('Are you sure you want to delete this user profile? This action cannot be undone.')) {
        console.log(`Deleting user profile ${profileId}...`);
        // TODO: Implement deletion logic
        showSuccess('Profile deletion feature will be implemented with backend API');
        loadUserProfiles(); // Refresh the table
    }
}

// Document ready initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('Benchmark Management page loaded');

    // Core Concepts Help Modal Event Listeners
    const conceptHelpBtn = document.getElementById('concept-help-btn');
    const conceptHelpModal = document.getElementById('concept-help-modal');

    if (conceptHelpBtn) {
        conceptHelpBtn.addEventListener('click', showConceptHelpModal);
    }

    if (conceptHelpModal) {
        // Close modal when clicking the close button
        const closeBtn = conceptHelpModal.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideConceptHelpModal);
        }

        // Close modal when clicking the "Got it!" button
        const gotItBtn = conceptHelpModal.querySelector('.close-modal');
        if (gotItBtn) {
            gotItBtn.addEventListener('click', hideConceptHelpModal);
        }

        // Close modal when clicking outside of it
        conceptHelpModal.addEventListener('click', function(event) {
            if (event.target === conceptHelpModal) {
                hideConceptHelpModal();
            }
        });
    }

    // Initialize Quick Test functionality
    initializeQuickTest();

    // Initialize other components as needed
    // ... existing initialization code can be added here
});

// Quick Test Functionality - Initialize using the QuickTest module
function initializeQuickTest() {
    // Check if QuickTest module is available
    if (typeof window.QuickTest === 'undefined') {
        console.warn('QuickTest module not loaded');
        return;
    }

    // Initialize QuickTest with required configuration
    const quickTestConfig = {
        scenariosApiUrl: window.BENCHMARK_SCENARIOS_API_URL,
        benchmarkRunApiUrl: '/admin/benchmarks/api/run/',
        taskStatusApiUrl: '/admin/benchmarks/api/task/',  // Correct URL pattern
        templatesApiUrl: '/admin/benchmarks/api/templates/',
        showSuccessMessage: showSuccess,
        showErrorMessage: showError
    };

    window.quickTestInstance = new window.QuickTest(quickTestConfig);
}









