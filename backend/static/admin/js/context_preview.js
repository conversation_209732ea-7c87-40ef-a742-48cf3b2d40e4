// ACTIVE_FILE - 29-05-2025
/**
 * Enhanced Context Preview
 *
 * This module provides an interactive preview and testing interface for
 * contextual evaluation templates with real-time adaptation visualization.
 */

class ContextPreview {
    constructor() {
        this.container = null;
        this.currentContext = this.getDefaultContext();
        this.presetScenarios = this.getPresetScenarios();
        this.socket = null;
        this.selectedCombinations = new Set(); // Track selected combination indices
        this.savedCombinationSets = this.loadSavedCombinationSets(); // Load saved sets from localStorage
        this.currentTaskId = null; // Track current running task for stop functionality

        this.init();
        this.initializeWebSocket();
    }

    init() {
        this.createContainer();
        this.setupEventListeners();
    }

    initializeWebSocket() {
        // Connect to the benchmark dashboard WebSocket for error events
        const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const wsUrl = `${protocol}://${window.location.host}/ws/benchmark-dashboard/`;

        try {
            this.socket = new WebSocket(wsUrl);

            this.socket.onopen = () => {
                console.log('ContextPreview: Connected to benchmark dashboard WebSocket.');
            };

            this.socket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'error' || data.type === 'debug_info' || data.type === 'tool_argument_error') {
                        this.handleWebSocketError(data);
                    }
                } catch (e) {
                    console.error('ContextPreview: Error parsing WebSocket message:', e);
                }
            };

            this.socket.onclose = (event) => {
                console.warn('ContextPreview: Benchmark dashboard WebSocket closed:', event);
            };

            this.socket.onerror = (error) => {
                console.error('ContextPreview: Benchmark dashboard WebSocket error:', error);
            };
        } catch (error) {
            console.error('ContextPreview: Failed to initialize WebSocket:', error);
        }
    }

    handleWebSocketError(data) {
        // Always log WebSocket errors for debugging
        console.log('WebSocket error received:', data);

        // Only handle errors if we're currently running a test
        const statusDiv = this.container?.querySelector('#test-status');
        if (!statusDiv || statusDiv.style.display === 'none') {
            // Even if not testing, show a notification for critical errors
            if (data.level === 'error' || data.type === 'error') {
                this.showGlobalErrorNotification(data);
            }
            return;
        }

        const statusText = this.container.querySelector('#test-status-text');
        const progressFill = this.container.querySelector('#test-progress-fill');
        const launchBtn = this.container.querySelector('#launch-test-btn');

        if (statusText && progressFill) {
            // Extract error message and details
            let errorMessage = 'An error occurred during benchmark execution';
            let errorDetails = {};

            if (data.type === 'error') {
                errorMessage = data.content || errorMessage;
                errorDetails = data.details || {};
            } else if (data.type === 'debug_info' && data.content) {
                // Handle debug_info messages with content structure
                errorMessage = data.content.message || errorMessage;
                errorDetails = data.content.details || {};
            } else if (data.type === 'debug_info' && data.data) {
                errorMessage = data.data.message || errorMessage;
                errorDetails = data.data.details || {};
            } else if (data.type === 'tool_argument_error') {
                errorMessage = `Tool Error (${data.tool_name}): ${data.error_message}`;
                errorDetails = data.details || {};
            }

            // Update UI to show error
            statusText.textContent = `Error: ${errorMessage}`;
            progressFill.style.width = '100%';
            progressFill.style.backgroundColor = '#dc3545'; // Red color for error

            // Re-enable the launch button
            if (launchBtn) {
                launchBtn.disabled = false;
            }

            // Show error in a more prominent way with details
            this.showTestError(errorMessage, errorDetails);
        }
    }

    showGlobalErrorNotification(data) {
        // Show a global notification for errors even when not testing
        const notification = document.createElement('div');
        notification.className = 'alert alert-danger alert-dismissible';
        notification.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';

        let message = 'Benchmark system error';
        if (data.type === 'debug_info' && data.content) {
            // Handle debug_info messages with content structure
            message = data.content.message || message;
        } else if (data.type === 'debug_info' && data.data) {
            message = data.data.message || message;
        } else if (data.content) {
            message = data.content;
        }

        notification.innerHTML = `
            <strong>Error:</strong> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    showTestError(errorMessage, errorDetails = {}) {
        // Create or update error display in the test results area
        const resultsDiv = this.container.querySelector('#test-results');
        if (!resultsDiv) return;

        // Check if this is an execution mode related error
        const isExecutionModeError = errorMessage.includes('Real workflow mode failed') ||
                                   errorMessage.includes('real mode') ||
                                   errorMessage.includes('execution failed');

        // Build detailed error display
        let errorHtml = `
            <div class="test-error-display">
                <h6><i class="fas fa-exclamation-triangle"></i> ${isExecutionModeError ? 'Execution Mode Error' : 'Test Error'}</h6>
                <div class="error-message">
                    <strong>Message:</strong> ${errorMessage}
                </div>
        `;

        // Add execution mode specific guidance
        if (isExecutionModeError) {
            const currentMode = this.container.querySelector('#execution-mode-select')?.value || 'unknown';
            errorHtml += `
                <div class="execution-mode-info" style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                    <h6><i class="fas fa-info-circle"></i> Execution Mode Information</h6>
                    <p><strong>Selected Mode:</strong> ${this.getExecutionModeDisplayName(currentMode)}</p>
                    <p><strong>Issue:</strong> The real workflow execution failed. This usually means the real workflow implementation is not fully ready or there's a configuration issue.</p>
                    <div class="suggested-actions">
                        <h6>Suggested Actions:</h6>
                        <ul>
                            <li>Try using <strong>Mock Mode</strong> for testing purposes</li>
                            <li>If you need real functionality, try <strong>Real Tools Only</strong> mode first</li>
                            <li>Check the error details below for specific guidance</li>
                            <li>Contact support if the issue persists</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // Add error details if available
        if (errorDetails && Object.keys(errorDetails).length > 0) {
            errorHtml += `
                <div class="error-details" style="margin-top: 15px;">
                    <h6>Error Details:</h6>
                    <div class="details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 10px;">
            `;

            // Show key details in a grid
            if (errorDetails.error_type) {
                errorHtml += `<div><strong>Type:</strong> ${errorDetails.error_type}</div>`;
            }
            if (errorDetails.scenario_name) {
                errorHtml += `<div><strong>Scenario:</strong> ${errorDetails.scenario_name}</div>`;
            }
            if (errorDetails.workflow_type) {
                errorHtml += `<div><strong>Workflow:</strong> ${errorDetails.workflow_type}</div>`;
            }
            if (errorDetails.benchmark_id) {
                errorHtml += `<div><strong>Benchmark ID:</strong> ${errorDetails.benchmark_id}</div>`;
            }
            if (errorDetails.requested_mode) {
                const mode = errorDetails.requested_mode;
                errorHtml += `<div><strong>Requested Mode:</strong> LLM:${mode.use_real_llm ? 'Real' : 'Mock'}, Tools:${mode.use_real_tools ? 'Real' : 'Mock'}, DB:${mode.use_real_db ? 'Real' : 'Mock'}</div>`;
            }
            if (errorDetails.suggestion) {
                errorHtml += `<div style="grid-column: 1 / -1;"><strong>Suggestion:</strong> ${errorDetails.suggestion}</div>`;
            }

            errorHtml += `
                    </div>
                    <button class="btn btn-sm btn-outline-secondary toggle-details" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
                        Show Full Details
                    </button>
                    <pre class="error-details-content" style="display: none; background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px; font-size: 12px; overflow-x: auto;">${JSON.stringify(errorDetails, null, 2)}</pre>
                </div>
            `;
        }

        errorHtml += `
                <div class="error-actions" style="margin-top: 15px;">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.test-error-display').style.display='none'">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                    ${isExecutionModeError ? `
                        <button type="button" class="btn btn-warning" onclick="document.getElementById('execution-mode-select').value='mock'; this.closest('.test-error-display').style.display='none';">
                            <i class="fas fa-shield-alt"></i> Switch to Mock Mode
                        </button>
                    ` : ''}
                    <button type="button" class="btn btn-primary" onclick="window.location.reload()">
                        <i class="fas fa-refresh"></i> Reload Page
                    </button>
                </div>
            </div>
        `;

        resultsDiv.innerHTML = errorHtml;
        resultsDiv.style.display = 'block';

        // Hide the status div after a short delay
        setTimeout(() => {
            const statusDiv = this.container.querySelector('#test-status');
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }, 2000);
    }

    getExecutionModeDisplayName(mode) {
        const modeNames = {
            'mock': '🎭 Mock Mode (Safe - No costs)',
            'real-tools': '🛠️ Real Tools Only',
            'real-llm': '🧠 Real LLM Only',
            'real-db': '🗄️ Real Database Only',
            'partial-real': '⚡ Partial Real (Tools + DB)',
            'full-real': '🚀 Full Real Mode (All components)'
        };
        return modeNames[mode] || mode;
    }

    displayCompletionWithErrors(result, statusText, progressFill) {
        /**
         * Display completion status with warning indicators for non-critical errors
         */
        const errorSummary = result.error_summary || {};
        const criticalErrors = errorSummary.critical_errors || 0;
        const warnings = errorSummary.warnings || 0;

        if (criticalErrors > 0) {
            statusText.textContent = `Completed with ${criticalErrors} critical error(s)`;
            progressFill.style.backgroundColor = '#dc3545'; // Red for critical errors
        } else if (warnings > 0) {
            statusText.textContent = `Completed with ${warnings} warning(s)`;
            progressFill.style.backgroundColor = '#ffc107'; // Yellow for warnings
        } else {
            statusText.textContent = 'Completed with minor issues';
            progressFill.style.backgroundColor = '#17a2b8'; // Blue for info
        }

        progressFill.style.width = '100%';
    }

    displayTaskFailure(result, statusText, progressFill) {
        /**
         * Display detailed failure information for failed tasks
         */
        const errorDetails = result.error_details || {};
        const errorSummary = errorDetails.error_summary || {};
        const totalErrors = errorSummary.total_errors || 1;

        statusText.textContent = `Failed with ${totalErrors} error(s)`;
        progressFill.style.width = '100%';
        progressFill.style.backgroundColor = '#dc3545';

        // Show detailed error information
        this.showDetailedError(result);
    }

    displayProgressWithErrors(meta, errorDetails, statusText, progressFill) {
        /**
         * Display progress with error indicators
         */
        const errorSummary = errorDetails.error_summary || {};
        const warnings = errorSummary.warnings || 0;

        let baseStatus = meta.status || 'Running test...';
        if (warnings > 0) {
            baseStatus += ` (${warnings} warning(s))`;
            progressFill.style.backgroundColor = '#ffc107'; // Yellow for warnings
        }

        statusText.textContent = baseStatus;

        if (meta.current && meta.total) {
            const progress = Math.min((meta.current / meta.total) * 100, 95);
            progressFill.style.width = `${progress}%`;
        }
    }

    showDetailedError(result) {
        /**
         * Display detailed error information in the results area
         */
        const resultsDiv = this.container.querySelector('#test-results');
        if (!resultsDiv) return;

        const errorDetails = result.error_details || {};
        const errors = errorDetails.errors || [];
        const errorSummary = errorDetails.error_summary || {};

        let errorHtml = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Task Failed</h6>
                <p><strong>Primary Error:</strong> ${result.error || 'Unknown error'}</p>

                <div class="error-summary" style="margin: 15px 0;">
                    <h6>Error Summary:</h6>
                    <div class="summary-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        <div><strong>Total Errors:</strong> ${errorSummary.total_errors || 0}</div>
                        <div><strong>Critical:</strong> <span style="color: #dc3545;">${errorSummary.critical_errors || 0}</span></div>
                        <div><strong>Warnings:</strong> <span style="color: #ffc107;">${errorSummary.warnings || 0}</span></div>
                        <div><strong>Info:</strong> <span style="color: #17a2b8;">${errorSummary.info_messages || 0}</span></div>
                    </div>
                </div>
        `;

        if (errors.length > 0) {
            errorHtml += `
                <div class="detailed-errors" style="margin-top: 15px;">
                    <h6>Detailed Errors:</h6>
                    <div class="errors-list" style="max-height: 300px; overflow-y: auto;">
            `;

            errors.forEach((error, index) => {
                const errorTypeClass = this.getErrorTypeClass(error.type || error.level);
                errorHtml += `
                    <div class="error-item ${errorTypeClass}" style="border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 10px; padding: 10px;">
                        <div class="error-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <span class="error-badge ${errorTypeClass}">${this.getErrorTypeIcon(error.type || error.level)} ${(error.type || error.level || 'error').toUpperCase()}</span>
                            <small class="text-muted">${error.source || 'Unknown source'}</small>
                        </div>
                        <div class="error-message" style="margin-bottom: 5px;">
                            <strong>Message:</strong> ${error.message || 'No message provided'}
                        </div>
                        ${error.timestamp ? `<div class="error-timestamp"><small><strong>Time:</strong> ${error.timestamp}</small></div>` : ''}
                        ${error.details && Object.keys(error.details).length > 0 ? `
                            <div class="error-details" style="margin-top: 10px;">
                                <button class="btn btn-sm btn-outline-secondary toggle-details" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
                                    Show Details
                                </button>
                                <pre class="error-details-content" style="display: none; background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px; font-size: 12px; overflow-x: auto;">${JSON.stringify(error.details, null, 2)}</pre>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            errorHtml += `
                    </div>
                </div>
            `;
        }

        errorHtml += '</div>';

        resultsDiv.innerHTML = errorHtml;
        resultsDiv.style.display = 'block';
    }

    getErrorTypeClass(errorType) {
        /**
         * Get CSS class for error type styling
         */
        switch ((errorType || '').toLowerCase()) {
            case 'critical':
            case 'error':
                return 'error-critical';
            case 'warning':
                return 'error-warning';
            case 'info':
                return 'error-info';
            default:
                return 'error-unknown';
        }
    }

    getErrorTypeIcon(errorType) {
        /**
         * Get icon for error type
         */
        switch ((errorType || '').toLowerCase()) {
            case 'critical':
            case 'error':
                return '🔴';
            case 'warning':
                return '🟡';
            case 'info':
                return '🔵';
            default:
                return '⚪';
        }
    }

    createContainer() {
        const modal = document.getElementById('template-modal');
        if (!modal) return;

        // Remove existing container
        const existing = modal.querySelector('#context-preview-container');
        if (existing) existing.remove();

        this.container = document.createElement('div');
        this.container.id = 'context-preview-container';
        this.container.className = 'template-tab-content';
        this.container.innerHTML = this.getHTML();

        // Insert into modal form
        const form = modal.querySelector('#template-form');
        if (form) {
            form.appendChild(this.container);
        }
    }

    getHTML() {
        return `
            <div class="context-preview enhanced">
                <div class="preview-header">
                    <h4><i class="fas fa-eye"></i> Context Preview & Testing</h4>
                    <p class="help-text">Test how your template adapts to different contextual scenarios in real-time.</p>
                </div>

                <div class="preview-layout">
                    <div class="context-simulator">
                        <h5><i class="fas fa-sliders-h"></i> Context Simulator</h5>

                        <div class="context-controls">
                            <div class="control-group">
                                <label>Trust Level: <span id="trust-level-value" class="value-display">50</span></label>
                                <input type="range" id="trust-level-slider" min="0" max="100" value="50" class="form-range">
                                <div class="range-labels">
                                    <span>Foundation</span>
                                    <span>Expansion</span>
                                    <span>Integration</span>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Mood Valence: <span id="valence-value" class="value-display">0.0</span></label>
                                <input type="range" id="valence-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                                <div class="range-labels">
                                    <span>Negative</span>
                                    <span>Neutral</span>
                                    <span>Positive</span>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Mood Arousal: <span id="arousal-value" class="value-display">0.0</span></label>
                                <input type="range" id="arousal-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                                <div class="range-labels">
                                    <span>Calm</span>
                                    <span>Neutral</span>
                                    <span>Excited</span>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Stress Level: <span id="stress-level-value" class="value-display">30</span></label>
                                <input type="range" id="stress-level-slider" min="0" max="100" value="30" class="form-range">
                                <div class="range-labels">
                                    <span>Low</span>
                                    <span>Medium</span>
                                    <span>High</span>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Time Pressure: <span id="time-pressure-value" class="value-display">30</span></label>
                                <input type="range" id="time-pressure-slider" min="0" max="100" value="30" class="form-range">
                                <div class="range-labels">
                                    <span>Relaxed</span>
                                    <span>Moderate</span>
                                    <span>Urgent</span>
                                </div>
                            </div>
                        </div>

                        <div class="simulator-actions">
                            <button type="button" class="btn btn-primary" id="update-preview-btn">
                                <i class="fas fa-refresh"></i> Update Preview
                            </button>
                            <button type="button" class="btn btn-secondary" id="reset-context-btn">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <div class="preview-results">
                        <h5><i class="fas fa-magic"></i> Adapted Criteria Preview</h5>
                        <div id="adapted-criteria-display" class="criteria-display enhanced">
                            <div class="placeholder">
                                <i class="fas fa-info-circle"></i>
                                <p>Adjust context variables and click "Update Preview" to see how criteria adapt.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="preset-scenarios">
                    <h5><i class="fas fa-users"></i> Preset User Scenarios</h5>
                    <div class="scenario-grid">
                        <button type="button" class="btn btn-outline-primary scenario-btn" data-scenario="new-user">
                            <i class="fas fa-user-plus"></i>
                            <span>New User</span>
                            <small>Low trust, cautious</small>
                        </button>
                        <button type="button" class="btn btn-outline-warning scenario-btn" data-scenario="stressed-user">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Stressed User</span>
                            <small>High stress, negative mood</small>
                        </button>
                        <button type="button" class="btn btn-outline-success scenario-btn" data-scenario="confident-user">
                            <i class="fas fa-user-check"></i>
                            <span>Confident User</span>
                            <small>High trust, positive mood</small>
                        </button>
                        <button type="button" class="btn btn-outline-info scenario-btn" data-scenario="low-mood">
                            <i class="fas fa-frown"></i>
                            <span>Low Mood User</span>
                            <small>Negative valence, low arousal</small>
                        </button>
                        <button type="button" class="btn btn-outline-secondary scenario-btn" data-scenario="time-pressured">
                            <i class="fas fa-clock"></i>
                            <span>Time Pressured</span>
                            <small>High urgency, focused</small>
                        </button>
                        <button type="button" class="btn btn-outline-dark scenario-btn" data-scenario="overwhelmed">
                            <i class="fas fa-dizzy"></i>
                            <span>Overwhelmed</span>
                            <small>High stress, low trust</small>
                        </button>
                    </div>
                </div>

                <div class="context-analysis">
                    <h6><i class="fas fa-chart-line"></i> Context Analysis</h6>
                    <div class="analysis-grid" id="context-analysis-grid">
                        <!-- Analysis will be populated dynamically -->
                    </div>
                </div>

                <div class="benchmark-testing">
                    <h5><i class="fas fa-play-circle"></i> Live Benchmark Testing</h5>
                    <p class="help-text">Test your evaluation template with real benchmark scenarios and see actual results.</p>

                    <div class="test-configuration">
                        <div class="config-row">
                            <div class="config-group">
                                <label for="test-scenario-select">Benchmark Scenario:</label>
                                <select id="test-scenario-select" class="form-control">
                                    <option value="">Loading scenarios...</option>
                                </select>
                            </div>
                            <div class="config-group">
                                <label for="test-runs-input">Number of Runs:</label>
                                <input type="number" id="test-runs-input" class="form-control" min="1" max="5" value="1">
                            </div>
                        </div>

                        <div class="config-row">
                            <div class="config-group">
                                <label for="execution-mode-select">Execution Mode:</label>
                                <select id="execution-mode-select" class="form-control">
                                    <option value="mock">🎭 Mock Mode (Safe - No costs)</option>
                                    <option value="real-tools">🛠️ Real Tools Only</option>
                                    <option value="real-llm">🧠 Real LLM Only</option>
                                    <option value="real-db">🗄️ Real Database Only</option>
                                    <option value="partial-real">⚡ Partial Real (Tools + DB)</option>
                                    <option value="full-real">🚀 Full Real Mode (All components)</option>
                                </select>
                                <small class="help-text">Choose execution mode - Real modes use actual resources and may incur costs</small>
                            </div>
                            <div class="config-group">
                                <label for="fake-user-profile-select">User Profile:</label>
                                <select id="fake-user-profile-select" class="form-control">
                                    <option value="">Loading profiles...</option>
                                </select>
                                <small class="help-text">Select a fake user profile for testing (only non-real profiles shown)</small>
                            </div>
                        </div>

                        <div class="config-row">
                            <div class="config-group">
                                <label>
                                    <input type="checkbox" id="test-semantic-eval" checked>
                                    Enable Semantic Evaluation
                                </label>
                            </div>
                            <div class="config-group">
                                <label>
                                    <input type="checkbox" id="multi-range-evaluation">
                                    Multi-Range Contextual Evaluation
                                </label>
                                <small class="help-text">Test against all defined variable ranges instead of just the current context</small>
                            </div>
                        </div>

                        <div class="multi-range-combinations" id="multi-range-combinations" style="display: none;">
                            <h6><i class="fas fa-list"></i> Combinations to be tested:</h6>

                            <div class="combination-set-controls">
                                <div class="control-row">
                                    <div class="control-group">
                                        <label for="combination-set-name">Set Name:</label>
                                        <input type="text" id="combination-set-name" class="form-control" placeholder="Enter set name...">
                                    </div>
                                    <div class="control-group">
                                        <label for="saved-combination-sets">Load Saved Set:</label>
                                        <select id="saved-combination-sets" class="form-control">
                                            <option value="">Select a saved set...</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <button type="button" id="save-combination-set-btn" class="btn btn-primary btn-sm">
                                        <i class="fas fa-save"></i> Save Set
                                    </button>
                                    <button type="button" id="select-all-combinations-btn" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-check-square"></i> Select All
                                    </button>
                                    <button type="button" id="clear-selection-btn" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-square"></i> Clear Selection
                                    </button>
                                    <button type="button" id="delete-combination-set-btn" class="btn btn-danger btn-sm" disabled>
                                        <i class="fas fa-trash"></i> Delete Set
                                    </button>
                                </div>
                            </div>

                            <div class="combinations-container" id="combinations-container">
                                <p class="text-muted">Enable Multi-Range Contextual Evaluation to see combinations</p>
                            </div>
                        </div>

                        <div class="config-row">
                            <div class="config-group">
                                <button type="button" id="launch-test-btn" class="btn btn-success" disabled>
                                    <i class="fas fa-rocket"></i> Launch Test
                                </button>
                                <button type="button" id="stop-test-btn" class="btn btn-danger" style="display: none; margin-left: 10px;">
                                    <i class="fas fa-stop"></i> Stop Test
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="test-status" id="test-status" style="display: none;">
                        <div class="status-indicator">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span id="test-status-text">Preparing test...</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="test-progress-fill"></div>
                        </div>
                    </div>

                    <div class="test-results" id="test-results" style="display: none;">
                        <h6><i class="fas fa-chart-bar"></i> Test Results</h6>
                        <div class="results-summary" id="results-summary">
                            <!-- Summary will be populated -->
                        </div>
                        <div class="results-table-container">
                            <table class="results-table" id="results-table">
                                <thead>
                                    <tr>
                                        <th>Run #</th>
                                        <th>Success</th>
                                        <th>Semantic Score</th>
                                        <th>Execution Time</th>
                                        <th>Token Usage</th>
                                        <th>Cost</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody id="results-table-body">
                                    <!-- Results will be populated -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        if (!this.container) return;

        // Slider updates
        const sliders = [
            { id: 'trust-level-slider', valueId: 'trust-level-value' },
            { id: 'valence-slider', valueId: 'valence-value' },
            { id: 'arousal-slider', valueId: 'arousal-value' },
            { id: 'stress-level-slider', valueId: 'stress-level-value' },
            { id: 'time-pressure-slider', valueId: 'time-pressure-value' }
        ];

        sliders.forEach(({ id, valueId }) => {
            const slider = this.container.querySelector(`#${id}`);
            const valueDisplay = this.container.querySelector(`#${valueId}`);

            if (slider && valueDisplay) {
                slider.addEventListener('input', () => {
                    valueDisplay.textContent = slider.value;
                    this.updateCurrentContext();
                    this.updateContextAnalysis();
                });
            }
        });

        // Update preview button
        const updateBtn = this.container.querySelector('#update-preview-btn');
        if (updateBtn) {
            updateBtn.addEventListener('click', () => this.updatePreview());
        }

        // Reset button
        const resetBtn = this.container.querySelector('#reset-context-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetContext());
        }

        // Scenario buttons
        this.container.querySelectorAll('.scenario-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const scenario = btn.dataset.scenario;
                this.loadScenario(scenario);
            });
        });

        // Benchmark testing event listeners
        const launchTestBtn = this.container.querySelector('#launch-test-btn');
        if (launchTestBtn) {
            launchTestBtn.addEventListener('click', () => this.launchBenchmarkTest());
        }

        const stopTestBtn = this.container.querySelector('#stop-test-btn');
        if (stopTestBtn) {
            stopTestBtn.addEventListener('click', () => this.stopBenchmarkTest());
        }

        const scenarioSelect = this.container.querySelector('#test-scenario-select');
        if (scenarioSelect) {
            scenarioSelect.addEventListener('change', () => this.updateLaunchButtonState());
        }

        // Multi-range evaluation checkbox
        const multiRangeCheckbox = this.container.querySelector('#multi-range-evaluation');
        if (multiRangeCheckbox) {
            multiRangeCheckbox.addEventListener('change', () => this.updateMultiRangeCombinations());
        }

        // Combination set management event listeners
        const saveCombinationSetBtn = this.container.querySelector('#save-combination-set-btn');
        if (saveCombinationSetBtn) {
            saveCombinationSetBtn.addEventListener('click', () => this.saveCombinationSet());
        }

        const savedCombinationSetsSelect = this.container.querySelector('#saved-combination-sets');
        if (savedCombinationSetsSelect) {
            savedCombinationSetsSelect.addEventListener('change', () => this.loadCombinationSet());
        }

        const selectAllBtn = this.container.querySelector('#select-all-combinations-btn');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllCombinations());
        }

        const clearSelectionBtn = this.container.querySelector('#clear-selection-btn');
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', () => this.clearCombinationSelection());
        }

        const deleteCombinationSetBtn = this.container.querySelector('#delete-combination-set-btn');
        if (deleteCombinationSetBtn) {
            deleteCombinationSetBtn.addEventListener('click', () => this.deleteCombinationSet());
        }
    }

    getDefaultContext() {
        return {
            trust_level: 50,
            mood: {
                valence: 0.0,
                arousal: 0.0
            },
            environment: {
                stress_level: 30,
                time_pressure: 30
            }
        };
    }

    getPresetScenarios() {
        return {
            'new-user': {
                trust_level: 25,
                mood: { valence: -0.2, arousal: 0.3 },
                environment: { stress_level: 40, time_pressure: 20 },
                description: 'New user with low trust, slightly anxious but curious'
            },
            'stressed-user': {
                trust_level: 60,
                mood: { valence: -0.5, arousal: 0.8 },
                environment: { stress_level: 85, time_pressure: 90 },
                description: 'Experienced user under high stress and time pressure'
            },
            'confident-user': {
                trust_level: 85,
                mood: { valence: 0.7, arousal: 0.2 },
                environment: { stress_level: 15, time_pressure: 25 },
                description: 'Confident user in a relaxed state, ready for challenges'
            },
            'low-mood': {
                trust_level: 45,
                mood: { valence: -0.8, arousal: -0.4 },
                environment: { stress_level: 60, time_pressure: 40 },
                description: 'User experiencing low mood, needs gentle support'
            },
            'time-pressured': {
                trust_level: 70,
                mood: { valence: 0.1, arousal: 0.6 },
                environment: { stress_level: 50, time_pressure: 95 },
                description: 'User with urgent deadline, needs quick efficient help'
            },
            'overwhelmed': {
                trust_level: 30,
                mood: { valence: -0.6, arousal: 0.9 },
                environment: { stress_level: 90, time_pressure: 80 },
                description: 'Overwhelmed user, needs calm and structured support'
            }
        };
    }

    updateCurrentContext() {
        this.currentContext = {
            trust_level: parseInt(this.container.querySelector('#trust-level-slider').value),
            mood: {
                valence: parseFloat(this.container.querySelector('#valence-slider').value),
                arousal: parseFloat(this.container.querySelector('#arousal-slider').value)
            },
            environment: {
                stress_level: parseInt(this.container.querySelector('#stress-level-slider').value),
                time_pressure: parseInt(this.container.querySelector('#time-pressure-slider').value)
            }
        };
    }

    updateContextAnalysis() {
        const analysisGrid = this.container.querySelector('#context-analysis-grid');
        if (!analysisGrid) return;

        const analysis = this.analyzeContext(this.currentContext);

        analysisGrid.innerHTML = `
            <div class="analysis-item">
                <span class="analysis-label">Trust Phase:</span>
                <span class="analysis-value ${analysis.trustPhase.toLowerCase()}">${analysis.trustPhase}</span>
            </div>
            <div class="analysis-item">
                <span class="analysis-label">Mood Quadrant:</span>
                <span class="analysis-value">${analysis.moodQuadrant}</span>
            </div>
            <div class="analysis-item">
                <span class="analysis-label">Stress Level:</span>
                <span class="analysis-value ${analysis.stressCategory.toLowerCase()}">${analysis.stressCategory}</span>
            </div>
            <div class="analysis-item">
                <span class="analysis-label">Urgency:</span>
                <span class="analysis-value ${analysis.urgencyLevel.toLowerCase()}">${analysis.urgencyLevel}</span>
            </div>
        `;
    }

    analyzeContext(context) {
        // Trust phase analysis
        let trustPhase = 'Foundation';
        if (context.trust_level >= 70) trustPhase = 'Integration';
        else if (context.trust_level >= 40) trustPhase = 'Expansion';

        // Mood quadrant analysis
        const valence = context.mood.valence;
        const arousal = context.mood.arousal;
        let moodQuadrant = 'Neutral';

        if (valence > 0.2 && arousal > 0.2) moodQuadrant = 'Excited';
        else if (valence > 0.2 && arousal < -0.2) moodQuadrant = 'Content';
        else if (valence < -0.2 && arousal > 0.2) moodQuadrant = 'Agitated';
        else if (valence < -0.2 && arousal < -0.2) moodQuadrant = 'Sad';

        // Stress category
        let stressCategory = 'Low';
        if (context.environment.stress_level >= 70) stressCategory = 'High';
        else if (context.environment.stress_level >= 40) stressCategory = 'Medium';

        // Urgency level
        let urgencyLevel = 'Relaxed';
        if (context.environment.time_pressure >= 70) urgencyLevel = 'Urgent';
        else if (context.environment.time_pressure >= 40) urgencyLevel = 'Moderate';

        return {
            trustPhase,
            moodQuadrant,
            stressCategory,
            urgencyLevel
        };
    }

    loadScenario(scenarioKey) {
        const scenario = this.presetScenarios[scenarioKey];
        if (!scenario) return;

        // Update sliders
        this.container.querySelector('#trust-level-slider').value = scenario.trust_level;
        this.container.querySelector('#trust-level-value').textContent = scenario.trust_level;

        this.container.querySelector('#valence-slider').value = scenario.mood.valence;
        this.container.querySelector('#valence-value').textContent = scenario.mood.valence;

        this.container.querySelector('#arousal-slider').value = scenario.mood.arousal;
        this.container.querySelector('#arousal-value').textContent = scenario.mood.arousal;

        this.container.querySelector('#stress-level-slider').value = scenario.environment.stress_level;
        this.container.querySelector('#stress-level-value').textContent = scenario.environment.stress_level;

        this.container.querySelector('#time-pressure-slider').value = scenario.environment.time_pressure;
        this.container.querySelector('#time-pressure-value').textContent = scenario.environment.time_pressure;

        this.updateCurrentContext();
        this.updateContextAnalysis();
        this.updatePreview();

        // Highlight the selected scenario button
        this.container.querySelectorAll('.scenario-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const selectedBtn = this.container.querySelector(`[data-scenario="${scenarioKey}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
        }
    }

    resetContext() {
        const defaultContext = this.getDefaultContext();

        this.container.querySelector('#trust-level-slider').value = defaultContext.trust_level;
        this.container.querySelector('#trust-level-value').textContent = defaultContext.trust_level;

        this.container.querySelector('#valence-slider').value = defaultContext.mood.valence;
        this.container.querySelector('#valence-value').textContent = defaultContext.mood.valence;

        this.container.querySelector('#arousal-slider').value = defaultContext.mood.arousal;
        this.container.querySelector('#arousal-value').textContent = defaultContext.mood.arousal;

        this.container.querySelector('#stress-level-slider').value = defaultContext.environment.stress_level;
        this.container.querySelector('#stress-level-value').textContent = defaultContext.environment.stress_level;

        this.container.querySelector('#time-pressure-slider').value = defaultContext.environment.time_pressure;
        this.container.querySelector('#time-pressure-value').textContent = defaultContext.environment.time_pressure;

        this.currentContext = defaultContext;
        this.updateContextAnalysis();
        this.updatePreview();

        // Remove active state from scenario buttons
        this.container.querySelectorAll('.scenario-btn').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    updatePreview() {
        const baseCriteria = this.getBaseCriteria();
        const contextualCriteria = this.getContextualCriteria();

        const adaptedCriteria = this.adaptCriteriaForContext(baseCriteria, contextualCriteria, this.currentContext);
        this.displayAdaptedCriteria(adaptedCriteria);
        this.updateMultiRangeCombinations();
    }

    getBaseCriteria() {
        const criteriaTextarea = document.getElementById('template-criteria');
        if (!criteriaTextarea) return {};

        try {
            return JSON.parse(criteriaTextarea.value || '{}');
        } catch (e) {
            console.warn('Invalid base criteria JSON:', e);
            return {};
        }
    }

    getContextualCriteria() {
        const contextualTextarea = document.getElementById('template-contextual-criteria');
        if (!contextualTextarea) return {};

        try {
            return JSON.parse(contextualTextarea.value || '{}');
        } catch (e) {
            console.warn('Invalid contextual criteria JSON:', e);
            return {};
        }
    }

    adaptCriteriaForContext(baseCriteria, contextualCriteria, context) {
        const adapted = { ...baseCriteria };

        // Apply trust level adaptations
        if (contextualCriteria.trust_level && context.trust_level !== undefined) {
            const trustRange = this.getTrustLevelRange(context.trust_level);
            if (contextualCriteria.trust_level[trustRange]) {
                Object.entries(contextualCriteria.trust_level[trustRange]).forEach(([dimension, criteria]) => {
                    if (!adapted[dimension]) adapted[dimension] = [];
                    adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
                });
            }
        }

        // Apply mood valence adaptations
        if (contextualCriteria.mood?.valence && context.mood?.valence !== undefined) {
            const valenceRange = this.getValenceRange(context.mood.valence);
            if (contextualCriteria.mood.valence[valenceRange]) {
                Object.entries(contextualCriteria.mood.valence[valenceRange]).forEach(([dimension, criteria]) => {
                    if (!adapted[dimension]) adapted[dimension] = [];
                    adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
                });
            }
        }

        // Apply stress level adaptations
        if (contextualCriteria.environment?.stress_level && context.environment?.stress_level !== undefined) {
            const stressRange = this.getStressLevelRange(context.environment.stress_level);
            if (contextualCriteria.environment.stress_level[stressRange]) {
                Object.entries(contextualCriteria.environment.stress_level[stressRange]).forEach(([dimension, criteria]) => {
                    if (!adapted[dimension]) adapted[dimension] = [];
                    adapted[dimension] = [...new Set([...adapted[dimension], ...criteria])];
                });
            }
        }

        return adapted;
    }

    getTrustLevelRange(trustLevel) {
        if (trustLevel <= 39) return '0-39';
        if (trustLevel <= 69) return '40-69';
        return '70-100';
    }

    getValenceRange(valence) {
        return valence < 0 ? '-1.0-0.0' : '0.0-1.0';
    }

    getStressLevelRange(stressLevel) {
        if (stressLevel <= 30) return '0-30';
        if (stressLevel <= 70) return '31-70';
        return '71-100';
    }

    displayAdaptedCriteria(adaptedCriteria) {
        const display = this.container.querySelector('#adapted-criteria-display');
        if (!display) return;

        const analysis = this.analyzeContext(this.currentContext);

        let html = `
            <div class="context-summary enhanced">
                <h6><i class="fas fa-info-circle"></i> Current Context:</h6>
                <div class="context-details">
                    <div class="context-item">
                        <span class="label">Trust Level:</span>
                        <span class="value">${this.currentContext.trust_level}</span>
                        <span class="phase ${analysis.trustPhase.toLowerCase()}">${analysis.trustPhase}</span>
                    </div>
                    <div class="context-item">
                        <span class="label">Mood:</span>
                        <span class="value">V:${this.currentContext.mood.valence}, A:${this.currentContext.mood.arousal}</span>
                        <span class="quadrant">${analysis.moodQuadrant}</span>
                    </div>
                    <div class="context-item">
                        <span class="label">Environment:</span>
                        <span class="value">Stress:${this.currentContext.environment.stress_level}, Time:${this.currentContext.environment.time_pressure}</span>
                        <span class="level">${analysis.stressCategory} stress, ${analysis.urgencyLevel}</span>
                    </div>
                </div>
            </div>

            <div class="adapted-criteria enhanced">
                <h6><i class="fas fa-magic"></i> Adapted Criteria:</h6>
        `;

        if (Object.keys(adaptedCriteria).length === 0) {
            html += '<p class="text-muted">No criteria defined. Add base criteria in the "Base Criteria" tab.</p>';
        } else {
            Object.entries(adaptedCriteria).forEach(([dimension, criteria]) => {
                html += `
                    <div class="criteria-dimension enhanced">
                        <div class="dimension-header">
                            <strong>${dimension}:</strong>
                            <span class="criteria-count">${criteria.length} criteria</span>
                        </div>
                        <div class="criteria-list">
                            ${criteria.map(criterion => `<span class="criterion-tag">${criterion}</span>`).join('')}
                        </div>
                    </div>
                `;
            });
        }

        html += '</div>';
        display.innerHTML = html;
    }

    refresh() {
        this.updateCurrentContext();
        this.updateContextAnalysis();
        this.updatePreview();
        this.loadBenchmarkScenarios();
        this.loadFakeUserProfiles();
        this.updateMultiRangeCombinations();
    }

    updateMultiRangeCombinations() {
        const multiRangeCheckbox = this.container.querySelector('#multi-range-evaluation');
        const combinationsDiv = this.container.querySelector('#multi-range-combinations');
        const combinationsContainer = this.container.querySelector('#combinations-container');

        if (!multiRangeCheckbox || !combinationsDiv || !combinationsContainer) return;

        if (multiRangeCheckbox.checked) {
            combinationsDiv.style.display = 'block';

            // Get variable ranges from the template
            const variableRanges = this.getVariableRanges();
            const combinations = this.generateCombinations(variableRanges);

            if (combinations.length > 0) {
                combinationsContainer.innerHTML = this.renderCombinations(combinations);
                this.setupCombinationCheckboxListeners();
                this.updateSavedSetsDropdown();
            } else {
                combinationsContainer.innerHTML = '<p class="text-muted">No variable ranges defined in template. Add variable ranges to see combinations.</p>';
            }
        } else {
            combinationsDiv.style.display = 'none';
        }
    }

    getVariableRanges() {
        const variableRangesTextarea = document.getElementById('template-variable-ranges');
        if (!variableRangesTextarea) return {};

        try {
            return JSON.parse(variableRangesTextarea.value || '{}');
        } catch (e) {
            console.warn('Invalid variable ranges JSON:', e);
            return {};
        }
    }

    generateCombinations(variableRanges) {
        if (!variableRanges || Object.keys(variableRanges).length === 0) {
            return [];
        }

        // Define standard ranges for each variable type
        const standardRanges = {
            trust_level: [
                { range: '0-39', label: 'Foundation (0-39)', values: [20, 35] },
                { range: '40-69', label: 'Expansion (40-69)', values: [50, 65] },
                { range: '70-100', label: 'Integration (70-100)', values: [80, 95] }
            ],
            valence: [
                { range: '-1.0-0.0', label: 'Negative (-1.0 to 0.0)', values: [-0.8, -0.3] },
                { range: '0.0-1.0', label: 'Positive (0.0 to 1.0)', values: [0.3, 0.8] }
            ],
            arousal: [
                { range: '-1.0-0.0', label: 'Calm (-1.0 to 0.0)', values: [-0.8, -0.3] },
                { range: '0.0-1.0', label: 'Excited (0.0 to 1.0)', values: [0.3, 0.8] }
            ],
            stress_level: [
                { range: '0-30', label: 'Low (0-30)', values: [10, 25] },
                { range: '31-70', label: 'Medium (31-70)', values: [45, 60] },
                { range: '71-100', label: 'High (71-100)', values: [80, 95] }
            ],
            time_pressure: [
                { range: '0-30', label: 'Relaxed (0-30)', values: [10, 25] },
                { range: '31-70', label: 'Moderate (31-70)', values: [45, 60] },
                { range: '71-100', label: 'Urgent (71-100)', values: [80, 95] }
            ]
        };

        const combinations = [];
        const variables = [];

        // Build list of variables and their ranges
        Object.keys(variableRanges).forEach(varName => {
            if (varName === 'mood') {
                // Handle mood sub-variables
                const moodRanges = variableRanges.mood;
                if (moodRanges.valence && standardRanges.valence) {
                    variables.push({ name: 'valence', ranges: standardRanges.valence });
                }
                if (moodRanges.arousal && standardRanges.arousal) {
                    variables.push({ name: 'arousal', ranges: standardRanges.arousal });
                }
            } else if (varName === 'environment') {
                // Handle environment sub-variables
                const envRanges = variableRanges.environment;
                if (envRanges.stress_level && standardRanges.stress_level) {
                    variables.push({ name: 'stress_level', ranges: standardRanges.stress_level });
                }
                if (envRanges.time_pressure && standardRanges.time_pressure) {
                    variables.push({ name: 'time_pressure', ranges: standardRanges.time_pressure });
                }
            } else if (standardRanges[varName]) {
                variables.push({ name: varName, ranges: standardRanges[varName] });
            }
        });

        if (variables.length === 0) return [];

        // Generate all combinations
        const generateRecursive = (index, currentCombination) => {
            if (index >= variables.length) {
                combinations.push({ ...currentCombination });
                return;
            }

            const variable = variables[index];
            variable.ranges.forEach(range => {
                // Use the first value from the range as representative
                currentCombination[variable.name] = {
                    value: range.values[0],
                    label: range.label,
                    range: range.range
                };
                generateRecursive(index + 1, currentCombination);
            });
        };

        generateRecursive(0, {});
        return combinations;
    }

    renderCombinations(combinations) {
        if (combinations.length === 0) {
            return '<p class="text-muted">No combinations to display</p>';
        }

        // Get all variable names for table headers
        const variableNames = combinations.length > 0 ? Object.keys(combinations[0]) : [];
        const selectedCount = this.selectedCombinations.size;

        let html = `
            <div class="combinations-summary">
                <p><strong>${combinations.length} total combinations</strong>
                   ${selectedCount > 0 ? `<span class="selected-count">(${selectedCount} selected)</span>` : ''}</p>
            </div>
            <div class="combinations-table-container">
                <table class="combinations-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-checkbox"
                                       ${selectedCount === combinations.length ? 'checked' : ''}>
                            </th>
                            <th>#</th>
        `;

        // Add header for each variable
        variableNames.forEach(varName => {
            html += `<th>${varName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</th>`;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add rows for each combination
        combinations.forEach((combination, index) => {
            const isSelected = this.selectedCombinations.has(index);
            const rowClass = isSelected ? 'combination-row selected' : 'combination-row';

            html += `<tr class="${rowClass}">
                        <td>
                            <input type="checkbox" class="combination-checkbox"
                                   data-combination-index="${index}" ${isSelected ? 'checked' : ''}>
                        </td>
                        <td class="combination-index">${index + 1}</td>`;

            variableNames.forEach(varName => {
                const varData = combination[varName];
                const colorClass = this.getVariableColorClass(varName, varData.value, varData.range);
                html += `
                    <td class="combination-cell ${colorClass}" title="${varData.label}">
                        ${varData.value}
                    </td>
                `;
            });

            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        return html;
    }

    getVariableColorClass(varName, value, range) {
        // Define color classes based on variable type and value ranges
        switch (varName) {
            case 'trust_level':
                if (range === '0-39') return 'trust-foundation';
                if (range === '40-69') return 'trust-expansion';
                if (range === '70-100') return 'trust-integration';
                break;

            case 'valence':
                if (range === '-1.0-0.0') return 'mood-negative';
                if (range === '0.0-1.0') return 'mood-positive';
                break;

            case 'arousal':
                if (range === '-1.0-0.0') return 'arousal-calm';
                if (range === '0.0-1.0') return 'arousal-excited';
                break;

            case 'stress_level':
                if (range === '0-30') return 'stress-low';
                if (range === '31-70') return 'stress-medium';
                if (range === '71-100') return 'stress-high';
                break;

            case 'time_pressure':
                if (range === '0-30') return 'pressure-relaxed';
                if (range === '31-70') return 'pressure-moderate';
                if (range === '71-100') return 'pressure-urgent';
                break;
        }

        return 'variable-default';
    }

    // Combination Set Management Methods

    loadSavedCombinationSets() {
        try {
            const saved = localStorage.getItem('contextPreview_combinationSets');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            console.warn('Error loading saved combination sets:', e);
            return {};
        }
    }

    saveCombinationSets() {
        try {
            localStorage.setItem('contextPreview_combinationSets', JSON.stringify(this.savedCombinationSets));
        } catch (e) {
            console.error('Error saving combination sets:', e);
        }
    }

    setupCombinationCheckboxListeners() {
        // Individual combination checkboxes
        this.container.querySelectorAll('.combination-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const index = parseInt(e.target.dataset.combinationIndex);
                if (e.target.checked) {
                    this.selectedCombinations.add(index);
                } else {
                    this.selectedCombinations.delete(index);
                }
                this.updateCombinationRowSelection(index, e.target.checked);
                this.updateSelectAllCheckbox();
                this.updateCombinationSummary();
            });
        });

        // Select all checkbox
        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectAllCombinations();
                } else {
                    this.clearCombinationSelection();
                }
            });
        }
    }

    updateCombinationRowSelection(index, isSelected) {
        const row = this.container.querySelector(`[data-combination-index="${index}"]`)?.closest('tr');
        if (row) {
            if (isSelected) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }
    }

    updateSelectAllCheckbox() {
        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        const totalCombinations = this.container.querySelectorAll('.combination-checkbox').length;

        if (selectAllCheckbox && totalCombinations > 0) {
            selectAllCheckbox.checked = this.selectedCombinations.size === totalCombinations;
            selectAllCheckbox.indeterminate = this.selectedCombinations.size > 0 && this.selectedCombinations.size < totalCombinations;
        }
    }

    updateCombinationSummary() {
        const summaryElement = this.container.querySelector('.combinations-summary p');
        if (summaryElement) {
            const totalCombinations = this.container.querySelectorAll('.combination-checkbox').length;
            const selectedCount = this.selectedCombinations.size;

            let summaryText = `<strong>${totalCombinations} total combinations</strong>`;
            if (selectedCount > 0) {
                summaryText += ` <span class="selected-count">(${selectedCount} selected)</span>`;
            }
            summaryElement.innerHTML = summaryText;
        }
    }

    selectAllCombinations() {
        this.container.querySelectorAll('.combination-checkbox').forEach((checkbox, index) => {
            checkbox.checked = true;
            this.selectedCombinations.add(index);
            this.updateCombinationRowSelection(index, true);
        });
        this.updateSelectAllCheckbox();
        this.updateCombinationSummary();
    }

    clearCombinationSelection() {
        this.container.querySelectorAll('.combination-checkbox').forEach((checkbox, index) => {
            checkbox.checked = false;
            this.selectedCombinations.delete(index);
            this.updateCombinationRowSelection(index, false);
        });
        this.updateSelectAllCheckbox();
        this.updateCombinationSummary();
    }

    saveCombinationSet() {
        const setNameInput = this.container.querySelector('#combination-set-name');
        const setName = setNameInput?.value.trim();

        if (!setName) {
            alert('Please enter a name for the combination set.');
            return;
        }

        if (this.selectedCombinations.size === 0) {
            alert('Please select at least one combination to save.');
            return;
        }

        // Get current combinations to save their data
        const variableRanges = this.getVariableRanges();
        const combinations = this.generateCombinations(variableRanges);
        const selectedCombinationData = Array.from(this.selectedCombinations).map(index => combinations[index]);

        this.savedCombinationSets[setName] = {
            name: setName,
            combinations: selectedCombinationData,
            selectedIndices: Array.from(this.selectedCombinations),
            createdAt: new Date().toISOString(),
            variableRanges: variableRanges
        };

        this.saveCombinationSets();
        this.updateSavedSetsDropdown();

        // Clear the input
        setNameInput.value = '';

        alert(`Combination set "${setName}" saved successfully!`);
    }

    loadCombinationSet() {
        const select = this.container.querySelector('#saved-combination-sets');
        const setName = select?.value;

        if (!setName || !this.savedCombinationSets[setName]) {
            this.updateDeleteButtonState();
            return;
        }

        const savedSet = this.savedCombinationSets[setName];

        // Clear current selection
        this.clearCombinationSelection();

        // Load the saved selection
        savedSet.selectedIndices.forEach(index => {
            this.selectedCombinations.add(index);
            const checkbox = this.container.querySelector(`[data-combination-index="${index}"]`);
            if (checkbox) {
                checkbox.checked = true;
                this.updateCombinationRowSelection(index, true);
            }
        });

        this.updateSelectAllCheckbox();
        this.updateCombinationSummary();
        this.updateDeleteButtonState();

        // Update the set name input
        const setNameInput = this.container.querySelector('#combination-set-name');
        if (setNameInput) {
            setNameInput.value = setName;
        }
    }

    deleteCombinationSet() {
        const select = this.container.querySelector('#saved-combination-sets');
        const setName = select?.value;

        if (!setName || !this.savedCombinationSets[setName]) {
            return;
        }

        if (confirm(`Are you sure you want to delete the combination set "${setName}"?`)) {
            delete this.savedCombinationSets[setName];
            this.saveCombinationSets();
            this.updateSavedSetsDropdown();

            // Clear the selection and input
            select.value = '';
            const setNameInput = this.container.querySelector('#combination-set-name');
            if (setNameInput) {
                setNameInput.value = '';
            }
            this.updateDeleteButtonState();
        }
    }

    updateSavedSetsDropdown() {
        const select = this.container.querySelector('#saved-combination-sets');
        if (!select) return;

        // Save current selection
        const currentValue = select.value;

        // Clear and rebuild options
        select.innerHTML = '<option value="">Select a saved set...</option>';

        Object.keys(this.savedCombinationSets).forEach(setName => {
            const option = document.createElement('option');
            option.value = setName;
            option.textContent = setName;
            select.appendChild(option);
        });

        // Restore selection if it still exists
        if (currentValue && this.savedCombinationSets[currentValue]) {
            select.value = currentValue;
        }

        this.updateDeleteButtonState();
    }

    updateDeleteButtonState() {
        const select = this.container.querySelector('#saved-combination-sets');
        const deleteBtn = this.container.querySelector('#delete-combination-set-btn');

        if (deleteBtn) {
            deleteBtn.disabled = !select?.value;
        }
    }

    // Benchmark Testing Methods

    async loadBenchmarkScenarios() {
        const scenarioSelect = this.container.querySelector('#test-scenario-select');
        if (!scenarioSelect) return;

        try {
            const response = await fetch(window.BENCHMARK_SCENARIOS_API_URL || '/admin/benchmarks/api/scenarios/');

            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                throw new Error(`Failed to parse server response: ${parseError.message}`);
            }

            if (!response.ok) {
                // Extract detailed error message from the response
                const errorMessage = data.error || `HTTP error ${response.status}`;
                throw new Error(errorMessage);
            }
            const scenarios = data.scenarios || [];

            // Clear existing options
            scenarioSelect.innerHTML = '<option value="">Select a scenario...</option>';

            // Add scenario options
            scenarios.forEach(scenario => {
                if (scenario.is_active) {
                    const option = document.createElement('option');
                    option.value = scenario.id;
                    option.textContent = `${scenario.name} (${scenario.agent_role})`;
                    scenarioSelect.appendChild(option);
                }
            });

            this.updateLaunchButtonState();

        } catch (error) {
            console.error('Error loading benchmark scenarios:', error);
            scenarioSelect.innerHTML = '<option value="">Error loading scenarios</option>';
        }
    }

    async loadFakeUserProfiles() {
        const profileSelect = this.container.querySelector('#fake-user-profile-select');
        if (!profileSelect) return;

        try {
            // Fetch user profiles with is_real=False filter
            const response = await fetch('/admin/benchmarks/api/user-profiles/?is_real=false');
            const data = await response.json();

            profileSelect.innerHTML = '<option value="">Select a test profile...</option>';

            if (data.profiles && Array.isArray(data.profiles)) {
                data.profiles.forEach(profile => {
                    const option = document.createElement('option');
                    option.value = profile.id;

                    // Create descriptive text with trust level and demographics
                    const trustInfo = profile.trust_level ? `Trust: ${profile.trust_level}` : '';
                    const demoInfo = profile.demographics ?
                        `${profile.demographics.age}y, ${profile.demographics.gender || 'N/A'}` : '';
                    const description = [trustInfo, demoInfo].filter(Boolean).join(' | ');

                    option.textContent = `${profile.profile_name}${description ? ` (${description})` : ''}`;
                    option.dataset.profileData = JSON.stringify(profile);
                    profileSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading fake user profiles:', error);
            profileSelect.innerHTML = '<option value="">Error loading profiles</option>';
        }
    }

    getExecutionModeParams() {
        const executionMode = this.container.querySelector('#execution-mode-select')?.value || 'mock';

        const modeParams = {
            'mock': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: false
            },
            'real-tools': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: false
            },
            'real-llm': {
                use_real_llm: true,
                use_real_tools: false,
                use_real_db: false
            },
            'real-db': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: true
            },
            'partial-real': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: true
            },
            'full-real': {
                use_real_llm: true,
                use_real_tools: true,
                use_real_db: true
            }
        };

        return modeParams[executionMode] || modeParams['mock'];
    }

    updateLaunchButtonState() {
        const scenarioSelect = this.container.querySelector('#test-scenario-select');
        const launchBtn = this.container.querySelector('#launch-test-btn');

        if (scenarioSelect && launchBtn) {
            launchBtn.disabled = !scenarioSelect.value;
        }
    }

    async launchBenchmarkTest() {
        const scenarioSelect = this.container.querySelector('#test-scenario-select');
        const runsInput = this.container.querySelector('#test-runs-input');
        const semanticEvalCheckbox = this.container.querySelector('#test-semantic-eval');
        const statusDiv = this.container.querySelector('#test-status');
        const resultsDiv = this.container.querySelector('#test-results');
        const launchBtn = this.container.querySelector('#launch-test-btn');

        if (!scenarioSelect.value) {
            alert('Please select a benchmark scenario first.');
            return;
        }

        // Get current template data
        const templateData = this.getCurrentTemplateData();
        if (!templateData) {
            alert('Please fill in the template name and criteria before testing.');
            return;
        }

        // Show status, hide results
        statusDiv.style.display = 'block';
        resultsDiv.style.display = 'none';
        launchBtn.disabled = true;

        // Show stop button if it exists
        const stopBtn = this.container.querySelector('#stop-test-btn');
        if (stopBtn) {
            stopBtn.style.display = 'inline-block';
            stopBtn.disabled = false;
        }

        const statusText = this.container.querySelector('#test-status-text');
        const progressFill = this.container.querySelector('#test-progress-fill');

        try {
            statusText.textContent = 'Launching benchmark test...';
            progressFill.style.width = '10%';

            // Prepare test parameters
            const multiRangeCheckbox = this.container.querySelector('#multi-range-evaluation');
            const isMultiRange = multiRangeCheckbox ? multiRangeCheckbox.checked : false;

            // Get execution mode parameters
            const executionModeParams = this.getExecutionModeParams();

            // Get selected user profile
            const profileSelect = this.container.querySelector('#fake-user-profile-select');
            const selectedProfileId = profileSelect?.value || null;

            const testParams = {
                scenario_id: parseInt(scenarioSelect.value),
                evaluation_template_data: templateData,
                params: {
                    runs: parseInt(runsInput.value) || 1,
                    semantic_evaluation: semanticEvalCheckbox.checked,
                    context_variables: this.currentContext,
                    multi_range_contextual_evaluation: isMultiRange,
                    // Add execution mode parameters
                    ...executionModeParams,
                    // Add user profile if selected
                    ...(selectedProfileId && { user_profile_id: selectedProfileId })
                }
            };

            // If multi-range is enabled and combinations are selected, include them
            if (isMultiRange && this.selectedCombinations.size > 0) {
                // Get the current combinations and filter by selected indices
                const variableRanges = this.getVariableRanges();
                const allCombinations = this.generateCombinations(variableRanges);
                const selectedCombinationData = Array.from(this.selectedCombinations).map(index => allCombinations[index]);

                testParams.params.selected_combinations = selectedCombinationData;
                testParams.params.selected_combination_indices = Array.from(this.selectedCombinations);

                statusText.textContent = `Preparing test for ${this.selectedCombinations.size} selected combinations...`;
            } else if (isMultiRange) {
                // Multi-range is enabled but no specific combinations selected - test all
                statusText.textContent = 'Preparing test for all combinations...';
            }

            statusText.textContent = 'Submitting test request...';
            progressFill.style.width = '30%';

            // Launch the benchmark test
            const response = await fetch('/admin/benchmarks/api/run/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(testParams)
            });

            let result;
            try {
                result = await response.json();
            } catch (parseError) {
                throw new Error(`Failed to parse server response: ${parseError.message}`);
            }

            if (!response.ok) {
                // Extract detailed error message from the response
                const errorMessage = result.error || `HTTP error ${response.status}`;
                throw new Error(errorMessage);
            }

            if (result.task_id) {
                // Store task ID for stop functionality
                this.currentTaskId = result.task_id;
                // Poll for results
                await this.pollForResults(result.task_id, statusText, progressFill);
            } else if (result.runs) {
                // Direct results - update progress and hide status
                statusText.textContent = 'Test completed!';
                progressFill.style.width = '100%';
                progressFill.style.backgroundColor = '#28a745';

                setTimeout(() => {
                    statusDiv.style.display = 'none';
                    this.displayTestResults(result.runs);
                }, 1000);
            } else {
                throw new Error('Unexpected response format');
            }

        } catch (error) {
            console.error('Error launching benchmark test:', error);
            statusText.textContent = `Error: ${error.message}`;
            progressFill.style.width = '100%';
            progressFill.style.backgroundColor = '#dc3545';

            // Also show the error in the results area for better visibility
            this.showTestError(error.message);
        } finally {
            launchBtn.disabled = false;
            this.currentTaskId = null;

            // Hide stop button
            const stopBtn = this.container.querySelector('#stop-test-btn');
            if (stopBtn) {
                stopBtn.style.display = 'none';
            }
        }
    }

    async stopBenchmarkTest() {
        if (!this.currentTaskId) {
            alert('No test is currently running.');
            return;
        }

        const stopBtn = this.container.querySelector('#stop-test-btn');
        const statusText = this.container.querySelector('#test-status-text');
        const progressFill = this.container.querySelector('#test-progress-fill');

        try {
            stopBtn.disabled = true;
            statusText.textContent = 'Stopping test...';

            const response = await fetch(`/admin/benchmarks/api/run/${this.currentTaskId}/stop/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (response.ok) {
                statusText.textContent = 'Test stopped successfully.';
                progressFill.style.width = '100%';
                progressFill.style.backgroundColor = '#ffc107'; // Warning color for stopped

                setTimeout(() => {
                    this.container.querySelector('#test-status').style.display = 'none';
                }, 2000);
            } else {
                throw new Error(result.error || 'Failed to stop test');
            }
        } catch (error) {
            console.error('Error stopping benchmark test:', error);
            statusText.textContent = `Error stopping test: ${error.message}`;
            progressFill.style.backgroundColor = '#dc3545';
        } finally {
            this.currentTaskId = null;
            stopBtn.style.display = 'none';

            const launchBtn = this.container.querySelector('#launch-test-btn');
            if (launchBtn) {
                launchBtn.disabled = false;
            }
        }
    }

    getCurrentTemplateData() {
        // Try to get template data from the form
        const nameField = document.getElementById('template-name');
        const criteriaField = document.getElementById('template-criteria');
        const contextualCriteriaField = document.getElementById('template-contextual-criteria');

        if (!nameField || !criteriaField || !nameField.value.trim()) {
            return null;
        }

        // Parse criteria to validate JSON
        let criteria = {};
        let contextualCriteria = {};

        try {
            criteria = JSON.parse(criteriaField.value || '{}');
        } catch (e) {
            alert('Invalid criteria JSON format. Please check the Base Criteria tab.');
            return null;
        }

        try {
            contextualCriteria = JSON.parse(contextualCriteriaField?.value || '{}');
        } catch (e) {
            alert('Invalid contextual criteria JSON format. Please check the Contextual Criteria tab.');
            return null;
        }

        return {
            id: document.getElementById('template-id')?.value || null,
            name: nameField.value.trim(),
            description: document.getElementById('template-description')?.value || '',
            workflow_type: document.getElementById('template-workflow-type-select')?.value || '',
            category: document.getElementById('template-category-select')?.value || 'quality',
            criteria: criteria,
            contextual_criteria: contextualCriteria,
            context_variables: this.currentContext,
            is_active: document.getElementById('template-is-active')?.checked || true
        };
    }

    async pollForResults(taskId, statusText, progressFill) {
        const maxAttempts = 60; // 5 minutes max
        let attempts = 0;

        const poll = async () => {
            attempts++;

            try {
                const response = await fetch(`/admin/benchmarks/api/task/${taskId}/status/`);

                let result;
                try {
                    result = await response.json();
                } catch (parseError) {
                    throw new Error(`Failed to parse server response: ${parseError.message}`);
                }

                if (!response.ok) {
                    // Extract detailed error message from the response
                    const errorMessage = result.error || `HTTP error ${response.status}`;
                    throw new Error(errorMessage);
                }

                if (result.status === 'completed') {
                    // Check for non-critical errors in successful completion
                    if (result.has_errors) {
                        this.displayCompletionWithErrors(result, statusText, progressFill);
                    } else {
                        statusText.textContent = 'Test completed!';
                        progressFill.style.width = '100%';
                        progressFill.style.backgroundColor = '#28a745';
                    }

                    setTimeout(() => {
                        this.container.querySelector('#test-status').style.display = 'none';
                        this.displayTestResults(result.runs || [], result.error_details);
                    }, 1000);

                } else if (result.status === 'failed') {
                    // Enhanced error display for failed tasks
                    this.displayTaskFailure(result, statusText, progressFill);

                } else if (result.status === 'progress' || result.status === 'started' || result.status === 'pending') {
                    // Handle Celery task progress with error awareness
                    const meta = result.meta || {};

                    // Check for progress errors
                    if (result.has_errors && result.error_details) {
                        this.displayProgressWithErrors(meta, result.error_details, statusText, progressFill);
                    } else {
                        if (meta.current && meta.total) {
                            // Use actual progress from task
                            const progress = Math.min((meta.current / meta.total) * 100, 95);
                            progressFill.style.width = `${progress}%`;
                            statusText.textContent = meta.status || `Running test... (${meta.current}/${meta.total})`;
                        } else {
                            // Fallback to time-based progress
                            const progress = Math.min(30 + (attempts * 2), 90);
                            progressFill.style.width = `${progress}%`;
                            statusText.textContent = meta.status || `Running test... (${attempts}/${maxAttempts})`;
                        }
                    }

                    if (attempts < maxAttempts) {
                        setTimeout(poll, 3000); // Poll every 3 seconds for better responsiveness
                    } else {
                        throw new Error('Test timeout - taking too long to complete');
                    }
                } else {
                    // Unknown status, continue polling
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 3000);
                    } else {
                        throw new Error('Test timeout');
                    }
                }

            } catch (error) {
                statusText.textContent = `Error: ${error.message}`;
                progressFill.style.width = '100%';
                progressFill.style.backgroundColor = '#dc3545';

                // Also show the error in the results area for better visibility
                this.showTestError(error.message);
            }
        };

        // Start polling
        setTimeout(poll, 2000); // Initial delay
    }

    formatTokenCount(tokenStr) {
        // Helper function to format token counts with 'k' for thousands
        if (typeof tokenStr === 'string' && tokenStr.includes('+')) {
            // Handle format like "100+50=150" or "1.2k+0.8k=2.0k"
            return tokenStr; // Already formatted by backend
        }

        const tokens = parseInt(tokenStr);
        if (isNaN(tokens)) return tokenStr;

        if (tokens >= 1000) {
            return `${(tokens / 1000).toFixed(1)}k`;
        }
        return tokens.toString();
    }

    displayTestResults(runs) {
        const resultsDiv = this.container.querySelector('#test-results');
        const summaryDiv = this.container.querySelector('#results-summary');
        const tableBody = this.container.querySelector('#results-table-body');

        if (!resultsDiv || !summaryDiv || !tableBody) return;

        // Check if this is a multi-range evaluation result
        if (runs.length === 1 && runs[0].semantic_evaluations && runs[0].semantic_evaluations.multi_range_evaluation) {
            this.displayMultiRangeResults(runs[0]);
            return;
        }

        // Calculate summary statistics
        const totalRuns = runs.length;
        const successfulRuns = runs.filter(run => run.success).length;
        const avgSemanticScore = runs.reduce((sum, run) => sum + (run.semantic_score || 0), 0) / totalRuns;
        const avgExecutionTime = runs.reduce((sum, run) => sum + (run.execution_time || 0), 0) / totalRuns;
        const totalCost = runs.reduce((sum, run) => sum + (run.cost || 0), 0);

        // Display summary
        summaryDiv.innerHTML = `
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">Success Rate:</span>
                    <span class="summary-value ${successfulRuns === totalRuns ? 'success' : 'warning'}">
                        ${successfulRuns}/${totalRuns} (${((successfulRuns/totalRuns)*100).toFixed(1)}%)
                    </span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Avg Semantic Score:</span>
                    <span class="summary-value">${avgSemanticScore.toFixed(2)}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Avg Execution Time:</span>
                    <span class="summary-value">${avgExecutionTime.toFixed(2)}s</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Total Cost:</span>
                    <span class="summary-value">$${totalCost.toFixed(4)}</span>
                </div>
            </div>
        `;

        // Display detailed results
        tableBody.innerHTML = '';
        runs.forEach((run, index) => {
            const row = document.createElement('tr');
            row.className = run.success ? 'success-row' : 'failure-row';

            row.innerHTML = `
                <td>${index + 1}</td>
                <td>
                    <span class="status-badge ${run.success ? 'success' : 'failure'}">
                        ${run.success ? '✓' : '✗'}
                    </span>
                </td>
                <td>${(run.semantic_score || 0).toFixed(2)}</td>
                <td>${(run.execution_time || 0).toFixed(2)}s</td>
                <td>${this.formatTokenCount(run.token_usage) || 'N/A'}</td>
                <td>$${(run.cost || 0).toFixed(4)}</td>
                <td>
                    <button class="btn btn-sm btn-secondary view-run-details" data-run-id="${run.id}">
                        View
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Add event listeners for view details buttons
        tableBody.querySelectorAll('.view-run-details').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const runId = e.target.dataset.runId;
                this.showRunDetails(runId, runs);
            });
        });

        // Show results
        resultsDiv.style.display = 'block';
    }

    displayMultiRangeResults(run) {
        const resultsDiv = this.container.querySelector('#test-results');
        const summaryDiv = this.container.querySelector('#results-summary');

        if (!resultsDiv || !summaryDiv) return;

        const contextualEvaluations = run.semantic_evaluations.contextual_evaluations;
        const rangeCount = Object.keys(contextualEvaluations).length;

        // Calculate summary statistics across all ranges
        const scores = [];
        let successCount = 0;

        Object.values(contextualEvaluations).forEach(rangeData => {
            const primaryResult = rangeData.results['mistral-small-latest']; // Use primary evaluator
            if (primaryResult && !primaryResult.error && primaryResult.overall_score !== null) {
                scores.push(primaryResult.overall_score);
                successCount++;
            }
        });

        const avgScore = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
        const minScore = scores.length > 0 ? Math.min(...scores) : 0;
        const maxScore = scores.length > 0 ? Math.max(...scores) : 0;

        // Display summary
        summaryDiv.innerHTML = `
            <div class="multi-range-summary">
                <h6><i class="fas fa-chart-line"></i> Multi-Range Contextual Evaluation Results</h6>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">Ranges Tested:</span>
                        <span class="summary-value">${rangeCount}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Success Rate:</span>
                        <span class="summary-value ${successCount === rangeCount ? 'success' : 'warning'}">
                            ${successCount}/${rangeCount} (${((successCount/rangeCount)*100).toFixed(1)}%)
                        </span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Average Score:</span>
                        <span class="summary-value">${avgScore.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Score Range:</span>
                        <span class="summary-value">${minScore.toFixed(2)} - ${maxScore.toFixed(2)}</span>
                    </div>
                </div>
            </div>
        `;

        // Create detailed results table for each range
        const detailedResults = document.createElement('div');
        detailedResults.className = 'multi-range-details';
        detailedResults.innerHTML = `
            <h6><i class="fas fa-table"></i> Results by Context Range</h6>
            <div class="range-results-container">
                <table class="range-results-table">
                    <thead>
                        <tr>
                            <th>Context Range</th>
                            <th>Semantic Score</th>
                            <th>Status</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody id="range-results-body">
                    </tbody>
                </table>
            </div>
        `;

        // Clear existing content and add new results
        resultsDiv.innerHTML = '';
        resultsDiv.appendChild(summaryDiv);
        resultsDiv.appendChild(detailedResults);

        const rangeTableBody = detailedResults.querySelector('#range-results-body');

        Object.entries(contextualEvaluations).forEach(([rangeKey, rangeData]) => {
            const primaryResult = rangeData.results['mistral-small-latest'];
            const row = document.createElement('tr');

            const score = primaryResult && !primaryResult.error ? primaryResult.overall_score : 0;
            const status = primaryResult && !primaryResult.error ? 'success' : 'failure';
            const reasoning = primaryResult ? primaryResult.overall_reasoning : 'Evaluation failed';

            row.innerHTML = `
                <td><strong>${rangeKey}</strong></td>
                <td>${score ? score.toFixed(2) : 'N/A'}</td>
                <td>
                    <span class="status-badge ${status}">
                        ${status === 'success' ? '✓' : '✗'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-secondary view-range-details"
                            data-range-key="${rangeKey}"
                            title="${reasoning}">
                        View
                    </button>
                </td>
            `;

            rangeTableBody.appendChild(row);
        });

        // Add event listeners for range details
        rangeTableBody.querySelectorAll('.view-range-details').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const rangeKey = e.target.dataset.rangeKey;
                this.showRangeDetails(rangeKey, contextualEvaluations[rangeKey]);
            });
        });

        // Show results
        resultsDiv.style.display = 'block';
    }

    showRangeDetails(rangeKey, rangeData) {
        const primaryResult = rangeData.results['mistral-small-latest'];
        if (!primaryResult) return;

        const details = `
Multi-Range Evaluation Details - ${rangeKey}

Context: ${JSON.stringify(rangeData.context, null, 2)}

Overall Score: ${primaryResult.overall_score || 'N/A'}
Overall Reasoning: ${primaryResult.overall_reasoning || 'N/A'}

Dimension Scores:
${Object.entries(primaryResult.dimensions || {}).map(([dim, data]) =>
    `- ${dim}: ${data.score} (${data.reasoning})`
).join('\n')}
        `;

        alert(details);
    }

    showRunDetails(runId, runs) {
        const run = runs.find(r => r.id === runId);
        if (!run) return;

        // Create a detailed modal with run details including agent communications
        const modal = document.createElement('div');
        modal.className = 'run-details-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000;">
                <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 90%; max-height: 90%; overflow-y: auto; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                        <h5><i class="fas fa-info-circle"></i> Benchmark Run Details</h5>
                        <button class="close-modal" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="run-summary">
                            <h6>Run Summary</h6>
                            <div class="summary-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 20px;">
                                <div><strong>ID:</strong> ${run.id}</div>
                                <div><strong>Success:</strong> <span class="status-badge ${run.success ? 'success' : 'failure'}">${run.success ? '✓ Yes' : '✗ No'}</span></div>
                                <div><strong>Semantic Score:</strong> ${run.semantic_score || 'N/A'}</div>
                                <div><strong>Execution Time:</strong> ${run.execution_time || 'N/A'}s</div>
                                <div><strong>Token Usage:</strong> ${this.formatTokenCount(run.token_usage) || 'N/A'}</div>
                                <div><strong>Cost:</strong> $${run.cost || 'N/A'}</div>
                            </div>
                            ${run.error ? `<div class="error-info" style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px;"><strong>Error:</strong> ${run.error}</div>` : ''}
                        </div>
                        ${this.renderAgentCommunications(run)}
                    </div>
                </div>
            </div>
        `;

        // Add to document
        document.body.appendChild(modal);

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.querySelector('.modal-backdrop').addEventListener('click', (e) => {
            if (e.target === modal.querySelector('.modal-backdrop')) {
                document.body.removeChild(modal);
            }
        });
    }

    renderAgentCommunications(run) {
        // Check if agent communications data is available
        if (!run.agent_communications || !run.agent_communications.enabled) {
            return '<div class="no-communications"><p><em>No agent communication data available for this run.</em></p></div>';
        }

        const comms = run.agent_communications;

        return `
            <div class="agent-communications-section">
                <h6><i class="fas fa-exchange-alt"></i> Agent Communications</h6>

                <div class="communications-summary" style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                    <div class="summary-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        <div><strong>Workflow ID:</strong> ${comms.workflow_id || 'N/A'}</div>
                        <div><strong>Total Communications:</strong> ${comms.summary?.total_communications || 0}</div>
                        <div><strong>Successful:</strong> ${comms.summary?.successful_communications || 0}</div>
                        <div><strong>Failed:</strong> ${comms.summary?.failed_communications || 0}</div>
                        <div><strong>Total Duration:</strong> ${(comms.summary?.total_duration_ms || 0).toFixed(2)}ms</div>
                        <div><strong>State Transitions:</strong> ${comms.summary?.state_transitions || 0}</div>
                    </div>
                    <div style="margin-top: 10px;"><strong>Agents Involved:</strong> ${(comms.summary?.agents_involved || []).join(', ')}</div>
                </div>

                ${this.renderAgentInteractions(comms.agents || [])}
                ${this.renderStateTransitions(comms.state_transitions || [])}
            </div>
        `;
    }

    renderAgentInteractions(agents) {
        if (!agents || agents.length === 0) {
            return '<div class="no-agents"><p><em>No agent interactions recorded.</em></p></div>';
        }

        return `
            <div class="agent-interactions">
                <h6><i class="fas fa-users"></i> Agent Interactions (${agents.length})</h6>
                <div class="agents-list">
                    ${agents.map((agent, index) => `
                        <div class="agent-interaction" style="border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 10px;">
                            <div class="agent-header" style="background: #f8f9fa; padding: 10px; cursor: pointer; display: flex; justify-content: space-between; align-items: center;" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
                                <div>
                                    <strong>${agent.agent}</strong> (${agent.stage}) - ${agent.duration_ms.toFixed(2)}ms
                                    <span class="status-badge ${agent.success ? 'success' : 'failure'}" style="margin-left: 10px;">${agent.success ? '✓' : '✗'}</span>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="agent-details" style="display: none; padding: 15px;">
                                <div class="agent-input" style="margin-bottom: 15px;">
                                    <h6>Input:</h6>
                                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(agent.input, null, 2)}</pre>
                                </div>
                                <div class="agent-output" style="margin-bottom: 15px;">
                                    <h6>Output:</h6>
                                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(agent.output, null, 2)}</pre>
                                </div>
                                <div class="agent-meta">
                                    <p><strong>Timestamp:</strong> ${agent.timestamp}</p>
                                    ${agent.error ? `<p style="color: #dc3545;"><strong>Error:</strong> ${agent.error}</p>` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    renderStateTransitions(transitions) {
        if (!transitions || transitions.length === 0) {
            return '<div class="no-transitions" style="margin-top: 20px;"><p><em>No state transitions recorded.</em></p></div>';
        }

        return `
            <div class="state-transitions" style="margin-top: 20px;">
                <h6><i class="fas fa-arrow-right"></i> State Transitions (${transitions.length})</h6>
                <div class="transitions-list">
                    ${transitions.map((transition, index) => `
                        <div class="state-transition" style="border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 10px;">
                            <div class="transition-header" style="background: #f8f9fa; padding: 10px; cursor: pointer; display: flex; justify-content: space-between; align-items: center;" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
                                <div>
                                    <strong>Transition ${index + 1}</strong> by ${transition.agent} at ${transition.timestamp}
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="transition-details" style="display: none; padding: 15px;">
                                <div class="from-state" style="margin-bottom: 15px;">
                                    <h6>From State:</h6>
                                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(transition.from_state, null, 2)}</pre>
                                </div>
                                <div class="to-state">
                                    <h6>To State:</h6>
                                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(transition.to_state, null, 2)}</pre>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    destroy() {
        // Clean up WebSocket connection when the modal is closed
        if (this.socket) {
            this.socket.close();
            this.socket = null;
            console.log('ContextPreview: WebSocket connection closed.');
        }
    }
}

// Export for global access
window.ContextPreview = ContextPreview;
