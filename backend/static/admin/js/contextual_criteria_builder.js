// ACTIVE_FILE - 29-05-2025
/**
 * Enhanced Contextual Criteria Builder
 *
 * This module provides an intuitive, feature-rich interface for building
 * contextual evaluation criteria with visual feedback and validation.
 */

class ContextualCriteriaBuilder {
    constructor() {
        this.container = null;
        this.data = {};
        this.presets = this.getPresets();

        this.init();
    }

    init() {
        this.createContainer();
        this.setupEventListeners();
    }

    createContainer() {
        const modal = document.getElementById('template-modal');
        if (!modal) return;

        // Remove existing container
        const existing = modal.querySelector('#contextual-criteria-builder');
        if (existing) existing.remove();

        this.container = document.createElement('div');
        this.container.id = 'contextual-tab-content';
        this.container.className = 'template-tab-content';
        this.container.innerHTML = this.getHTML();

        // Insert into modal form
        const form = modal.querySelector('#template-form');
        if (form) {
            form.appendChild(this.container);
        }
    }

    getHTML() {
        return `
            <div class="contextual-builder enhanced">
                <div class="builder-header">
                    <h4><i class="fas fa-brain"></i> Contextual Criteria Builder</h4>
                    <p class="help-text">Define how evaluation criteria adapt based on contextual variables like trust level, mood, and environment.</p>

                    <div class="builder-toolbar">
                        <div class="preset-selector">
                            <label for="criteria-preset">Quick Start Templates:</label>
                            <select id="criteria-preset" class="form-control">
                                <option value="">Choose a template...</option>
                                <option value="trust-basic">🛡️ Trust Level (Basic)</option>
                                <option value="trust-advanced">🛡️ Trust Level (Advanced)</option>
                                <option value="mood-support">❤️ Mood-Aware Support</option>
                                <option value="stress-adaptive">⚡ Stress-Adaptive</option>
                                <option value="comprehensive">🎯 Comprehensive</option>
                                <option value="coaching-focused">🎓 Coaching-Focused</option>
                                <option value="therapeutic">🌱 Therapeutic Support</option>
                            </select>
                            <button type="button" class="btn btn-sm btn-secondary" id="load-preset-btn">
                                <i class="fas fa-download"></i> Load
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" id="preview-preset-btn" title="Preview selected template">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <div class="builder-actions">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-all-btn" title="Clear all criteria">
                                <i class="fas fa-trash"></i> Clear All
                            </button>
                            <button type="button" class="btn btn-sm btn-info" id="validate-criteria-btn">
                                <i class="fas fa-check-circle"></i> Validate
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="sync-json-btn">
                                <i class="fas fa-sync"></i> Sync to JSON
                            </button>
                        </div>
                    </div>

                    <div class="preset-preview" id="preset-preview" style="display: none;">
                        <div class="preset-preview-content">
                            <h6>Template Preview</h6>
                            <div id="preset-preview-details"></div>
                            <button type="button" class="btn btn-xs btn-outline-secondary" id="close-preview-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="validation-status" id="criteria-validation-status"></div>

                <div class="smart-suggestions" id="smart-suggestions" style="display: none;">
                    <div class="suggestions-header">
                        <h6><i class="fas fa-lightbulb"></i> Smart Suggestions</h6>
                        <button type="button" class="btn btn-xs btn-outline-secondary" id="close-suggestions-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="suggestions-content" id="suggestions-content"></div>
                </div>

                <div class="variable-sections">
                    ${this.getTrustLevelSection()}
                    ${this.getMoodSection()}
                    ${this.getEnvironmentSection()}
                </div>

                <div class="coverage-indicator">
                    <h6><i class="fas fa-chart-pie"></i> Coverage Analysis</h6>
                    <div class="coverage-bars" id="coverage-bars">
                        <div class="coverage-item">
                            <span>Trust Level:</span>
                            <div class="progress">
                                <div class="progress-bar" id="trust-coverage" style="width: 0%"></div>
                            </div>
                            <span class="coverage-percentage" id="trust-percentage">0%</span>
                        </div>
                        <div class="coverage-item">
                            <span>Mood:</span>
                            <div class="progress">
                                <div class="progress-bar" id="mood-coverage" style="width: 0%"></div>
                            </div>
                            <span class="coverage-percentage" id="mood-percentage">0%</span>
                        </div>
                        <div class="coverage-item">
                            <span>Environment:</span>
                            <div class="progress">
                                <div class="progress-bar" id="env-coverage" style="width: 0%"></div>
                            </div>
                            <span class="coverage-percentage" id="env-percentage">0%</span>
                        </div>
                    </div>
                    <div class="coverage-summary" id="coverage-summary">
                        <small class="text-muted">Complete all sections for optimal contextual adaptation</small>
                    </div>
                </div>
            </div>
        `;
    }

    getTrustLevelSection() {
        return `
            <div class="variable-section enhanced" data-variable="trust_level">
                <div class="section-header">
                    <h5><i class="fas fa-shield-alt"></i> Trust Level Adaptations</h5>
                    <div class="section-controls">
                        <button type="button" class="btn btn-xs btn-outline-primary" id="trust-help-btn" title="Show trust level guidance">
                            <i class="fas fa-question-circle"></i> Help
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-success" id="trust-suggest-btn" title="Get smart suggestions">
                            <i class="fas fa-magic"></i> Suggest
                        </button>
                    </div>
                </div>

                <div class="trust-guidance" id="trust-guidance" style="display: none;">
                    <div class="guidance-content">
                        <h6>Trust Level Guidelines</h6>
                        <ul>
                            <li><strong>Foundation (0-39):</strong> New users, low confidence. Use simple, clear, reassuring language.</li>
                            <li><strong>Expansion (40-69):</strong> Growing trust. Encourage exploration with supportive guidance.</li>
                            <li><strong>Integration (70-100):</strong> High trust. Enable collaboration and advanced challenges.</li>
                        </ul>
                    </div>
                </div>

                <div class="range-visual">
                    <div class="range-bar interactive">
                        <div class="range-segment foundation" data-range="0-39" title="Foundation Phase: Building basic trust">
                            <span class="range-label">Foundation</span>
                            <span class="range-values">(0-39)</span>
                        </div>
                        <div class="range-segment expansion" data-range="40-69" title="Expansion Phase: Growing confidence">
                            <span class="range-label">Expansion</span>
                            <span class="range-values">(40-69)</span>
                        </div>
                        <div class="range-segment integration" data-range="70-100" title="Integration Phase: High trust collaboration">
                            <span class="range-label">Integration</span>
                            <span class="range-values">(70-100)</span>
                        </div>
                    </div>
                </div>

                <div class="range-definitions">
                    ${this.getRangeDefinition('0-39', 'Foundation Phase', 'Basic trust building - simple, clear, reassuring approach')}
                    ${this.getRangeDefinition('40-69', 'Expansion Phase', 'Growing confidence - encouraging, supportive guidance')}
                    ${this.getRangeDefinition('70-100', 'Integration Phase', 'High trust collaboration - empowering, challenging approach')}
                </div>
            </div>
        `;
    }

    getMoodSection() {
        return `
            <div class="variable-section enhanced" data-variable="mood">
                <div class="section-header">
                    <h5><i class="fas fa-heart"></i> Mood Adaptations</h5>
                    <div class="section-controls">
                        <button type="button" class="btn btn-xs btn-outline-primary" id="mood-help-btn" title="Show mood adaptation guidance">
                            <i class="fas fa-question-circle"></i> Help
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-success" id="mood-suggest-btn" title="Get mood-based suggestions">
                            <i class="fas fa-magic"></i> Suggest
                        </button>
                    </div>
                </div>

                <div class="mood-guidance" id="mood-guidance" style="display: none;">
                    <div class="guidance-content">
                        <h6>Mood Adaptation Guidelines</h6>
                        <p><strong>Valence:</strong> Emotional positivity/negativity (-1.0 to 1.0)</p>
                        <ul>
                            <li><strong>Negative Valence:</strong> Use gentle, understanding, patient approaches</li>
                            <li><strong>Positive Valence:</strong> Use enthusiastic, energetic, positive approaches</li>
                        </ul>
                        <p><strong>Arousal:</strong> Emotional activation level (-1.0 to 1.0)</p>
                        <ul>
                            <li><strong>Low Arousal:</strong> Calming, relaxation-focused criteria</li>
                            <li><strong>High Arousal:</strong> Stimulating, dynamic criteria</li>
                        </ul>
                    </div>
                </div>

                <div class="mood-visualization">
                    <div class="mood-space">
                        <div class="mood-grid interactive">
                            <div class="mood-quadrant q1" data-valence="positive" data-arousal="high" title="High Arousal + Positive Valence">
                                <span class="quadrant-label">Excited</span>
                                <span class="quadrant-coords">(+,+)</span>
                            </div>
                            <div class="mood-quadrant q2" data-valence="positive" data-arousal="low" title="Low Arousal + Positive Valence">
                                <span class="quadrant-label">Content</span>
                                <span class="quadrant-coords">(+,-)</span>
                            </div>
                            <div class="mood-quadrant q3" data-valence="negative" data-arousal="low" title="Low Arousal + Negative Valence">
                                <span class="quadrant-label">Sad</span>
                                <span class="quadrant-coords">(-,-)</span>
                            </div>
                            <div class="mood-quadrant q4" data-valence="negative" data-arousal="high" title="High Arousal + Negative Valence">
                                <span class="quadrant-label">Stressed</span>
                                <span class="quadrant-coords">(-,+)</span>
                            </div>
                        </div>
                        <div class="mood-axes">
                            <span class="axis-label valence-axis">Valence (Negative ← → Positive)</span>
                            <span class="axis-label arousal-axis">Arousal (Low ↑ High)</span>
                        </div>
                    </div>
                </div>

                <div class="mood-subsections">
                    <div class="subsection" data-subsection="valence">
                        <h6><i class="fas fa-smile"></i> Valence (Emotional Positivity)</h6>
                        <div class="range-definitions">
                            ${this.getRangeDefinition('-1.0-0.0', 'Negative Valence', 'Negative emotions - gentle, understanding approach')}
                            ${this.getRangeDefinition('0.0-1.0', 'Positive Valence', 'Positive emotions - enthusiastic, energetic approach')}
                        </div>
                    </div>

                    <div class="subsection" data-subsection="arousal">
                        <h6><i class="fas fa-bolt"></i> Arousal (Emotional Intensity)</h6>
                        <div class="range-definitions">
                            ${this.getRangeDefinition('-1.0-0.0', 'Low Arousal', 'Calm state - relaxing, soothing approach')}
                            ${this.getRangeDefinition('0.0-1.0', 'High Arousal', 'Excited state - dynamic, stimulating approach')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getEnvironmentSection() {
        return `
            <div class="variable-section enhanced" data-variable="environment">
                <div class="section-header">
                    <h5><i class="fas fa-thermometer-half"></i> Environment Adaptations</h5>
                    <div class="section-controls">
                        <button type="button" class="btn btn-xs btn-outline-primary" id="env-help-btn" title="Show environment adaptation guidance">
                            <i class="fas fa-question-circle"></i> Help
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-success" id="env-suggest-btn" title="Get environment-based suggestions">
                            <i class="fas fa-magic"></i> Suggest
                        </button>
                    </div>
                </div>

                <div class="env-guidance" id="env-guidance" style="display: none;">
                    <div class="guidance-content">
                        <h6>Environment Adaptation Guidelines</h6>
                        <div class="guidance-grid">
                            <div class="guidance-item">
                                <h7><i class="fas fa-exclamation-triangle"></i> Stress Level (0-100)</h7>
                                <ul>
                                    <li><strong>Low (0-30):</strong> Detailed, comprehensive approaches</li>
                                    <li><strong>Medium (31-70):</strong> Balanced, focused approaches</li>
                                    <li><strong>High (71-100):</strong> Concise, essential-only approaches</li>
                                </ul>
                            </div>
                            <div class="guidance-item">
                                <h7><i class="fas fa-clock"></i> Time Pressure (0-100)</h7>
                                <ul>
                                    <li><strong>Low (0-30):</strong> Long-term, detailed planning</li>
                                    <li><strong>Medium (31-70):</strong> Structured, efficient approaches</li>
                                    <li><strong>High (71-100):</strong> Quick, immediate action focus</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="env-visualization">
                    <div class="env-indicators enhanced">
                        <div class="indicator stress interactive" title="Stress Level Indicator">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Stress Level</span>
                            <div class="indicator-bar">
                                <div class="bar-segment low" title="Low Stress (0-30)">Low</div>
                                <div class="bar-segment medium" title="Medium Stress (31-70)">Med</div>
                                <div class="bar-segment high" title="High Stress (71-100)">High</div>
                            </div>
                        </div>
                        <div class="indicator time interactive" title="Time Pressure Indicator">
                            <i class="fas fa-clock"></i>
                            <span>Time Pressure</span>
                            <div class="indicator-bar">
                                <div class="bar-segment low" title="Low Pressure (0-30)">Low</div>
                                <div class="bar-segment medium" title="Medium Pressure (31-70)">Med</div>
                                <div class="bar-segment high" title="High Pressure (71-100)">High</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="env-subsections">
                    <div class="subsection" data-subsection="stress_level">
                        <h6><i class="fas fa-exclamation-triangle"></i> Stress Level</h6>
                        <div class="range-definitions">
                            ${this.getRangeDefinition('0-30', 'Low Stress', 'Calm environment - detailed, comprehensive approach')}
                            ${this.getRangeDefinition('31-70', 'Medium Stress', 'Moderate pressure - balanced, focused approach')}
                            ${this.getRangeDefinition('71-100', 'High Stress', 'High pressure - concise, essential-only approach')}
                        </div>
                    </div>

                    <div class="subsection" data-subsection="time_pressure">
                        <h6><i class="fas fa-clock"></i> Time Pressure</h6>
                        <div class="range-definitions">
                            ${this.getRangeDefinition('0-30', 'Low Pressure', 'Relaxed timing - long-term, detailed planning')}
                            ${this.getRangeDefinition('31-70', 'Medium Pressure', 'Moderate urgency - structured, efficient approach')}
                            ${this.getRangeDefinition('71-100', 'High Pressure', 'Urgent timing - quick, immediate action focus')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getRangeDefinition(range, label, description) {
        return `
            <div class="range-def enhanced" data-range="${range}">
                <div class="range-header">
                    <div class="range-title">
                        <label>${label}</label>
                        <span class="range-badge">${range}</span>
                    </div>
                    <span class="range-description">${description}</span>
                    <div class="range-actions">
                        <button type="button" class="btn btn-xs btn-outline-success add-criterion" title="Add Custom Criterion">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-info copy-range" title="Copy from another range">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-warning suggest-range" title="Get suggestions for this range">
                            <i class="fas fa-magic"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-danger clear-range" title="Clear Range">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="criteria-inputs enhanced">
                    ${this.getCriteriaInputs()}
                </div>
                <div class="range-status">
                    <span class="criteria-count">0 criteria defined</span>
                    <div class="completeness-indicator">
                        <div class="completeness-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
    }

    getCriteriaInputs() {
        const dimensions = [
            { name: 'Tone', icon: 'fas fa-volume-up', placeholder: 'e.g., Gentle, Encouraging, Professional' },
            { name: 'Content', icon: 'fas fa-file-alt', placeholder: 'e.g., Detailed, Focused, Essential' },
            { name: 'Approach', icon: 'fas fa-route', placeholder: 'e.g., Step-by-step, Collaborative, Direct' },
            { name: 'Structure', icon: 'fas fa-sitemap', placeholder: 'e.g., Organized, Flexible, Streamlined' },
            { name: 'Empathy', icon: 'fas fa-hand-holding-heart', placeholder: 'e.g., Compassionate, Validating, Patient' },
            { name: 'Guidance', icon: 'fas fa-compass', placeholder: 'e.g., Directive, Supportive, Exploratory' },
            { name: 'Engagement', icon: 'fas fa-comments', placeholder: 'e.g., Interactive, Participatory, Responsive' }
        ];

        return dimensions.map(dimension => `
            <div class="criteria-input-group enhanced">
                <div class="dimension-header">
                    <label><i class="${dimension.icon}"></i> ${dimension.name}:</label>
                    <button type="button" class="btn btn-xs btn-outline-secondary suggest-dimension"
                            data-dimension="${dimension.name}" title="Get suggestions for ${dimension.name}">
                        <i class="fas fa-lightbulb"></i>
                    </button>
                </div>
                <div class="input-container">
                    <input type="text" class="form-control criteria-input"
                           data-dimension="${dimension.name}"
                           placeholder="${dimension.placeholder}">
                    <div class="input-feedback">
                        <span class="char-count">0 characters</span>
                        <span class="criteria-count-indicator">0 criteria</span>
                    </div>
                    <div class="criteria-tags" data-dimension="${dimension.name}"></div>
                </div>
                <div class="dimension-suggestions" data-dimension="${dimension.name}" style="display: none;">
                    <div class="suggestions-list"></div>
                </div>
            </div>
        `).join('');
    }

    setupEventListeners() {
        if (!this.container) return;

        // Preset loading and preview
        const loadPresetBtn = this.container.querySelector('#load-preset-btn');
        if (loadPresetBtn) {
            loadPresetBtn.addEventListener('click', () => this.loadPreset());
        }

        const previewPresetBtn = this.container.querySelector('#preview-preset-btn');
        if (previewPresetBtn) {
            previewPresetBtn.addEventListener('click', () => this.previewPreset());
        }

        const closePreviewBtn = this.container.querySelector('#close-preview-btn');
        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', () => this.closePreview());
        }

        // Clear all
        const clearAllBtn = this.container.querySelector('#clear-all-btn');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => this.clearAll());
        }

        // Validation
        const validateBtn = this.container.querySelector('#validate-criteria-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validate());
        }

        // JSON sync
        const syncBtn = this.container.querySelector('#sync-json-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.syncToJSON());
        }

        // Help buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('#trust-help-btn')) {
                this.toggleGuidance('trust-guidance');
            } else if (e.target.closest('#mood-help-btn')) {
                this.toggleGuidance('mood-guidance');
            } else if (e.target.closest('#env-help-btn')) {
                this.toggleGuidance('env-guidance');
            }
        });

        // Suggestion buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('#trust-suggest-btn')) {
                this.showSmartSuggestions('trust_level');
            } else if (e.target.closest('#mood-suggest-btn')) {
                this.showSmartSuggestions('mood');
            } else if (e.target.closest('#env-suggest-btn')) {
                this.showSmartSuggestions('environment');
            } else if (e.target.closest('.suggest-dimension')) {
                const dimension = e.target.closest('.suggest-dimension').dataset.dimension;
                this.showDimensionSuggestions(dimension, e.target.closest('.criteria-input-group'));
            }
        });

        // Input changes with enhanced feedback
        this.container.addEventListener('input', (e) => {
            if (e.target.classList.contains('criteria-input')) {
                this.handleInputChange(e.target);
                this.updateInputFeedback(e.target);
            }
        });

        // Range actions
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.add-criterion')) {
                this.addCriterion(e.target.closest('.range-def'));
            } else if (e.target.closest('.clear-range')) {
                this.clearRange(e.target.closest('.range-def'));
            } else if (e.target.closest('.copy-range')) {
                this.copyRange(e.target.closest('.range-def'));
            } else if (e.target.closest('.suggest-range')) {
                this.suggestForRange(e.target.closest('.range-def'));
            }
        });

        // Interactive range segments
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.range-segment')) {
                this.highlightRange(e.target.closest('.range-segment'));
            } else if (e.target.closest('.mood-quadrant')) {
                this.highlightMoodQuadrant(e.target.closest('.mood-quadrant'));
            } else if (e.target.closest('.bar-segment')) {
                this.highlightBarSegment(e.target.closest('.bar-segment'));
            }
        });

        // Close suggestions
        const closeSuggestionsBtn = this.container.querySelector('#close-suggestions-btn');
        if (closeSuggestionsBtn) {
            closeSuggestionsBtn.addEventListener('click', () => this.closeSuggestions());
        }
    }

    handleInputChange(input) {
        const dimension = input.dataset.dimension;
        const tagsContainer = input.parentElement.querySelector(`[data-dimension="${dimension}"]`);

        if (tagsContainer) {
            this.updateTags(input.value, tagsContainer);
        }

        this.updateCoverage();
        this.updateRangeStatus(input.closest('.range-def'));
    }

    updateInputFeedback(input) {
        const inputContainer = input.closest('.input-container');
        const charCount = inputContainer.querySelector('.char-count');
        const criteriaCount = inputContainer.querySelector('.criteria-count-indicator');

        if (charCount) {
            charCount.textContent = `${input.value.length} characters`;
        }

        if (criteriaCount) {
            const criteria = input.value.split(',').map(c => c.trim()).filter(c => c);
            criteriaCount.textContent = `${criteria.length} criteria`;
        }
    }

    updateTags(value, container) {
        const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag);
        container.innerHTML = tags.map(tag =>
            `<span class="criteria-tag">${tag}<button type="button" class="tag-remove" onclick="this.parentElement.remove()">×</button></span>`
        ).join('');
    }

    updateRangeStatus(rangeElement) {
        if (!rangeElement) return;

        const inputs = rangeElement.querySelectorAll('.criteria-input');
        let totalCriteria = 0;
        let filledDimensions = 0;

        inputs.forEach(input => {
            const criteria = input.value.split(',').map(c => c.trim()).filter(c => c);
            totalCriteria += criteria.length;
            if (criteria.length > 0) filledDimensions++;
        });

        const statusElement = rangeElement.querySelector('.criteria-count');
        const completenessBar = rangeElement.querySelector('.completeness-bar');

        if (statusElement) {
            statusElement.textContent = `${totalCriteria} criteria defined`;
        }

        if (completenessBar) {
            const completeness = (filledDimensions / inputs.length) * 100;
            completenessBar.style.width = `${completeness}%`;
            completenessBar.style.backgroundColor = completeness === 100 ? '#28a745' :
                                                   completeness >= 50 ? '#ffc107' : '#dc3545';
        }
    }

    updateCoverage() {
        // Calculate coverage for each variable type
        const trustCoverage = this.calculateTrustCoverage();
        const moodCoverage = this.calculateMoodCoverage();
        const envCoverage = this.calculateEnvironmentCoverage();

        // Update progress bars and percentages
        const trustBar = document.getElementById('trust-coverage');
        const moodBar = document.getElementById('mood-coverage');
        const envBar = document.getElementById('env-coverage');

        const trustPercentage = document.getElementById('trust-percentage');
        const moodPercentage = document.getElementById('mood-percentage');
        const envPercentage = document.getElementById('env-percentage');

        if (trustBar) {
            trustBar.style.width = `${trustCoverage}%`;
            trustBar.style.backgroundColor = this.getCoverageColor(trustCoverage);
        }
        if (moodBar) {
            moodBar.style.width = `${moodCoverage}%`;
            moodBar.style.backgroundColor = this.getCoverageColor(moodCoverage);
        }
        if (envBar) {
            envBar.style.width = `${envCoverage}%`;
            envBar.style.backgroundColor = this.getCoverageColor(envCoverage);
        }

        if (trustPercentage) trustPercentage.textContent = `${trustCoverage}%`;
        if (moodPercentage) moodPercentage.textContent = `${moodCoverage}%`;
        if (envPercentage) envPercentage.textContent = `${envCoverage}%`;

        // Update overall summary
        this.updateCoverageSummary(trustCoverage, moodCoverage, envCoverage);
    }

    getCoverageColor(percentage) {
        if (percentage >= 80) return '#28a745'; // Green
        if (percentage >= 50) return '#ffc107'; // Yellow
        if (percentage >= 25) return '#fd7e14'; // Orange
        return '#dc3545'; // Red
    }

    updateCoverageSummary(trust, mood, env) {
        const summaryElement = document.getElementById('coverage-summary');
        if (!summaryElement) return;

        const overall = Math.round((trust + mood + env) / 3);
        let message = '';

        if (overall >= 80) {
            message = '<small class="text-success"><i class="fas fa-check-circle"></i> Excellent coverage! Your template is well-defined.</small>';
        } else if (overall >= 50) {
            message = '<small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Good progress. Consider completing remaining sections.</small>';
        } else if (overall >= 25) {
            message = '<small class="text-info"><i class="fas fa-info-circle"></i> Getting started. Add more criteria for better adaptation.</small>';
        } else {
            message = '<small class="text-muted"><i class="fas fa-lightbulb"></i> Complete all sections for optimal contextual adaptation.</small>';
        }

        summaryElement.innerHTML = message;
    }

    calculateTrustCoverage() {
        const trustSection = this.container.querySelector('[data-variable="trust_level"]');
        if (!trustSection) return 0;

        const ranges = trustSection.querySelectorAll('.range-def');
        let filledRanges = 0;

        ranges.forEach(range => {
            const inputs = range.querySelectorAll('.criteria-input');
            const hasContent = Array.from(inputs).some(input => input.value.trim());
            if (hasContent) filledRanges++;
        });

        return Math.round((filledRanges / ranges.length) * 100);
    }

    calculateMoodCoverage() {
        const moodSection = this.container.querySelector('[data-variable="mood"]');
        if (!moodSection) return 0;

        const ranges = moodSection.querySelectorAll('.range-def');
        let filledRanges = 0;

        ranges.forEach(range => {
            const inputs = range.querySelectorAll('.criteria-input');
            const hasContent = Array.from(inputs).some(input => input.value.trim());
            if (hasContent) filledRanges++;
        });

        return Math.round((filledRanges / Math.max(ranges.length, 1)) * 100);
    }

    calculateEnvironmentCoverage() {
        const envSection = this.container.querySelector('[data-variable="environment"]');
        if (!envSection) return 0;

        const ranges = envSection.querySelectorAll('.range-def');
        let filledRanges = 0;

        ranges.forEach(range => {
            const inputs = range.querySelectorAll('.criteria-input');
            const hasContent = Array.from(inputs).some(input => input.value.trim());
            if (hasContent) filledRanges++;
        });

        return Math.round((filledRanges / Math.max(ranges.length, 1)) * 100);
    }

    getPresets() {
        return {
            'trust-basic': {
                name: 'Trust Level (Basic)',
                description: 'Simple trust-based adaptations for new users',
                data: {
                    trust_level: {
                        "0-39": {
                            "Tone": ["Simple", "Clear", "Reassuring"],
                            "Content": ["Safe options", "Basic guidance"]
                        },
                        "40-69": {
                            "Tone": ["Encouraging", "Supportive"],
                            "Content": ["Balanced options", "Moderate challenges"]
                        },
                        "70-100": {
                            "Tone": ["Collaborative", "Empowering"],
                            "Content": ["Advanced options", "Creative challenges"]
                        }
                    }
                }
            },
            'trust-advanced': {
                name: 'Trust Level (Advanced)',
                description: 'Comprehensive trust-based adaptations with detailed criteria',
                data: {
                    trust_level: {
                        "0-39": {
                            "Tone": ["Simple", "Clear", "Reassuring", "Non-threatening"],
                            "Content": ["Safe options", "Basic guidance", "Low-risk activities", "Familiar concepts"],
                            "Approach": ["Step-by-step", "Basic", "Structured", "Predictable"],
                            "Structure": ["Linear", "Simple", "Clear hierarchy"]
                        },
                        "40-69": {
                            "Tone": ["Encouraging", "Supportive", "Motivating", "Confident"],
                            "Content": ["Balanced options", "Moderate challenges", "Growth opportunities"],
                            "Approach": ["Collaborative", "Guided", "Interactive", "Exploratory"],
                            "Structure": ["Flexible", "Adaptive", "User-driven"]
                        },
                        "70-100": {
                            "Tone": ["Collaborative", "Empowering", "Challenging", "Inspiring"],
                            "Content": ["Advanced options", "Creative challenges", "Complex scenarios"],
                            "Approach": ["Advanced", "Independent", "Innovative", "Experimental"],
                            "Structure": ["Open-ended", "Customizable", "Dynamic"]
                        }
                    }
                }
            },
            'mood-support': {
                name: 'Mood-Aware Support',
                description: 'Emotional state-based adaptations for supportive interactions',
                data: {
                    mood: {
                        valence: {
                            "-1.0-0.0": {
                                "Tone": ["Gentle", "Understanding", "Patient", "Compassionate"],
                                "Approach": ["Supportive", "Careful", "Nurturing", "Validating"],
                                "Content": ["Comforting", "Reassuring", "Stabilizing"]
                            },
                            "0.0-1.0": {
                                "Tone": ["Enthusiastic", "Energetic", "Positive", "Uplifting"],
                                "Approach": ["Dynamic", "Engaging", "Motivating", "Inspiring"],
                                "Content": ["Exciting", "Optimistic", "Growth-oriented"]
                            }
                        },
                        arousal: {
                            "-1.0-0.0": {
                                "Tone": ["Calm", "Soothing", "Peaceful"],
                                "Approach": ["Relaxing", "Meditative", "Gentle"]
                            },
                            "0.0-1.0": {
                                "Tone": ["Stimulating", "Dynamic", "Energizing"],
                                "Approach": ["Active", "Engaging", "Intensive"]
                            }
                        }
                    }
                }
            },
            'stress-adaptive': {
                name: 'Stress-Adaptive',
                description: 'Environment-based adaptations for different stress levels',
                data: {
                    environment: {
                        stress_level: {
                            "0-30": {
                                "Approach": ["Detailed", "Comprehensive", "Thorough", "Exploratory"],
                                "Content": ["In-depth", "Complete", "Nuanced"],
                                "Structure": ["Complex", "Multi-layered", "Rich"]
                            },
                            "31-70": {
                                "Approach": ["Balanced", "Focused", "Efficient", "Practical"],
                                "Content": ["Relevant", "Targeted", "Actionable"],
                                "Structure": ["Organized", "Clear", "Logical"]
                            },
                            "71-100": {
                                "Approach": ["Concise", "Essential-only", "Direct", "Immediate"],
                                "Content": ["Critical", "Urgent", "Key points"],
                                "Structure": ["Minimal", "Streamlined", "Quick"]
                            }
                        }
                    }
                }
            },
            'coaching-focused': {
                name: 'Coaching-Focused',
                description: 'Professional coaching adaptations based on client readiness',
                data: {
                    trust_level: {
                        "0-39": {
                            "Tone": ["Professional", "Warm", "Encouraging"],
                            "Approach": ["Supportive", "Structured", "Goal-oriented"],
                            "Content": ["Foundation-building", "Skill development"]
                        },
                        "70-100": {
                            "Tone": ["Challenging", "Direct", "Empowering"],
                            "Approach": ["Transformational", "Deep-diving", "Breakthrough-focused"],
                            "Content": ["Advanced strategies", "Complex challenges"]
                        }
                    }
                }
            },
            'therapeutic': {
                name: 'Therapeutic Support',
                description: 'Mental health and wellness-focused adaptations',
                data: {
                    mood: {
                        valence: {
                            "-1.0-0.0": {
                                "Tone": ["Gentle", "Non-judgmental", "Validating"],
                                "Approach": ["Trauma-informed", "Safety-focused", "Stabilizing"],
                                "Content": ["Coping strategies", "Self-care", "Grounding techniques"]
                            }
                        }
                    },
                    environment: {
                        stress_level: {
                            "71-100": {
                                "Approach": ["Crisis-informed", "Immediate support", "Safety-first"],
                                "Content": ["Emergency resources", "Immediate coping"]
                            }
                        }
                    }
                }
            },
            'comprehensive': {
                name: 'Comprehensive',
                description: 'Full-featured template covering all contextual variables',
                data: {
                    trust_level: {
                        "0-39": {
                            "Tone": ["Simple", "Clear", "Reassuring"],
                            "Content": ["Safe options", "Low-risk activities"],
                            "Approach": ["Step-by-step", "Basic"]
                        },
                        "40-69": {
                            "Tone": ["Encouraging", "Supportive"],
                            "Content": ["Balanced options", "Some stretch goals"],
                            "Approach": ["Collaborative", "Guided"]
                        },
                        "70-100": {
                            "Tone": ["Collaborative", "Empowering"],
                            "Content": ["Ambitious goals", "Creative challenges"],
                            "Approach": ["Advanced", "Independent"]
                        }
                    },
                    mood: {
                        valence: {
                            "-1.0-0.0": {
                                "Tone": ["Gentle", "Understanding", "Patient"]
                            },
                            "0.0-1.0": {
                                "Tone": ["Enthusiastic", "Energetic", "Positive"]
                            }
                        }
                    },
                    environment: {
                        stress_level: {
                            "0-30": {
                                "Approach": ["Detailed", "Comprehensive"]
                            },
                            "71-100": {
                                "Approach": ["Concise", "Essential-only"]
                            }
                        }
                    }
                }
            }
        };
    }

    loadPreset() {
        const select = this.container.querySelector('#criteria-preset');
        const presetKey = select.value;

        if (!presetKey || !this.presets[presetKey]) return;

        this.loadData(this.presets[presetKey].data);
        this.syncToJSON();

        // Show success message
        const status = this.container.querySelector('#criteria-validation-status');
        if (status) {
            status.innerHTML = `<div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                "${this.presets[presetKey].name}" template loaded successfully!
            </div>`;
            setTimeout(() => status.innerHTML = '', 3000);
        }
    }

    previewPreset() {
        const select = this.container.querySelector('#criteria-preset');
        const presetKey = select.value;

        if (!presetKey || !this.presets[presetKey]) return;

        const preset = this.presets[presetKey];
        const previewContainer = this.container.querySelector('#preset-preview');
        const previewDetails = this.container.querySelector('#preset-preview-details');

        if (previewContainer && previewDetails) {
            previewDetails.innerHTML = `
                <h6>${preset.name}</h6>
                <p>${preset.description}</p>
                <div class="preset-stats">
                    <small class="text-muted">
                        Variables: ${Object.keys(preset.data).join(', ')} |
                        Total ranges: ${this.countPresetRanges(preset.data)}
                    </small>
                </div>
            `;
            previewContainer.style.display = 'block';
        }
    }

    closePreview() {
        const previewContainer = this.container.querySelector('#preset-preview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    countPresetRanges(data) {
        let count = 0;
        Object.values(data).forEach(variable => {
            if (typeof variable === 'object') {
                Object.keys(variable).forEach(key => {
                    if (typeof variable[key] === 'object') {
                        count += Object.keys(variable[key]).length;
                    }
                });
            }
        });
        return count;
    }

    clearAll() {
        if (confirm('Are you sure you want to clear all criteria? This action cannot be undone.')) {
            this.container.querySelectorAll('.criteria-input').forEach(input => {
                input.value = '';
                this.handleInputChange(input);
            });
            this.updateCoverage();

            const status = this.container.querySelector('#criteria-validation-status');
            if (status) {
                status.innerHTML = '<div class="alert alert-info">All criteria cleared.</div>';
                setTimeout(() => status.innerHTML = '', 2000);
            }
        }
    }

    toggleGuidance(guidanceId) {
        const guidance = this.container.querySelector(`#${guidanceId}`);
        if (guidance) {
            guidance.style.display = guidance.style.display === 'none' ? 'block' : 'none';
        }
    }

    loadData(data) {
        this.data = data;
        this.populateFromData();
        this.updateCoverage();
    }

    populateFromData() {
        // Clear all inputs first
        this.container.querySelectorAll('.criteria-input').forEach(input => {
            input.value = '';
            this.handleInputChange(input);
        });

        // Populate trust level
        if (this.data.trust_level) {
            Object.entries(this.data.trust_level).forEach(([range, criteria]) => {
                this.populateRange('trust_level', range, criteria);
            });
        }

        // Populate mood
        if (this.data.mood) {
            if (this.data.mood.valence) {
                Object.entries(this.data.mood.valence).forEach(([range, criteria]) => {
                    this.populateRange('mood', range, criteria, 'valence');
                });
            }
            if (this.data.mood.arousal) {
                Object.entries(this.data.mood.arousal).forEach(([range, criteria]) => {
                    this.populateRange('mood', range, criteria, 'arousal');
                });
            }
        }

        // Populate environment
        if (this.data.environment) {
            if (this.data.environment.stress_level) {
                Object.entries(this.data.environment.stress_level).forEach(([range, criteria]) => {
                    this.populateRange('environment', range, criteria, 'stress_level');
                });
            }
            if (this.data.environment.time_pressure) {
                Object.entries(this.data.environment.time_pressure).forEach(([range, criteria]) => {
                    this.populateRange('environment', range, criteria, 'time_pressure');
                });
            }
        }
    }

    populateRange(variable, range, criteria, subsection = null) {
        let selector = `[data-variable="${variable}"]`;
        if (subsection) {
            selector += ` [data-subsection="${subsection}"]`;
        }
        selector += ` [data-range="${range}"]`;

        const rangeContainer = this.container.querySelector(selector);
        if (!rangeContainer) return;

        Object.entries(criteria).forEach(([dimension, values]) => {
            const input = rangeContainer.querySelector(`[data-dimension="${dimension}"]`);
            if (input && Array.isArray(values)) {
                input.value = values.join(', ');
                this.handleInputChange(input);
            }
        });
    }

    collectData() {
        const data = {};

        // Collect trust level data
        const trustSection = this.container.querySelector('[data-variable="trust_level"]');
        if (trustSection) {
            data.trust_level = this.collectSectionData(trustSection);
        }

        // Collect mood data
        const moodSection = this.container.querySelector('[data-variable="mood"]');
        if (moodSection) {
            const valenceData = this.collectSubsectionData(moodSection, 'valence');
            if (Object.keys(valenceData).length > 0) {
                data.mood = { valence: valenceData };
            }
        }

        // Collect environment data
        const envSection = this.container.querySelector('[data-variable="environment"]');
        if (envSection) {
            const stressData = this.collectSubsectionData(envSection, 'stress_level');
            if (Object.keys(stressData).length > 0) {
                data.environment = { stress_level: stressData };
            }
        }

        return data;
    }

    collectSectionData(section) {
        const data = {};
        const ranges = section.querySelectorAll('.range-def');

        ranges.forEach(range => {
            const rangeKey = range.dataset.range;
            const criteria = {};

            range.querySelectorAll('.criteria-input').forEach(input => {
                const dimension = input.dataset.dimension;
                const value = input.value.trim();
                if (value) {
                    criteria[dimension] = value.split(',').map(v => v.trim()).filter(v => v);
                }
            });

            if (Object.keys(criteria).length > 0) {
                data[rangeKey] = criteria;
            }
        });

        return data;
    }

    collectSubsectionData(section, subsectionName) {
        const subsection = section.querySelector(`[data-subsection="${subsectionName}"]`);
        if (!subsection) return {};

        return this.collectSectionData(subsection);
    }

    validate() {
        const data = this.collectData();
        const errors = [];

        // Check if at least one variable is defined
        if (!data.trust_level && !data.mood && !data.environment) {
            errors.push('At least one contextual variable must be defined');
        }

        // Validate trust level ranges
        if (data.trust_level) {
            const ranges = Object.keys(data.trust_level);
            const expectedRanges = ['0-39', '40-69', '70-100'];
            const missingRanges = expectedRanges.filter(r => !ranges.includes(r));
            if (missingRanges.length === expectedRanges.length) {
                errors.push('Trust level should have at least one range defined');
            }
        }

        // Display validation results
        const status = this.container.querySelector('#criteria-validation-status');
        if (status) {
            if (errors.length > 0) {
                status.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>Validation Issues:</strong>
                        <ul>${errors.map(e => `<li>${e}</li>`).join('')}</ul>
                    </div>
                `;
            } else {
                status.innerHTML = '<div class="alert alert-success">Contextual criteria are valid!</div>';
            }
        }

        return errors.length === 0;
    }

    syncToJSON() {
        const data = this.collectData();
        const textarea = document.getElementById('template-contextual-criteria');
        if (textarea) {
            textarea.value = JSON.stringify(data, null, 2);
        }

        // Show sync confirmation
        const status = this.container.querySelector('#criteria-validation-status');
        if (status) {
            status.innerHTML = '<div class="alert alert-info">Synced to JSON successfully!</div>';
            setTimeout(() => status.innerHTML = '', 2000);
        }
    }

    refresh() {
        this.updateCoverage();

        // Try to load data from JSON textarea
        const textarea = document.getElementById('template-contextual-criteria');
        if (textarea && textarea.value.trim()) {
            try {
                const data = JSON.parse(textarea.value);
                this.loadData(data);
            } catch (e) {
                console.warn('Could not parse contextual criteria JSON:', e);
            }
        }
    }

    addCriterion(rangeElement) {
        // This could be expanded to add custom criterion dimensions
        console.log('Add criterion functionality can be expanded');
    }

    clearRange(rangeElement) {
        const inputs = rangeElement.querySelectorAll('.criteria-input');
        inputs.forEach(input => {
            input.value = '';
            this.handleInputChange(input);
        });
        this.updateCoverage();
    }

    copyRange(rangeElement) {
        const sourceRange = rangeElement.dataset.range;
        const variable = rangeElement.closest('[data-variable]').dataset.variable;

        // Show a simple prompt for now - could be enhanced with a modal
        const targetRange = prompt(`Copy criteria from ${sourceRange} to which range?`);
        if (!targetRange) return;

        const targetElement = this.container.querySelector(`[data-variable="${variable}"] [data-range="${targetRange}"]`);
        if (!targetElement) {
            alert('Target range not found');
            return;
        }

        // Copy values
        const sourceInputs = rangeElement.querySelectorAll('.criteria-input');
        const targetInputs = targetElement.querySelectorAll('.criteria-input');

        sourceInputs.forEach((sourceInput, index) => {
            if (targetInputs[index]) {
                targetInputs[index].value = sourceInput.value;
                this.handleInputChange(targetInputs[index]);
            }
        });
    }

    suggestForRange(rangeElement) {
        const range = rangeElement.dataset.range;
        const variable = rangeElement.closest('[data-variable]').dataset.variable;

        // Simple suggestions based on range and variable
        const suggestions = this.getRangeSuggestions(variable, range);

        if (suggestions.length > 0) {
            const suggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
            const emptyInput = rangeElement.querySelector('.criteria-input[value=""]');

            if (emptyInput) {
                emptyInput.value = suggestion.join(', ');
                this.handleInputChange(emptyInput);
            }
        }
    }

    getRangeSuggestions(variable, range) {
        const suggestions = {
            trust_level: {
                '0-39': {
                    Tone: ['Gentle', 'Patient', 'Clear', 'Simple'],
                    Content: ['Basic', 'Safe', 'Familiar', 'Low-risk'],
                    Approach: ['Structured', 'Predictable', 'Step-by-step'],
                    Structure: ['Linear', 'Simple', 'Organized']
                },
                '40-69': {
                    Tone: ['Encouraging', 'Supportive', 'Motivating'],
                    Content: ['Balanced', 'Growth-oriented', 'Moderate'],
                    Approach: ['Collaborative', 'Interactive', 'Guided'],
                    Structure: ['Flexible', 'Adaptive', 'User-driven']
                },
                '70-100': {
                    Tone: ['Empowering', 'Challenging', 'Inspiring'],
                    Content: ['Advanced', 'Complex', 'Creative'],
                    Approach: ['Independent', 'Innovative', 'Experimental'],
                    Structure: ['Open-ended', 'Customizable', 'Dynamic']
                }
            }
        };

        return Object.values(suggestions[variable]?.[range] || {});
    }

    showSmartSuggestions(variableType) {
        const suggestionsContainer = this.container.querySelector('#smart-suggestions');
        const suggestionsContent = this.container.querySelector('#suggestions-content');

        if (!suggestionsContainer || !suggestionsContent) return;

        const suggestions = this.getSmartSuggestions(variableType);

        suggestionsContent.innerHTML = `
            <div class="suggestions-grid">
                ${suggestions.map(suggestion => `
                    <div class="suggestion-item" data-variable="${variableType}" data-range="${suggestion.range}">
                        <h7>${suggestion.title}</h7>
                        <p>${suggestion.description}</p>
                        <button type="button" class="btn btn-xs btn-primary apply-suggestion">Apply</button>
                    </div>
                `).join('')}
            </div>
        `;

        suggestionsContainer.style.display = 'block';

        // Add event listeners for apply buttons
        suggestionsContent.querySelectorAll('.apply-suggestion').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const item = e.target.closest('.suggestion-item');
                this.applySuggestion(item.dataset.variable, item.dataset.range);
            });
        });
    }

    getSmartSuggestions(variableType) {
        // Return contextual suggestions based on variable type
        const suggestions = {
            trust_level: [
                {
                    range: '0-39',
                    title: 'Foundation Phase',
                    description: 'Focus on building basic trust with simple, clear communication'
                },
                {
                    range: '40-69',
                    title: 'Growth Phase',
                    description: 'Encourage exploration with supportive, collaborative approaches'
                }
            ],
            mood: [
                {
                    range: 'negative',
                    title: 'Supportive Approach',
                    description: 'Use gentle, understanding tone for negative emotional states'
                }
            ],
            environment: [
                {
                    range: 'high-stress',
                    title: 'Stress Management',
                    description: 'Provide concise, essential information for high-stress situations'
                }
            ]
        };

        return suggestions[variableType] || [];
    }

    applySuggestion(variable, range) {
        // Apply the suggestion to the appropriate range
        console.log(`Applying suggestion for ${variable} range ${range}`);
    }

    closeSuggestions() {
        const suggestionsContainer = this.container.querySelector('#smart-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    showDimensionSuggestions(dimension, inputGroup) {
        const suggestionsContainer = inputGroup.querySelector('.dimension-suggestions');
        const suggestionsList = inputGroup.querySelector('.suggestions-list');

        if (!suggestionsContainer || !suggestionsList) return;

        const suggestions = this.getDimensionSuggestions(dimension);

        suggestionsList.innerHTML = suggestions.map(suggestion =>
            `<span class="suggestion-tag" onclick="this.closest('.criteria-input-group').querySelector('.criteria-input').value += '${suggestion}, '">${suggestion}</span>`
        ).join('');

        suggestionsContainer.style.display = 'block';
    }

    getDimensionSuggestions(dimension) {
        const suggestions = {
            Tone: ['Professional', 'Friendly', 'Encouraging', 'Supportive', 'Clear', 'Gentle'],
            Content: ['Detailed', 'Focused', 'Comprehensive', 'Essential', 'Practical', 'Actionable'],
            Approach: ['Collaborative', 'Structured', 'Flexible', 'Direct', 'Supportive', 'Interactive'],
            Structure: ['Organized', 'Linear', 'Flexible', 'Hierarchical', 'Modular', 'Adaptive']
        };

        return suggestions[dimension] || [];
    }

    highlightRange(rangeSegment) {
        // Remove previous highlights
        this.container.querySelectorAll('.range-segment').forEach(segment => {
            segment.classList.remove('highlighted');
        });

        // Highlight selected segment
        rangeSegment.classList.add('highlighted');

        // Scroll to corresponding range definition
        const range = rangeSegment.dataset.range;
        const rangeDefinition = this.container.querySelector(`[data-range="${range}"]`);
        if (rangeDefinition) {
            rangeDefinition.scrollIntoView({ behavior: 'smooth', block: 'center' });
            rangeDefinition.classList.add('highlighted');
            setTimeout(() => rangeDefinition.classList.remove('highlighted'), 2000);
        }
    }

    highlightMoodQuadrant(quadrant) {
        // Similar highlighting for mood quadrants
        this.container.querySelectorAll('.mood-quadrant').forEach(q => {
            q.classList.remove('highlighted');
        });
        quadrant.classList.add('highlighted');
    }

    highlightBarSegment(segment) {
        // Similar highlighting for environment bar segments
        const parent = segment.closest('.indicator-bar');
        if (parent) {
            parent.querySelectorAll('.bar-segment').forEach(s => {
                s.classList.remove('highlighted');
            });
            segment.classList.add('highlighted');
        }
    }
}

// Export for global access
window.ContextualCriteriaBuilder = ContextualCriteriaBuilder;
