// ACTIVE_FILE - 29-05-2025
/**
 * Evaluation Template Modal Management
 *
 * This module handles the main evaluation template modal functionality,
 * including tab navigation, form management, and high-level UI interactions.
 */

class EvaluationTemplateModal {
    constructor() {
        this.modal = null;
        this.currentTab = 'basic';
        this.templateData = {};
        this.isEditMode = false;

        this.init();
    }

    init() {
        this.modal = document.getElementById('template-modal');
        if (!this.modal) return;

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('template-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSave(e));
        }

        // Close modal events
        this.modal.querySelectorAll('.close, .close-modal').forEach(el => {
            el.addEventListener('click', () => this.close());
        });

        // Click outside to close
        window.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.close();
            }
        });
    }

    show(templateId = null) {
        console.log(`EvaluationTemplateModal.show() called with templateId: ${templateId}`);
        if (!this.modal) {
            console.error('EvaluationTemplateModal.show(): Modal element not found.');
            return;
        }

        this.isEditMode = !!templateId;

        if (templateId) {
            this.loadTemplate(templateId);
        } else {
            this.resetForm();
            window.TemplateModalUtils.setDefaultValues();
        }

        this.initializeTabs();
        this.modal.style.display = 'block';
    }

    close() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
    }

    resetForm() {
        const form = document.getElementById('template-form');
        if (form) form.reset();

        document.getElementById('template-modal-title').textContent = 'Create New Evaluation Template';
        document.getElementById('template-id').value = '';

        this.templateData = {};
        this.currentTab = 'basic';
    }

    initializeTabs() {
        // Create tab navigation if it doesn't exist
        let tabNav = this.modal.querySelector('.template-tab-nav');
        if (!tabNav) {
            tabNav = document.createElement('div');
            tabNav.className = 'template-tab-nav';
            tabNav.innerHTML = `
                <button type="button" class="template-tab-btn active" data-tab="basic">
                    <i class="fas fa-info-circle"></i> Basic Info
                </button>
                <button type="button" class="template-tab-btn" data-tab="criteria">
                    <i class="fas fa-list"></i> Base Criteria
                </button>
                <button type="button" class="template-tab-btn" data-tab="contextual">
                    <i class="fas fa-brain"></i> Contextual Criteria
                </button>
                <button type="button" class="template-tab-btn" data-tab="variables">
                    <i class="fas fa-sliders-h"></i> Variable Ranges
                </button>
                <button type="button" class="template-tab-btn" data-tab="preview">
                    <i class="fas fa-eye"></i> Preview & Test
                </button>
            `;

            // Insert after the modal title
            const title = this.modal.querySelector('#template-modal-title');
            if (title) {
                title.parentNode.insertBefore(tabNav, title.nextSibling);
            }
        }

        // Add tab click handlers
        tabNav.querySelectorAll('.template-tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(btn.dataset.tab);
            });
        });

        // Initialize with basic tab active
        this.switchTab('basic');

        // Initialize specialized builders
        if (window.ContextualCriteriaBuilder) {
            window.contextualCriteriaBuilder = new window.ContextualCriteriaBuilder();
        }

        // Initialize help system for this modal
        this.initializeHelpSystem();

        if (window.VariableRangesBuilder) {
            window.variableRangesBuilder = new window.VariableRangesBuilder();
        }

        if (window.ContextPreview) {
            window.contextPreview = new window.ContextPreview();
        }
    }

    switchTab(tabName) {
        if (!this.modal) return;

        this.currentTab = tabName;

        // Update tab buttons
        this.modal.querySelectorAll('.template-tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Show/hide form sections
        const sections = {
            'basic': ['template-name', 'template-description', 'template-workflow-type-select', 'template-category-select', 'template-is-active'],
            'criteria': ['template-criteria'],
            'contextual': ['template-contextual-criteria'],
            'variables': ['template-variable-ranges'],
            'preview': ['context-preview-container']
        };

        // Hide all form groups first
        this.modal.querySelectorAll('.form-group').forEach(group => {
            group.style.display = 'none';
        });

        // Hide all custom tab content containers first
        this.modal.querySelectorAll('.template-tab-content').forEach(container => {
            container.style.display = 'none';
        });

        // Show relevant form groups for current tab
        if (sections[tabName]) {
            sections[tabName].forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        formGroup.style.display = 'block';
                    }
                }
            });
        }

        // Special handling for custom containers based on tab name
        const customContainers = {
            'contextual': 'contextual-tab-content',
            'variables': 'variable-ranges-builder',
            'preview': 'context-preview-container'
        };

        if (customContainers[tabName]) {
            const container = this.modal.querySelector(`#${customContainers[tabName]}`);
            if (container) {
                container.style.display = 'block';
            }
        }

        // Trigger tab-specific initialization
        this.onTabSwitch(tabName);
    }

    onTabSwitch(tabName) {
        switch (tabName) {
            case 'contextual':
                if (window.contextualCriteriaBuilder) {
                    window.contextualCriteriaBuilder.refresh();
                }
                // Refresh help icons for contextual tab
                if (window.helpSystem) {
                    setTimeout(() => {
                        window.helpSystem.addContextualHelpIcons();
                    }, 200);
                }
                break;
            case 'variables':
                if (window.variableRangesBuilder) {
                    window.variableRangesBuilder.refresh();
                }
                break;
            case 'preview':
                if (window.contextPreview) {
                    window.contextPreview.refresh();
                }
                break;
        }
    }

    initializeHelpSystem() {
        // Wait for help system to be available
        if (window.helpSystem) {
            // Initialize help icons for basic form elements
            setTimeout(() => {
                window.helpSystem.initializeHelpIcons();
            }, 100);
        } else {
            // Retry after a short delay if help system isn't ready
            setTimeout(() => {
                this.initializeHelpSystem();
            }, 200);
        }
    }

    async loadTemplate(templateId) {
        try {
            const data = await window.TemplateModalUtils.fetchTemplate(templateId);
            this.templateData = data;

            window.TemplateModalUtils.populateForm(this.templateData);

            document.getElementById('template-modal-title').textContent = 'Edit Evaluation Template';

        } catch (error) {
            console.error('Error loading template:', error);
            if (window.showError) {
                window.showError(`Error loading template: ${error.message}`);
            }
        }
    }

    async handleSave(event) {
        event.preventDefault();

        try {
            const templateData = window.TemplateModalUtils.collectFormData();

            // Validate data
            const validationErrors = window.TemplateModalUtils.validateTemplateData(templateData);
            if (validationErrors.length > 0) {
                if (window.showError) {
                    window.showError(`Validation errors: ${validationErrors.join(', ')}`, 'template-error-message');
                }
                return;
            }

            // Save template
            await window.TemplateModalUtils.saveTemplate(templateData, this.isEditMode);

            // Success
            this.close();

            if (window.showSuccess) {
                window.showSuccess(this.isEditMode ? 'Template updated successfully' : 'Template created successfully');
            }

            if (window.loadTemplates) {
                window.loadTemplates();
            }

        } catch (error) {
            console.error('Error saving template:', error);
            if (window.showError) {
                window.showError(`Error saving template: ${error.message}`, 'template-error-message');
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.evaluationTemplateModal = new EvaluationTemplateModal();
});

// Export for global access
window.EvaluationTemplateModal = EvaluationTemplateModal;
