// ACTIVE_FILE - 29-05-2025
/**
 * Help System for Evaluation Template Modal
 *
 * This module provides contextual help functionality with sub-modals that display
 * relevant documentation content when users click help icons.
 */

class HelpSystem {
    constructor() {
        this.helpModal = null;
        this.currentHelpId = null;
        this.init();
    }

    init() {
        this.createHelpModal();
        this.setupEventListeners();
    }

    createHelpModal() {
        // Remove existing help modal if it exists
        const existing = document.getElementById('help-modal');
        if (existing) existing.remove();

        // Create help modal HTML
        this.helpModal = document.createElement('div');
        this.helpModal.id = 'help-modal';
        this.helpModal.className = 'help-modal';
        this.helpModal.innerHTML = `
            <div class="help-modal-content">
                <div class="help-modal-header">
                    <h4 id="help-modal-title">Help</h4>
                    <button type="button" class="help-modal-close" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="help-modal-body" id="help-modal-body">
                    <!-- Help content will be inserted here -->
                </div>
            </div>
        `;

        // Append to body
        document.body.appendChild(this.helpModal);
    }

    setupEventListeners() {
        // Close modal when clicking outside or on close button
        this.helpModal.addEventListener('click', (e) => {
            if (e.target === this.helpModal || e.target.closest('.help-modal-close')) {
                this.hideHelp();
            }
        });

        // Prevent modal content clicks from closing the modal
        this.helpModal.querySelector('.help-modal-content').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.helpModal.style.display === 'block') {
                this.hideHelp();
            }
        });
    }

    showHelp(helpId, triggerElement = null) {
        const helpContent = window.TemplateModalUtils?.helpContent?.[helpId];
        if (!helpContent) {
            console.warn(`Help content not found for ID: ${helpId}`);
            return;
        }

        this.currentHelpId = helpId;

        // Update modal content
        document.getElementById('help-modal-title').textContent = helpContent.title;
        document.getElementById('help-modal-body').innerHTML = helpContent.content;

        // Position the modal relative to the trigger element if provided
        if (triggerElement) {
            this.positionModal(triggerElement);
        }

        // Show the modal
        this.helpModal.style.display = 'block';

        // Add animation class
        setTimeout(() => {
            this.helpModal.classList.add('show');
        }, 10);
    }

    hideHelp() {
        this.helpModal.classList.remove('show');
        setTimeout(() => {
            this.helpModal.style.display = 'none';
            this.currentHelpId = null;
        }, 200);
    }

    positionModal(triggerElement) {
        const rect = triggerElement.getBoundingClientRect();
        const modalContent = this.helpModal.querySelector('.help-modal-content');

        // Reset positioning
        modalContent.style.position = 'fixed';
        modalContent.style.top = '';
        modalContent.style.left = '';
        modalContent.style.right = '';
        modalContent.style.bottom = '';
        modalContent.style.transform = '';

        // Calculate optimal position
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const modalWidth = 400; // Approximate modal width
        const modalHeight = 300; // Approximate modal height

        let top = rect.bottom + 10;
        let left = rect.left;

        // Adjust if modal would go off-screen
        if (left + modalWidth > viewportWidth) {
            left = viewportWidth - modalWidth - 20;
        }
        if (left < 20) {
            left = 20;
        }

        if (top + modalHeight > viewportHeight) {
            top = rect.top - modalHeight - 10;
        }
        if (top < 20) {
            top = 20;
        }

        modalContent.style.top = `${top}px`;
        modalContent.style.left = `${left}px`;
        modalContent.style.transform = 'none';
    }

    addHelpIcon(elementId, helpId, position = 'after') {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn(`Element not found for help icon: ${elementId}`);
            return;
        }

        // Check if help icon already exists
        const existingIcon = element.parentElement.querySelector(`[data-help-for="${elementId}"]`);
        if (existingIcon) return;

        // Create help icon
        const helpIcon = document.createElement('button');
        helpIcon.type = 'button';
        helpIcon.className = 'help-icon';
        helpIcon.setAttribute('data-help-for', elementId);
        helpIcon.setAttribute('data-help-id', helpId);
        helpIcon.setAttribute('title', 'Click for help');
        helpIcon.innerHTML = '<i class="fas fa-question-circle"></i>';

        // Add click handler
        helpIcon.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showHelp(helpId, helpIcon);
        });

        // Position the icon
        if (position === 'after') {
            element.parentElement.appendChild(helpIcon);
        } else if (position === 'before') {
            element.parentElement.insertBefore(helpIcon, element);
        } else if (position === 'inline') {
            // For labels, add inline with the label text
            const label = element.closest('.form-group')?.querySelector('label');
            if (label) {
                label.appendChild(helpIcon);
            }
        }
    }

    initializeHelpIcons() {
        // Add help icons to various form elements
        const helpMappings = [
            { elementId: 'template-name', helpId: 'template-name', position: 'inline' },
            { elementId: 'template-category-select', helpId: 'template-category-select', position: 'inline' },
            { elementId: 'template-criteria', helpId: 'template-criteria', position: 'inline' }
        ];

        helpMappings.forEach(mapping => {
            this.addHelpIcon(mapping.elementId, mapping.helpId, mapping.position);
        });

        // Add help icons for contextual sections (these will be added when tabs are created)
        this.addContextualHelpIcons();
    }

    addContextualHelpIcons() {
        // These will be called when the contextual tabs are initialized
        setTimeout(() => {
            // Check if contextual builder exists
            const contextualBuilder = document.getElementById('contextual-tab-content');
            if (contextualBuilder) {
                this.addSectionHelpIcon(contextualBuilder, 'contextual-criteria-help', 'Contextual Criteria Help');

                // Add specific help icons for variable sections
                this.addVariableSectionHelp(contextualBuilder);
            }

            // Check if variable ranges builder exists
            const variableRangesBuilder = document.getElementById('variable-ranges-builder');
            if (variableRangesBuilder) {
                this.addSectionHelpIcon(variableRangesBuilder, 'variable-ranges-help', 'Variable Ranges Help');
            }

            // Check if context preview exists
            const contextPreview = document.getElementById('context-preview-container');
            if (contextPreview) {
                this.addSectionHelpIcon(contextPreview, 'context-preview-help', 'Context Preview Help');
            }
        }, 1000);
    }

    addVariableSectionHelp(container) {
        // Add help icons for specific variable sections
        const variableSections = [
            { selector: '[data-variable="trust_level"]', helpId: 'trust-level-help', title: 'Trust Level Help' },
            { selector: '[data-variable="mood"]', helpId: 'mood-help', title: 'Mood Variables Help' },
            { selector: '[data-variable="environment"]', helpId: 'environment-help', title: 'Environment Help' }
        ];

        variableSections.forEach(section => {
            const element = container.querySelector(section.selector);
            if (element) {
                const header = element.querySelector('h5');
                if (header && !header.querySelector(`[data-help-id="${section.helpId}"]`)) {
                    const helpIcon = document.createElement('button');
                    helpIcon.type = 'button';
                    helpIcon.className = 'help-icon variable-help';
                    helpIcon.setAttribute('data-help-id', section.helpId);
                    helpIcon.setAttribute('title', section.title);
                    helpIcon.innerHTML = '<i class="fas fa-question-circle"></i>';

                    helpIcon.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.showHelp(section.helpId, helpIcon);
                    });

                    header.appendChild(helpIcon);
                }
            }
        });
    }

    addSectionHelpIcon(container, helpId, title) {
        if (!container) return;

        // Check if help icon already exists
        const existingIcon = container.querySelector(`[data-help-id="${helpId}"]`);
        if (existingIcon) return;

        // Find the header or create one
        let header = container.querySelector('h4, .builder-header h4');
        if (!header) {
            header = container.querySelector('.builder-header');
        }

        if (header) {
            const helpIcon = document.createElement('button');
            helpIcon.type = 'button';
            helpIcon.className = 'help-icon section-help';
            helpIcon.setAttribute('data-help-id', helpId);
            helpIcon.setAttribute('title', title);
            helpIcon.innerHTML = '<i class="fas fa-question-circle"></i>';

            helpIcon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showHelp(helpId, helpIcon);
            });

            header.appendChild(helpIcon);
        }
    }
}

// Initialize help system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.helpSystem = new HelpSystem();
});

// Export for use in other modules
window.HelpSystem = HelpSystem;
