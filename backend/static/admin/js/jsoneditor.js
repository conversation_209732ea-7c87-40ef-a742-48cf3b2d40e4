// Place this file in your static/admin/js/jsoneditor.js

document.addEventListener('DOMContentLoaded', function() {
    // Find all JSON schema textareas
    const jsonFields = document.querySelectorAll('textarea.json-schema');
    
    jsonFields.forEach(function(field) {
        // Create container elements
        const container = document.createElement('div');
        container.className = 'json-editor-container';
        
        const toolbar = document.createElement('div');
        toolbar.className = 'json-editor-toolbar';
        
        // Create format button
        const formatBtn = document.createElement('button');
        formatBtn.type = 'button';
        formatBtn.className = 'json-format-btn';
        formatBtn.textContent = 'Format JSON';
        formatBtn.style.marginRight = '10px';
        formatBtn.style.padding = '5px 10px';
        formatBtn.style.backgroundColor = '#417690';
        formatBtn.style.color = 'white';
        formatBtn.style.border = 'none';
        formatBtn.style.borderRadius = '3px';
        formatBtn.style.cursor = 'pointer';
        
        // Create validate button
        const validateBtn = document.createElement('button');
        validateBtn.type = 'button';
        validateBtn.className = 'json-validate-btn';
        validateBtn.textContent = 'Validate JSON';
        validateBtn.style.padding = '5px 10px';
        validateBtn.style.backgroundColor = '#79aec8';
        validateBtn.style.color = 'white';
        validateBtn.style.border = 'none';
        validateBtn.style.borderRadius = '3px';
        validateBtn.style.cursor = 'pointer';
        
        // Create validation status element
        const validationStatus = document.createElement('span');
        validationStatus.className = 'json-validation-status';
        validationStatus.style.marginLeft = '10px';
        validationStatus.style.display = 'inline-block';
        validationStatus.style.fontWeight = 'bold';
        
        // Add buttons to toolbar
        toolbar.appendChild(formatBtn);
        toolbar.appendChild(validateBtn);
        toolbar.appendChild(validationStatus);
        
        // Insert toolbar before the textarea
        field.parentNode.insertBefore(toolbar, field);
        
        // Format JSON button click handler
        formatBtn.addEventListener('click', function() {
            try {
                const json = JSON.parse(field.value);
                field.value = JSON.stringify(json, null, 2);
                validationStatus.textContent = 'Valid JSON';
                validationStatus.style.color = 'green';
            } catch (e) {
                validationStatus.textContent = 'Invalid JSON: ' + e.message;
                validationStatus.style.color = 'red';
            }
        });
        
        // Validate JSON button click handler
        validateBtn.addEventListener('click', function() {
            try {
                JSON.parse(field.value);
                validationStatus.textContent = 'Valid JSON';
                validationStatus.style.color = 'green';
            } catch (e) {
                validationStatus.textContent = 'Invalid JSON: ' + e.message;
                validationStatus.style.color = 'red';
            }
        });
        
        // Add line numbers (basic implementation)
        field.addEventListener('input', function() {
            const lines = field.value.split('\n').length;
            const currentRows = parseInt(field.getAttribute('rows'));
            if (lines > currentRows) {
                field.setAttribute('rows', lines);
            }
        });
        
        // Add tab support
        field.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = field.selectionStart;
                const end = field.selectionEnd;
                
                // Insert tab at cursor position
                field.value = field.value.substring(0, start) + '  ' + field.value.substring(end);
                
                // Move cursor after inserted tab
                field.selectionStart = field.selectionEnd = start + 2;
            }
        });
    });
    
    // Add custom styles
    const style = document.createElement('style');
    style.textContent = `
        .json-editor-container {
            margin-bottom: 10px;
        }
        .json-editor-toolbar {
            margin-bottom: 5px;
        }
        .json-format-btn:hover, .json-validate-btn:hover {
            opacity: 0.8;
        }
        textarea.json-schema {
            font-family: 'Courier New', monospace;
            tab-size: 2;
        }
    `;
    document.head.appendChild(style);
});