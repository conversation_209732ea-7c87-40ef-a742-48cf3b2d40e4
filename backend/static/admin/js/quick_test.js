/**
 * QuickTest Module - Reusable quick benchmark testing functionality
 * 
 * This module provides a reusable QuickTest class that can be used on any page
 * to add quick benchmark testing capabilities.
 */

class QuickTest {
    constructor(config) {
        this.config = {
            scenariosApiUrl: config.scenariosApiUrl || '/admin/benchmarks/api/scenarios/',
            benchmarkRunApiUrl: config.benchmarkRunApiUrl || '/admin/benchmarks/api/run/',
            taskStatusApiUrl: config.taskStatusApiUrl || '/admin/benchmarks/api/task-status/',
            templatesApiUrl: config.templatesApiUrl || '/admin/benchmarks/api/templates/',
            showSuccessMessage: config.showSuccessMessage || this.defaultShowSuccess,
            showErrorMessage: config.showErrorMessage || this.defaultShowError,
            storageKey: config.storageKey || 'quickTestConfig',
            ...config
        };
        
        this.currentTaskId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConfig();
        this.populateScenarios();
        this.populateTemplates();
        this.populateFakeUserProfiles();
    }

    bindEvents() {
        // Quick test button
        const quickTestBtn = document.getElementById('quick-test-btn');
        if (quickTestBtn) {
            quickTestBtn.addEventListener('click', () => this.runTest());
        }

        // Configure button
        const configureBtn = document.getElementById('configure-quick-test-btn');
        if (configureBtn) {
            configureBtn.addEventListener('click', () => this.showConfig());
        }

        // Configuration modal events
        const configModal = document.getElementById('quick-test-config-modal');
        if (configModal) {
            // Cancel button
            const cancelBtn = document.getElementById('cancel-config-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.hideConfig());
            }

            // Save configuration form
            const configForm = document.getElementById('quick-test-config-form');
            if (configForm) {
                configForm.addEventListener('submit', (e) => this.saveConfig(e));
            }

            // Template selection change
            const templateSelect = document.getElementById('quick-template-select');
            if (templateSelect) {
                templateSelect.addEventListener('change', (e) => {
                    if (e.target.value) {
                        this.updateTemplatePreview(e.target.value);
                    } else {
                        const templatePreview = document.getElementById('quick-template-preview');
                        if (templatePreview) {
                            templatePreview.style.display = 'none';
                        }
                    }
                });
            }

            // Close modal when clicking outside
            configModal.addEventListener('click', (e) => {
                if (e.target === configModal) {
                    this.hideConfig();
                }
            });

            // Close modal with X button
            const closeBtn = configModal.querySelector('.close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hideConfig());
            }
        }


    }

    loadConfig() {
        const config = localStorage.getItem(this.config.storageKey);
        if (config) {
            try {
                const parsed = JSON.parse(config);
                this.updateButtonState(parsed);
            } catch (e) {
                console.warn('Failed to parse saved quick test config:', e);
                this.updateButtonState(null);
            }
        } else {
            this.updateButtonState(null);
        }
    }

    updateButtonState(config) {
        const quickTestBtn = document.getElementById('quick-test-btn');
        const quickTestDetails = document.getElementById('quick-test-details');

        if (!quickTestBtn) return;

        if (config && config.scenarioId) {
            quickTestBtn.disabled = false;
            quickTestBtn.innerHTML = '<i class="fas fa-play"></i> Run Quick Test';
            if (quickTestDetails) {
                quickTestDetails.textContent = `Ready: ${config.scenarioName || 'Scenario'} (${config.runs || 1} run${config.runs > 1 ? 's' : ''})`;
            }
        } else {
            quickTestBtn.disabled = true;
            quickTestBtn.innerHTML = '<i class="fas fa-cog"></i> Configure First';
            if (quickTestDetails) {
                quickTestDetails.textContent = 'Click Configure to set up quick test parameters';
            }
        }
    }

    async populateScenarios() {
        const scenarioSelect = document.getElementById('quick-scenario-select');
        if (!scenarioSelect) return;

        try {
            const response = await fetch(this.config.scenariosApiUrl);
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            scenarioSelect.innerHTML = '<option value="">-- Select Scenario --</option>';

            if (data.scenarios) {
                data.scenarios.forEach(scenario => {
                    if (scenario.is_active) {
                        const option = document.createElement('option');
                        option.value = scenario.id;
                        option.textContent = `${scenario.name} (${scenario.agent_role})`;
                        option.dataset.name = scenario.name;
                        scenarioSelect.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('Error loading scenarios for quick test:', error);
        }
    }

    async populateTemplates() {
        const templateSelect = document.getElementById('quick-template-select');
        if (!templateSelect) return;

        try {
            const response = await fetch(this.config.templatesApiUrl);
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            templateSelect.innerHTML = '<option value="">-- Select Template --</option>';

            if (data.templates) {
                data.templates.forEach(template => {
                    if (template.is_active) {
                        const option = document.createElement('option');
                        option.value = template.id;
                        option.textContent = `${template.name} (${template.category})`;
                        option.dataset.name = template.name;
                        option.dataset.description = template.description;
                        option.dataset.criteria = JSON.stringify(template.criteria || {});
                        templateSelect.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('Error loading templates for quick test:', error);
        }
    }

    async populateFakeUserProfiles() {
        const profileSelect = document.getElementById('quick-user-profile-select');
        if (!profileSelect) return;

        try {
            // Fetch user profiles with is_real=False filter
            const response = await fetch('/admin/benchmarks/api/user-profiles/?is_real=false');
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            profileSelect.innerHTML = '<option value="">-- Select Test Profile --</option>';

            if (data.profiles && Array.isArray(data.profiles)) {
                data.profiles.forEach(profile => {
                    const option = document.createElement('option');
                    option.value = profile.id;

                    // Create descriptive text with trust level and demographics
                    const trustInfo = profile.trust_level ? `Trust: ${profile.trust_level}` : '';
                    const demoInfo = profile.demographics ?
                        `${profile.demographics.age}y, ${profile.demographics.gender || 'N/A'}` : '';
                    const description = [trustInfo, demoInfo].filter(Boolean).join(' | ');

                    option.textContent = `${profile.profile_name}${description ? ` (${description})` : ''}`;
                    option.dataset.profileData = JSON.stringify(profile);
                    profileSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading fake user profiles for quick test:', error);
        }
    }

    getExecutionModeParams() {
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const executionMode = executionModeSelect?.value || 'mock';

        const modeParams = {
            'mock': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: false
            },
            'real-tools': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: false
            },
            'real-llm': {
                use_real_llm: true,
                use_real_tools: false,
                use_real_db: false
            },
            'real-db': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: true
            },
            'partial-real': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: true
            },
            'full-real': {
                use_real_llm: true,
                use_real_tools: true,
                use_real_db: true
            }
        };

        return modeParams[executionMode] || modeParams['mock'];
    }

    getExecutionModeParamsFromConfig(config) {
        const executionMode = config.executionMode || 'mock';

        const modeParams = {
            'mock': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: false
            },
            'real-tools': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: false
            },
            'real-llm': {
                use_real_llm: true,
                use_real_tools: false,
                use_real_db: false
            },
            'real-db': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: true
            },
            'partial-real': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: true
            },
            'full-real': {
                use_real_llm: true,
                use_real_tools: true,
                use_real_db: true
            }
        };

        return modeParams[executionMode] || modeParams['mock'];
    }

    showConfig() {
        const configModal = document.getElementById('quick-test-config-modal');
        if (!configModal) return;

        // Load current configuration
        const config = localStorage.getItem(this.config.storageKey);
        if (config) {
            try {
                const parsed = JSON.parse(config);
                this.populateConfigForm(parsed);
            } catch (e) {
                console.warn('Failed to parse saved config:', e);
            }
        }

        configModal.style.display = 'block';
    }

    hideConfig() {
        const configModal = document.getElementById('quick-test-config-modal');
        if (configModal) {
            configModal.style.display = 'none';
        }
    }

    populateConfigForm(config) {
        const scenarioSelect = document.getElementById('quick-scenario-select');
        const runsInput = document.getElementById('quick-runs-input');
        const semanticEvalCheckbox = document.getElementById('quick-semantic-eval');
        const templateSelect = document.getElementById('quick-template-select');
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const userProfileSelect = document.getElementById('quick-user-profile-select');

        // Enhanced fields
        const detailedLoggingCheckbox = document.getElementById('quick-detailed-logging');
        const saveArtifactsCheckbox = document.getElementById('quick-save-artifacts');
        const timeoutInput = document.getElementById('quick-timeout-input');
        const retryAttemptsInput = document.getElementById('quick-retry-attempts');

        if (scenarioSelect) scenarioSelect.value = config.scenarioId || '';
        if (runsInput) runsInput.value = config.runs || 1;
        if (semanticEvalCheckbox) semanticEvalCheckbox.checked = config.semanticEval !== false;
        if (templateSelect) templateSelect.value = config.templateId || '';
        if (executionModeSelect) executionModeSelect.value = config.executionMode || 'mock';
        if (userProfileSelect) userProfileSelect.value = config.userProfileId || '';

        // Populate enhanced fields
        if (detailedLoggingCheckbox) detailedLoggingCheckbox.checked = config.detailedLogging || false;
        if (saveArtifactsCheckbox) saveArtifactsCheckbox.checked = config.saveArtifacts || false;
        if (timeoutInput) timeoutInput.value = config.timeout || 300;
        if (retryAttemptsInput) retryAttemptsInput.value = config.retryAttempts || 1;

        // Update template preview if a template is selected
        if (config.templateId) {
            this.updateTemplatePreview(config.templateId);
        }

        // Trigger validation if enhancements are available
        if (window.quickTestModalEnhancements) {
            // Trigger field validation for loaded values
            if (config.scenarioId) {
                window.quickTestModalEnhancements.validateField('scenario', config.scenarioId, 'scenario-status');
            }
            if (config.templateId) {
                window.quickTestModalEnhancements.validateField('template', config.templateId, 'template-status');
            }
            if (config.userProfileId) {
                window.quickTestModalEnhancements.validateField('profile', config.userProfileId, 'profile-status');
            }
            if (config.executionMode) {
                window.quickTestModalEnhancements.updateExecutionModeInfo(config.executionMode);
            }
        }
    }

    updateTemplatePreview(templateId) {
        const templateSelect = document.getElementById('quick-template-select');
        const templatePreview = document.getElementById('quick-template-preview');

        if (!templateSelect || !templatePreview) return;

        const selectedOption = templateSelect.querySelector(`option[value="${templateId}"]`);
        if (!selectedOption) return;

        const templateName = selectedOption.dataset.name;
        const templateDescription = selectedOption.dataset.description;
        const templateCriteria = JSON.parse(selectedOption.dataset.criteria || '{}');

        let criteriaHtml = '';
        if (Object.keys(templateCriteria).length > 0) {
            criteriaHtml = '<h5>Evaluation Criteria:</h5><ul>';
            for (const [dimension, criteria] of Object.entries(templateCriteria)) {
                criteriaHtml += `<li><strong>${dimension}:</strong> ${Array.isArray(criteria) ? criteria.join(', ') : criteria}</li>`;
            }
            criteriaHtml += '</ul>';
        }

        templatePreview.innerHTML = `
            <div class="template-preview-content">
                <h4>${templateName}</h4>
                <p>${templateDescription}</p>
                ${criteriaHtml}
            </div>
        `;
        templatePreview.style.display = 'block';
    }

    saveConfig(e) {
        e.preventDefault();

        const scenarioSelect = document.getElementById('quick-scenario-select');
        const runsInput = document.getElementById('quick-runs-input');
        const semanticEvalCheckbox = document.getElementById('quick-semantic-eval');
        const templateSelect = document.getElementById('quick-template-select');
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const userProfileSelect = document.getElementById('quick-user-profile-select');

        // Enhanced fields
        const detailedLoggingCheckbox = document.getElementById('quick-detailed-logging');
        const saveArtifactsCheckbox = document.getElementById('quick-save-artifacts');
        const timeoutInput = document.getElementById('quick-timeout-input');
        const retryAttemptsInput = document.getElementById('quick-retry-attempts');

        if (!scenarioSelect || !scenarioSelect.value) {
            alert('Please select a scenario');
            return;
        }

        if (!templateSelect || !templateSelect.value) {
            alert('Please select an evaluation template');
            return;
        }

        const selectedScenario = scenarioSelect.options[scenarioSelect.selectedIndex];
        const selectedTemplate = templateSelect.options[templateSelect.selectedIndex];
        const selectedProfile = userProfileSelect?.options[userProfileSelect.selectedIndex];

        const config = {
            scenarioId: scenarioSelect.value,
            scenarioName: selectedScenario.dataset.name || selectedScenario.textContent,
            runs: parseInt(runsInput?.value) || 1,
            semanticEval: semanticEvalCheckbox?.checked !== false,
            templateId: templateSelect.value,
            templateName: selectedTemplate.dataset.name,
            templateDescription: selectedTemplate.dataset.description,
            templateCriteria: this.parseJsonSafely(selectedTemplate.dataset.criteria, {}),
            executionMode: executionModeSelect?.value || 'mock',
            userProfileId: (selectedProfile && selectedProfile.value !== '') ? selectedProfile.value : null,
            userProfileName: selectedProfile?.textContent || null,
            // Enhanced configuration options
            detailedLogging: detailedLoggingCheckbox?.checked || false,
            saveArtifacts: saveArtifactsCheckbox?.checked || false,
            timeout: parseInt(timeoutInput?.value) || 300,
            retryAttempts: parseInt(retryAttemptsInput?.value) || 1
        };

        // Save to localStorage
        localStorage.setItem(this.config.storageKey, JSON.stringify(config));

        // Update button state
        this.updateButtonState(config);

        // Hide modal
        this.hideConfig();

        this.config.showSuccessMessage('Quick test configuration saved!');
    }

    // Default message functions
    defaultShowSuccess(message) {
        console.log('Success:', message);
    }

    defaultShowError(message) {
        console.error('Error:', message);
    }

    handleTestError(error) {
        const errorMessage = error.message || 'Unknown error';

        // Check if this is an execution mode related error
        const isExecutionModeError = errorMessage.includes('Real workflow mode failed') ||
                                   errorMessage.includes('real mode') ||
                                   errorMessage.includes('execution failed');

        if (isExecutionModeError) {
            // Get current execution mode
            const executionModeSelect = document.getElementById('quick-execution-mode-select');
            const currentMode = executionModeSelect?.value || 'unknown';

            // Create enhanced error message with guidance
            let enhancedMessage = `Execution Mode Error: ${errorMessage}\n\n`;
            enhancedMessage += `Selected Mode: ${this.getExecutionModeDisplayName(currentMode)}\n\n`;
            enhancedMessage += `The real workflow execution failed. This usually means:\n`;
            enhancedMessage += `• The real workflow implementation is not fully ready\n`;
            enhancedMessage += `• There's a configuration issue with external services\n`;
            enhancedMessage += `• API keys or authentication are missing\n\n`;
            enhancedMessage += `Suggested Actions:\n`;
            enhancedMessage += `• Try using "Mock Mode" for testing purposes\n`;
            enhancedMessage += `• If you need real functionality, try "Real Tools Only" mode first\n`;
            enhancedMessage += `• Check the browser console for more detailed error information\n`;
            enhancedMessage += `• Contact support if the issue persists`;

            this.config.showErrorMessage(enhancedMessage);

            // Optionally switch to mock mode automatically
            if (confirm('Would you like to switch to Mock Mode and try again?')) {
                if (executionModeSelect) {
                    executionModeSelect.value = 'mock';
                }
            }
        } else {
            // Standard error handling
            this.config.showErrorMessage(`Quick test failed: ${errorMessage}`);
        }
    }

    getExecutionModeDisplayName(mode) {
        const modeNames = {
            'mock': '🎭 Mock Mode (Safe - No costs)',
            'real-tools': '🛠️ Real Tools Only',
            'real-llm': '🧠 Real LLM Only',
            'real-db': '🗄️ Real Database Only',
            'partial-real': '⚡ Partial Real (Tools + DB)',
            'full-real': '🚀 Full Real Mode (All components)'
        };
        return modeNames[mode] || mode;
    }

    async runTest() {
        const config = localStorage.getItem(this.config.storageKey);
        if (!config) {
            alert('Please configure the quick test first');
            return;
        }

        let parsedConfig;
        try {
            parsedConfig = JSON.parse(config);
        } catch (e) {
            alert('Invalid configuration. Please reconfigure the quick test.');
            return;
        }

        const statusDiv = document.getElementById('quick-test-status');
        const resultsDiv = document.getElementById('quick-test-results');
        const statusText = document.getElementById('quick-test-status-text');
        const progressBar = document.getElementById('quick-test-progress');
        const detailsText = document.getElementById('quick-test-details');
        const quickTestBtn = document.getElementById('quick-test-btn');

        if (!statusDiv || !statusText || !progressBar) {
            this.config.showErrorMessage('Quick test UI elements not found');
            return;
        }

        // Show status, hide results
        statusDiv.classList.remove('hidden');
        if (resultsDiv) resultsDiv.classList.add('hidden');
        if (quickTestBtn) quickTestBtn.disabled = true;

        try {
            statusText.textContent = 'Preparing test...';
            progressBar.style.width = '10%';
            progressBar.style.backgroundColor = '#2196f3';
            if (detailsText) detailsText.textContent = `Running ${parsedConfig.scenarioName} with ${parsedConfig.runs} run(s)`;

            // Get execution mode parameters
            const executionModeParams = this.getExecutionModeParamsFromConfig(parsedConfig);

            // Prepare test parameters using the selected template
            const testParams = {
                scenario_id: parseInt(parsedConfig.scenarioId),
                evaluation_template_id: parseInt(parsedConfig.templateId),
                params: {
                    runs: parsedConfig.runs,
                    semantic_evaluation: parsedConfig.semanticEval,
                    context_variables: {
                        trust_level: 35 // Default trust level for quick tests
                    },
                    // Add execution mode parameters
                    ...executionModeParams,
                    // Add user profile if selected
                    ...(parsedConfig.userProfileId && { user_profile_id: parsedConfig.userProfileId })
                }
            };

            statusText.textContent = 'Submitting test request...';
            progressBar.style.width = '30%';

            // Launch the benchmark test
            const response = await fetch(this.config.benchmarkRunApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(testParams)
            });

            let result;
            try {
                result = await response.json();
            } catch (parseError) {
                throw new Error(`Failed to parse server response: ${parseError.message}`);
            }

            if (!response.ok) {
                throw new Error(result.error || `HTTP error ${response.status}`);
            }

            if (!result.success) {
                throw new Error(result.error || 'Test failed to start');
            }

            this.currentTaskId = result.task_id;
            statusText.textContent = 'Test running...';
            progressBar.style.width = '60%';
            if (detailsText) detailsText.textContent = `Task ID: ${result.task_id}`;

            // Poll for results
            await this.pollResults(result.task_id, statusText, progressBar, detailsText);

        } catch (error) {
            console.error('Error running quick test:', error);
            statusText.textContent = `Error: ${error.message}`;
            progressBar.style.width = '100%';
            progressBar.style.backgroundColor = '#dc3545';
            if (detailsText) detailsText.textContent = 'Test failed';

            // Enhanced error handling for execution mode issues
            this.handleTestError(error);
        } finally {
            if (quickTestBtn) quickTestBtn.disabled = false;
            this.currentTaskId = null;
        }
    }

    async pollResults(taskId, statusText, progressBar, detailsText) {
        const maxAttempts = 60; // 5 minutes max
        let attempts = 0;

        const poll = async () => {
            attempts++;

            try {
                const response = await fetch(`/admin/benchmarks/api/task/${taskId}/status/`);

                let result;
                try {
                    result = await response.json();
                } catch (parseError) {
                    throw new Error(`Failed to parse server response: ${parseError.message}`);
                }

                if (!response.ok) {
                    throw new Error(result.error || `HTTP error ${response.status}`);
                }

                // The API returns 'status' field, not 'state'
                const taskStatus = result.status || 'pending';

                if (taskStatus === 'completed') {
                    statusText.textContent = 'Test completed!';
                    progressBar.style.width = '100%';
                    progressBar.style.backgroundColor = '#28a745';
                    if (detailsText) detailsText.textContent = 'Processing results...';

                    // Show results - the result data is in the 'result' field
                    this.displayResults(result.result || result);

                    // Hide status after a moment
                    setTimeout(() => {
                        const statusDiv = document.getElementById('quick-test-status');
                        if (statusDiv) statusDiv.classList.add('hidden');
                    }, 2000);

                } else if (taskStatus === 'failed') {
                    const errorMessage = result.error || 'Test failed';
                    // Handle execution mode errors during polling
                    this.handleTestError(new Error(errorMessage));
                    throw new Error(errorMessage);
                } else if (taskStatus === 'pending' || taskStatus === 'started' || taskStatus === 'progress') {
                    // Update progress
                    const progress = Math.min(60 + (attempts * 2), 95);
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = 'Test in progress...';

                    if (attempts < maxAttempts) {
                        setTimeout(poll, 5000); // Poll every 5 seconds
                    } else {
                        throw new Error('Test timeout - taking too long to complete');
                    }
                } else {
                    // Unknown state, continue polling
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 5000);
                    } else {
                        throw new Error('Test timeout');
                    }
                }
            } catch (error) {
                throw error;
            }
        };

        // Start polling
        setTimeout(poll, 2000); // Wait 2 seconds before first poll
    }

    displayResults(result) {
        const resultsDiv = document.getElementById('quick-test-results');
        const resultsContent = document.getElementById('quick-test-results-content');

        if (!resultsDiv || !resultsContent) return;

        if (!result || !result.runs || result.runs.length === 0) {
            resultsContent.innerHTML = '<p class="text-muted">No results available</p>';
            resultsDiv.classList.remove('hidden');
            return;
        }

        const run = result.runs[0]; // Get first run
        const semanticScore = run.semantic_score || 0;
        const executionTime = run.execution_time || 0;
        const tokenUsage = run.token_usage || 'N/A';
        const cost = run.cost || 0;

        resultsContent.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 15px;">
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 24px; font-weight: bold; color: ${semanticScore >= 0.7 ? '#28a745' : semanticScore >= 0.5 ? '#ffc107' : '#dc3545'};">
                        ${(semanticScore * 100).toFixed(1)}%
                    </div>
                    <div style="font-size: 12px; color: #666;">Semantic Score</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 18px; font-weight: bold; color: #333;">
                        ${(executionTime * 1000).toFixed(0)}ms
                    </div>
                    <div style="font-size: 12px; color: #666;">Execution Time</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 16px; font-weight: bold; color: #333;">
                        ${tokenUsage}
                    </div>
                    <div style="font-size: 12px; color: #666;">Token Usage</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 16px; font-weight: bold; color: #333;">
                        $${cost.toFixed(4)}
                    </div>
                    <div style="font-size: 12px; color: #666;">Estimated Cost</div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <a href="/admin/benchmarks/history/" class="btn btn-secondary" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View in History
                </a>
                <button onclick="window.quickTestInstance?.runTest()" class="btn btn-primary" style="margin-left: 10px;">
                    <i class="fas fa-redo"></i> Run Again
                </button>
            </div>
        `;

        resultsDiv.classList.remove('hidden');

        // Show success message
        this.config.showSuccessMessage(`Quick test completed! Semantic score: ${(semanticScore * 100).toFixed(1)}%`);
    }

    // Get CSRF token from cookies
    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to safely parse JSON
    parseJsonSafely(jsonString, defaultValue) {
        try {
            const parsed = JSON.parse(jsonString);
            // If parsed is null and we expect an object, return defaultValue
            if (parsed === null && typeof defaultValue === 'object' && defaultValue !== null) {
                return defaultValue;
            }
            return parsed;
        } catch (e) {
            console.warn('Failed to parse JSON string safely:', jsonString, e);
            return defaultValue;
        }
    }
}

// Make QuickTest available globally
window.QuickTest = QuickTest;
