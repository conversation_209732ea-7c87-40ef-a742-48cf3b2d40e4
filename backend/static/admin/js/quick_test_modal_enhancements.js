// ACTIVE_FILE - Enhanced Quick Test Modal Functionality
/**
 * Enhanced Quick Test Modal Features
 * 
 * This module provides enhanced functionality for the quick test configuration modal,
 * including tabbed interface, collapsible sections, real-time validation, and preview features.
 */

class QuickTestModalEnhancements {
    constructor() {
        this.modal = null;
        this.currentTab = 'basic';
        this.validationState = {
            scenario: false,
            template: false,
            profile: false
        };
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.autoSaveTimer = null;
        this.keyboardShortcuts = new Map();

        this.init();
    }

    init() {
        this.modal = document.getElementById('quick-test-config-modal');
        if (!this.modal) {
            console.warn('QuickTestModalEnhancements: Modal not found');
            return;
        }

        console.log('QuickTestModalEnhancements: Initializing...');

        // Add a small delay to ensure DOM is fully ready
        setTimeout(() => {
            this.setupTabNavigation();
            this.setupCollapsibleSections();
            this.setupFieldValidation();
            this.setupPreviewFeatures();
            this.setupEnhancedEventListeners();
            this.setupKeyboardNavigation();
            this.setupDragFunctionality();
            this.setupAutoSave();
            this.setupAccessibilityFeatures();
            console.log('QuickTestModalEnhancements: Initialization complete');
        }, 100);
    }

    setupTabNavigation() {
        const tabButtons = this.modal.querySelectorAll('.tab-button');
        const tabContents = this.modal.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                this.switchTab(targetTab, tabButtons, tabContents);
            });
        });
    }

    switchTab(targetTab, tabButtons, tabContents) {
        // Remove active class from all buttons and contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        // Add active class to clicked button and corresponding content
        const activeButton = this.modal.querySelector(`[data-tab="${targetTab}"]`);
        const activeContent = this.modal.querySelector(`#${targetTab}-tab`);

        if (activeButton && activeContent) {
            activeButton.classList.add('active');
            activeContent.classList.add('active');
            this.currentTab = targetTab;

            // Update preview if switching to preview tab
            if (targetTab === 'preview') {
                this.updateConfigPreview();
            }
        }
    }

    setupCollapsibleSections() {
        console.log('Setting up collapsible sections...');
        const collapsibleSections = this.modal.querySelectorAll('.form-section.collapsible');
        console.log(`Found ${collapsibleSections.length} collapsible sections`);

        collapsibleSections.forEach((section, index) => {
            const header = section.querySelector('.section-header');
            if (header) {
                // Remove any existing event listeners to prevent duplicates
                const newHeader = header.cloneNode(true);
                header.parentNode.replaceChild(newHeader, header);

                // Add single event listener with proper handling
                newHeader.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    console.log(`Toggling section ${index}`);
                    this.toggleSection(section);
                }, { once: false });

                // Mark as having listener for debugging
                newHeader._hasClickListener = true;
                console.log(`Added click listener to section ${index}`);
            }
        });
    }

    toggleSection(section) {
        const isCurrentlyCollapsed = section.classList.contains('collapsed');
        console.log(`Section currently collapsed: ${isCurrentlyCollapsed}`);

        // Use a flag to prevent rapid toggling
        if (section.dataset.toggling === 'true') {
            console.log('Section is already toggling, ignoring...');
            return;
        }

        // Check if section is in an active tab
        const parentTab = section.closest('.tab-content');
        const isInActiveTab = !parentTab || parentTab.classList.contains('active');
        console.log(`Section is in active tab: ${isInActiveTab}`);

        if (!isInActiveTab) {
            console.log('Section is not in active tab, ignoring toggle');
            return;
        }

        section.dataset.toggling = 'true';

        // Toggle the collapsed state
        if (isCurrentlyCollapsed) {
            section.classList.remove('collapsed');
            console.log('Expanding section');
        } else {
            section.classList.add('collapsed');
            console.log('Collapsing section');
        }

        // Animate the toggle icon
        const icon = section.querySelector('.toggle-icon');
        if (icon) {
            icon.style.transform = section.classList.contains('collapsed')
                ? 'rotate(-90deg)'
                : 'rotate(0deg)';
        }

        // Update section content visibility with high specificity
        const content = section.querySelector('.section-content');
        if (content) {
            // Get computed style to debug what's happening
            const computedStyle = window.getComputedStyle(content);
            console.log(`Content computed display before change: ${computedStyle.display}`);
            console.log(`Parent tab active: ${parentTab ? parentTab.classList.contains('active') : 'no parent tab'}`);

            if (section.classList.contains('collapsed')) {
                // Use multiple methods to ensure hiding works
                content.style.setProperty('display', 'none', 'important');
                content.style.setProperty('visibility', 'hidden', 'important');
                content.style.setProperty('opacity', '0', 'important');
                content.style.setProperty('max-height', '0', 'important');
                content.style.setProperty('overflow', 'hidden', 'important');
                console.log('Applied collapsed styles');
            } else {
                // Use multiple methods to ensure showing works
                content.style.setProperty('display', 'block', 'important');
                content.style.setProperty('visibility', 'visible', 'important');
                content.style.setProperty('opacity', '1', 'important');
                content.style.setProperty('max-height', 'none', 'important');
                content.style.setProperty('overflow', 'visible', 'important');
                console.log('Applied expanded styles');
            }

            // Check computed style after change
            setTimeout(() => {
                const newComputedStyle = window.getComputedStyle(content);
                console.log(`Content computed display after change: ${newComputedStyle.display}`);
                console.log(`Content computed visibility after change: ${newComputedStyle.visibility}`);
                console.log(`Content computed max-height after change: ${newComputedStyle.maxHeight}`);

                // Additional debugging: check if content is actually visible
                const rect = content.getBoundingClientRect();
                console.log(`Content bounding rect:`, {
                    width: rect.width,
                    height: rect.height,
                    top: rect.top,
                    left: rect.left,
                    visible: rect.width > 0 && rect.height > 0
                });
            }, 50);
        }

        // Reset the toggling flag after a short delay
        setTimeout(() => {
            section.dataset.toggling = 'false';
        }, 300);
    }

    setupFieldValidation() {
        // Scenario validation
        const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
        if (scenarioSelect) {
            scenarioSelect.addEventListener('change', () => {
                this.validateField('scenario', scenarioSelect.value, 'scenario-status');
            });
        }

        // Template validation
        const templateSelect = this.modal.querySelector('#quick-template-select');
        if (templateSelect) {
            templateSelect.addEventListener('change', () => {
                this.validateField('template', templateSelect.value, 'template-status');
                if (templateSelect.value) {
                    this.updateTemplatePreview(templateSelect.value);
                }
            });
        }

        // Profile validation
        const profileSelect = this.modal.querySelector('#quick-user-profile-select');
        if (profileSelect) {
            profileSelect.addEventListener('change', () => {
                this.validateField('profile', profileSelect.value, 'profile-status');
            });
        }

        // Execution mode info
        const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
        if (executionModeSelect) {
            executionModeSelect.addEventListener('change', () => {
                this.updateExecutionModeInfo(executionModeSelect.value);
            });
        }
    }

    validateField(fieldName, value, statusElementId) {
        const statusElement = this.modal.querySelector(`#${statusElementId}`);
        if (!statusElement) return;

        let isValid = false;
        let message = '';

        switch (fieldName) {
            case 'scenario':
                isValid = value && value.trim() !== '';
                message = isValid ? 'Scenario selected' : 'Please select a scenario';
                break;
            case 'template':
                isValid = value && value.trim() !== '';
                message = isValid ? 'Template selected' : 'Please select an evaluation template';
                break;
            case 'profile':
                // Profile is optional, so always valid
                isValid = true;
                message = value ? 'Profile selected' : 'No profile selected (optional)';
                break;
        }

        this.validationState[fieldName] = isValid;
        this.updateFieldStatus(statusElement, isValid, message);
        this.updateConfigPreview();
    }

    updateFieldStatus(statusElement, isValid, message) {
        statusElement.className = `field-status ${isValid ? 'success' : 'error'}`;
        statusElement.textContent = message;
    }

    updateExecutionModeInfo(mode) {
        const infoElement = this.modal.querySelector('#execution-mode-info');
        if (!infoElement) return;

        const modeInfo = this.getExecutionModeInfo(mode);
        
        if (modeInfo.showInfo) {
            infoElement.innerHTML = `
                <div class="mode-details">
                    <strong>${modeInfo.title}</strong>
                    <p>${modeInfo.description}</p>
                    ${modeInfo.warnings ? `<div class="warnings">${modeInfo.warnings}</div>` : ''}
                </div>
            `;
            infoElement.className = `execution-mode-info ${modeInfo.level}`;
            infoElement.style.display = 'block';
        } else {
            infoElement.style.display = 'none';
        }
    }

    getExecutionModeInfo(mode) {
        const modeInfoMap = {
            'mock': {
                showInfo: false
            },
            'real-tools': {
                showInfo: true,
                title: 'Real Tools Mode',
                description: 'Uses actual tool implementations but mocked LLM and database.',
                level: 'warning',
                warnings: 'Tool calls will execute real operations.'
            },
            'real-llm': {
                showInfo: true,
                title: 'Real LLM Mode',
                description: 'Uses actual LLM API calls but mocked tools and database.',
                level: 'warning',
                warnings: 'This will consume LLM API credits and incur costs.'
            },
            'real-db': {
                showInfo: true,
                title: 'Real Database Mode',
                description: 'Uses actual database operations but mocked LLM and tools.',
                level: 'warning',
                warnings: 'Database operations will affect real data.'
            },
            'partial-real': {
                showInfo: true,
                title: 'Partial Real Mode',
                description: 'Uses real tools and database but mocked LLM.',
                level: 'warning',
                warnings: 'Tool calls and database operations will execute real operations.'
            },
            'full-real': {
                showInfo: true,
                title: 'Full Real Mode',
                description: 'Uses all real components: LLM, tools, and database.',
                level: 'danger',
                warnings: 'This will consume API credits, execute real operations, and incur costs.'
            }
        };

        return modeInfoMap[mode] || { showInfo: false };
    }

    setupPreviewFeatures() {
        // Validate configuration button
        const validateBtn = this.modal.querySelector('#validate-config-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => {
                this.validateConfiguration();
            });
        }

        // Test connection button
        const testConnectionBtn = this.modal.querySelector('#test-connection-btn');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', () => {
                this.testConnection();
            });
        }

        // Reset configuration button
        const resetBtn = this.modal.querySelector('#reset-config-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetConfiguration();
            });
        }

        // Export configuration button
        const exportBtn = this.modal.querySelector('#export-config-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportConfiguration();
            });
        }

        // Import configuration button
        const importBtn = this.modal.querySelector('#import-config-btn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.importConfiguration();
            });
        }

        // Set up health check monitoring
        this.setupHealthCheckMonitoring();
    }

    setupEnhancedEventListeners() {
        // Listen for form changes to update preview
        const form = this.modal.querySelector('#quick-test-config-form');
        if (form) {
            form.addEventListener('change', () => {
                if (this.currentTab === 'preview') {
                    this.updateConfigPreview();
                }
            });
        }
    }

    updateConfigPreview() {
        const summaryElement = this.modal.querySelector('#config-summary');
        if (!summaryElement) return;

        const config = this.getCurrentConfiguration();
        const isValid = this.isConfigurationValid();

        let html = `
            <div class="config-summary-grid">
                <div class="summary-section">
                    <h6>📋 Test Configuration</h6>
                    <div class="summary-item">
                        <span class="label">Scenario:</span>
                        <span class="value ${config.scenario ? 'valid' : 'invalid'}">${config.scenario || 'Not selected'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Runs:</span>
                        <span class="value">${config.runs}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Semantic Evaluation:</span>
                        <span class="value">${config.semanticEval ? 'Enabled' : 'Disabled'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>🔧 Execution Settings</h6>
                    <div class="summary-item">
                        <span class="label">Mode:</span>
                        <span class="value">${this.getExecutionModeDisplayName(config.executionMode)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">User Profile:</span>
                        <span class="value">${config.userProfile || 'None selected'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>📊 Evaluation</h6>
                    <div class="summary-item">
                        <span class="label">Template:</span>
                        <span class="value ${config.template ? 'valid' : 'invalid'}">${config.template || 'Not selected'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>🚀 Advanced Options</h6>
                    <div class="summary-item">
                        <span class="label">Detailed Logging:</span>
                        <span class="value">${config.detailedLogging ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Save Artifacts:</span>
                        <span class="value">${config.saveArtifacts ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Timeout:</span>
                        <span class="value">${config.timeout}s</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Retry Attempts:</span>
                        <span class="value">${config.retryAttempts}</span>
                    </div>
                </div>
            </div>

            <div class="config-status ${isValid ? 'valid' : 'invalid'}">
                <strong>Configuration Status:</strong> 
                ${isValid ? '✅ Ready to save' : '❌ Missing required fields'}
            </div>
        `;

        summaryElement.innerHTML = html;
    }

    getCurrentConfiguration() {
        const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
        const runsInput = this.modal.querySelector('#quick-runs-input');
        const semanticEvalCheckbox = this.modal.querySelector('#quick-semantic-eval');
        const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
        const userProfileSelect = this.modal.querySelector('#quick-user-profile-select');
        const templateSelect = this.modal.querySelector('#quick-template-select');
        const detailedLoggingCheckbox = this.modal.querySelector('#quick-detailed-logging');
        const saveArtifactsCheckbox = this.modal.querySelector('#quick-save-artifacts');
        const timeoutInput = this.modal.querySelector('#quick-timeout-input');
        const retryAttemptsInput = this.modal.querySelector('#quick-retry-attempts');

        return {
            scenario: scenarioSelect?.selectedOptions[0]?.textContent || '',
            runs: runsInput?.value || 1,
            semanticEval: semanticEvalCheckbox?.checked || false,
            executionMode: executionModeSelect?.value || 'mock',
            userProfile: userProfileSelect?.selectedOptions[0]?.textContent || '',
            template: templateSelect?.selectedOptions[0]?.textContent || '',
            detailedLogging: detailedLoggingCheckbox?.checked || false,
            saveArtifacts: saveArtifactsCheckbox?.checked || false,
            timeout: timeoutInput?.value || 300,
            retryAttempts: retryAttemptsInput?.value || 1
        };
    }

    isConfigurationValid() {
        return this.validationState.scenario && this.validationState.template;
    }

    getExecutionModeDisplayName(mode) {
        const modeNames = {
            'mock': '🎭 Mock Mode',
            'real-tools': '🛠️ Real Tools Only',
            'real-llm': '🧠 Real LLM Only',
            'real-db': '🗄️ Real Database Only',
            'partial-real': '⚡ Partial Real',
            'full-real': '🚀 Full Real Mode'
        };
        return modeNames[mode] || mode;
    }

    validateConfiguration() {
        const resultsElement = this.modal.querySelector('#validation-results');
        if (!resultsElement) return;

        resultsElement.style.display = 'block';
        resultsElement.innerHTML = '<div class="loading-spinner">Validating configuration...</div>';

        // Simulate validation process
        setTimeout(() => {
            const isValid = this.isConfigurationValid();
            const config = this.getCurrentConfiguration();

            let html = `
                <h6>Validation Results</h6>
                <div class="validation-items">
            `;

            // Check each required field
            html += this.getValidationItem('Scenario Selection', this.validationState.scenario, 'A scenario must be selected');
            html += this.getValidationItem('Template Selection', this.validationState.template, 'An evaluation template must be selected');
            html += this.getValidationItem('Runs Configuration', config.runs >= 1 && config.runs <= 5, 'Number of runs must be between 1 and 5');
            html += this.getValidationItem('Timeout Configuration', config.timeout >= 60 && config.timeout <= 1800, 'Timeout must be between 60 and 1800 seconds');

            html += '</div>';

            if (isValid) {
                html += '<div class="validation-summary success">✅ Configuration is valid and ready to save!</div>';
                resultsElement.className = 'validation-results success';
            } else {
                html += '<div class="validation-summary error">❌ Please fix the issues above before saving.</div>';
                resultsElement.className = 'validation-results error';
            }

            resultsElement.innerHTML = html;
        }, 1000);
    }

    getValidationItem(label, isValid, message) {
        const icon = isValid ? '✅' : '❌';
        const status = isValid ? 'valid' : 'invalid';
        return `
            <div class="validation-item ${status}">
                <span class="validation-icon">${icon}</span>
                <span class="validation-label">${label}</span>
                ${!isValid ? `<span class="validation-message">${message}</span>` : ''}
            </div>
        `;
    }

    testConnection() {
        const resultsElement = this.modal.querySelector('#validation-results');
        if (!resultsElement) return;

        resultsElement.style.display = 'block';
        resultsElement.className = 'validation-results';
        resultsElement.innerHTML = '<div class="loading-spinner">Testing connection...</div>';

        // Simulate connection test
        setTimeout(() => {
            const html = `
                <h6>Connection Test Results</h6>
                <div class="connection-tests">
                    <div class="test-item success">
                        <span class="test-icon">✅</span>
                        <span class="test-label">API Endpoint</span>
                        <span class="test-status">Connected</span>
                    </div>
                    <div class="test-item success">
                        <span class="test-icon">✅</span>
                        <span class="test-label">Database</span>
                        <span class="test-status">Available</span>
                    </div>
                    <div class="test-item warning">
                        <span class="test-icon">⚠️</span>
                        <span class="test-label">LLM Service</span>
                        <span class="test-status">Limited quota</span>
                    </div>
                </div>
                <div class="test-summary success">🟢 System is ready for testing</div>
            `;

            resultsElement.className = 'validation-results success';
            resultsElement.innerHTML = html;
        }, 1500);
    }

    resetConfiguration() {
        if (confirm('Are you sure you want to reset all configuration to defaults?')) {
            // Reset all form fields to defaults
            const form = this.modal.querySelector('#quick-test-config-form');
            if (form) {
                form.reset();
                
                // Reset specific fields to their default values
                const runsInput = this.modal.querySelector('#quick-runs-input');
                if (runsInput) runsInput.value = 1;
                
                const semanticEvalCheckbox = this.modal.querySelector('#quick-semantic-eval');
                if (semanticEvalCheckbox) semanticEvalCheckbox.checked = true;
                
                const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
                if (executionModeSelect) executionModeSelect.value = 'mock';
                
                const timeoutInput = this.modal.querySelector('#quick-timeout-input');
                if (timeoutInput) timeoutInput.value = 300;
                
                const retryAttemptsInput = this.modal.querySelector('#quick-retry-attempts');
                if (retryAttemptsInput) retryAttemptsInput.value = 1;
            }

            // Reset validation state
            this.validationState = {
                scenario: false,
                template: false,
                profile: false
            };

            // Clear status indicators
            const statusElements = this.modal.querySelectorAll('.field-status');
            statusElements.forEach(element => {
                element.style.display = 'none';
            });

            // Hide execution mode info
            const executionModeInfo = this.modal.querySelector('#execution-mode-info');
            if (executionModeInfo) {
                executionModeInfo.style.display = 'none';
            }

            // Hide template preview
            const templatePreview = this.modal.querySelector('#quick-template-preview');
            if (templatePreview) {
                templatePreview.style.display = 'none';
            }

            // Update preview
            this.updateConfigPreview();

            // Switch back to basic tab
            const basicTabButton = this.modal.querySelector('[data-tab="basic"]');
            if (basicTabButton) {
                basicTabButton.click();
            }
        }
    }

    updateTemplatePreview(templateId) {
        const previewElement = this.modal.querySelector('#quick-template-preview');
        if (!previewElement) return;

        // This would typically fetch template details from the server
        // For now, show a placeholder
        previewElement.innerHTML = `
            <h6>Template Preview</h6>
            <div class="template-info">
                <p><strong>Template ID:</strong> ${templateId}</p>
                <p><em>Loading template details...</em></p>
            </div>
        `;
        previewElement.style.display = 'block';
        previewElement.classList.add('visible');
    }

    setupKeyboardNavigation() {
        // Set up keyboard shortcuts
        this.keyboardShortcuts.set('Escape', () => this.closeModal());
        this.keyboardShortcuts.set('F1', () => this.showHelp());
        this.keyboardShortcuts.set('Control+Enter', () => this.saveConfiguration());
        this.keyboardShortcuts.set('Control+r', () => this.resetConfiguration());

        document.addEventListener('keydown', (e) => {
            if (!this.modal || this.modal.style.display === 'none') return;

            const key = e.key;
            const modifiers = [];
            if (e.ctrlKey) modifiers.push('Control');
            if (e.shiftKey) modifiers.push('Shift');
            if (e.altKey) modifiers.push('Alt');

            const shortcut = modifiers.length > 0 ? `${modifiers.join('+')}+${key}` : key;

            if (this.keyboardShortcuts.has(shortcut)) {
                e.preventDefault();
                this.keyboardShortcuts.get(shortcut)();
            }

            // Tab navigation between tabs
            if (e.key === 'Tab' && e.ctrlKey) {
                e.preventDefault();
                this.switchToNextTab();
            }
        });

        // Add keyboard navigation hints
        this.addKeyboardHints();
    }

    setupDragFunctionality() {
        const header = this.modal.querySelector('.quick-test-header');
        if (!header) return;

        header.style.cursor = 'move';

        header.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            const rect = this.modal.querySelector('.modal-content').getBoundingClientRect();
            this.dragOffset.x = e.clientX - rect.left;
            this.dragOffset.y = e.clientY - rect.top;

            document.addEventListener('mousemove', this.handleDrag.bind(this));
            document.addEventListener('mouseup', this.handleDragEnd.bind(this));

            e.preventDefault();
        });
    }

    handleDrag(e) {
        if (!this.isDragging) return;

        const modalContent = this.modal.querySelector('.modal-content');
        const newX = e.clientX - this.dragOffset.x;
        const newY = e.clientY - this.dragOffset.y;

        // Constrain to viewport
        const maxX = window.innerWidth - modalContent.offsetWidth;
        const maxY = window.innerHeight - modalContent.offsetHeight;

        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));

        modalContent.style.position = 'fixed';
        modalContent.style.left = `${constrainedX}px`;
        modalContent.style.top = `${constrainedY}px`;
        modalContent.style.margin = '0';
    }

    handleDragEnd() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.handleDrag.bind(this));
        document.removeEventListener('mouseup', this.handleDragEnd.bind(this));
    }

    setupAutoSave() {
        const form = this.modal.querySelector('#quick-test-config-form');
        if (!form) return;

        form.addEventListener('input', () => {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = setTimeout(() => {
                this.autoSaveFormState();
            }, 2000); // Auto-save after 2 seconds of inactivity
        });

        // Load saved state on initialization
        this.loadSavedFormState();
    }

    autoSaveFormState() {
        const formData = new FormData(this.modal.querySelector('#quick-test-config-form'));
        const state = {};

        for (let [key, value] of formData.entries()) {
            state[key] = value;
        }

        // Also save checkbox states
        this.modal.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            state[checkbox.id] = checkbox.checked;
        });

        localStorage.setItem('quickTestModalAutoSave', JSON.stringify(state));
        this.showAutoSaveIndicator();
    }

    loadSavedFormState() {
        const saved = localStorage.getItem('quickTestModalAutoSave');
        if (!saved) return;

        try {
            const state = JSON.parse(saved);

            Object.entries(state).forEach(([key, value]) => {
                const element = this.modal.querySelector(`#${key}`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = value;
                    } else {
                        element.value = value;
                    }
                }
            });
        } catch (e) {
            console.warn('Failed to load auto-saved form state:', e);
        }
    }

    showAutoSaveIndicator() {
        // Create or update auto-save indicator
        let indicator = this.modal.querySelector('.auto-save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auto-save-indicator';
            indicator.innerHTML = '<i class="fas fa-check-circle"></i> Auto-saved';
            indicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: 50px;
                background: #28a745;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s;
                z-index: 1001;
            `;
            this.modal.querySelector('.modal-content').appendChild(indicator);
        }

        indicator.style.opacity = '1';
        setTimeout(() => {
            indicator.style.opacity = '0';
        }, 2000);
    }

    setupAccessibilityFeatures() {
        // Add ARIA labels and roles
        this.modal.setAttribute('role', 'dialog');
        this.modal.setAttribute('aria-labelledby', 'modal-title');
        this.modal.setAttribute('aria-modal', 'true');

        // Add focus management
        const firstFocusable = this.modal.querySelector('input, select, button, textarea');
        if (firstFocusable) {
            firstFocusable.focus();
        }

        // Trap focus within modal
        this.modal.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.trapFocus(e);
            }
        });
    }

    trapFocus(e) {
        const focusableElements = this.modal.querySelectorAll(
            'input, select, button, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    closeModal() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
    }

    showHelp() {
        const helpContent = `
            <div class="help-modal">
                <h4>Quick Test Modal Help</h4>
                <div class="help-section">
                    <h5>Keyboard Shortcuts:</h5>
                    <ul>
                        <li><kbd>Esc</kbd> - Close modal</li>
                        <li><kbd>Ctrl+Enter</kbd> - Save configuration</li>
                        <li><kbd>Ctrl+R</kbd> - Reset configuration</li>
                        <li><kbd>Ctrl+Tab</kbd> - Switch between tabs</li>
                        <li><kbd>F1</kbd> - Show this help</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h5>Features:</h5>
                    <ul>
                        <li>Drag the header to reposition the modal</li>
                        <li>Auto-save functionality saves your progress</li>
                        <li>Collapsible sections to organize content</li>
                        <li>Real-time validation and preview</li>
                    </ul>
                </div>
                <button onclick="this.closest('.help-modal').remove()" class="btn btn-primary">Close Help</button>
            </div>
        `;

        const helpOverlay = document.createElement('div');
        helpOverlay.className = 'help-overlay';
        helpOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        helpOverlay.innerHTML = helpContent;
        document.body.appendChild(helpOverlay);

        helpOverlay.addEventListener('click', (e) => {
            if (e.target === helpOverlay) {
                helpOverlay.remove();
            }
        });
    }

    switchToNextTab() {
        const tabs = ['basic', 'advanced', 'preview'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        this.switchTab(tabs[nextIndex]);
    }

    addKeyboardHints() {
        // Add keyboard hints to the modal header
        const header = this.modal.querySelector('.quick-test-header');
        if (header) {
            const hintsElement = document.createElement('div');
            hintsElement.className = 'keyboard-hints';
            hintsElement.innerHTML = `
                <small style="opacity: 0.8; font-size: 11px;">
                    Press <kbd>F1</kbd> for help • <kbd>Esc</kbd> to close • <kbd>Ctrl+Tab</kbd> to switch tabs
                </small>
            `;
            hintsElement.style.cssText = `
                position: absolute;
                bottom: 5px;
                left: 20px;
                font-size: 11px;
                opacity: 0.8;
            `;
            header.appendChild(hintsElement);
        }
    }

    saveConfiguration() {
        // Trigger the form submission
        const form = this.modal.querySelector('#quick-test-config-form');
        if (form) {
            form.dispatchEvent(new Event('submit'));
        }
    }

    exportConfiguration() {
        const config = this.getCurrentConfiguration();
        const dataStr = JSON.stringify(config, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `quick-test-config-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification('Configuration exported successfully!', 'success');
    }

    importConfiguration() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const config = JSON.parse(e.target.result);
                    this.populateConfigForm(config);
                    this.showNotification('Configuration imported successfully!', 'success');
                } catch (error) {
                    this.showNotification('Failed to import configuration: Invalid JSON', 'error');
                }
            };
            reader.readAsText(file);
        };
        input.click();
    }

    populateConfigForm(config) {
        // Populate form fields with imported configuration
        Object.entries(config).forEach(([key, value]) => {
            const element = this.modal.querySelector(`#quick-${key}-select, #quick-${key}-input, #quick-${key}`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });

        // Update preview after importing
        if (this.currentTab === 'preview') {
            this.updateConfigPreview();
        }
    }

    setupHealthCheckMonitoring() {
        // Monitor form changes and update health indicators
        const form = this.modal.querySelector('#quick-test-config-form');
        if (!form) return;

        form.addEventListener('change', () => {
            this.updateHealthIndicators();
        });

        // Initial health check
        setTimeout(() => {
            this.updateHealthIndicators();
        }, 500);
    }

    updateHealthIndicators() {
        const indicators = this.modal.querySelectorAll('.health-item');

        indicators.forEach(indicator => {
            const checkType = indicator.dataset.check;
            const result = this.performHealthCheck(checkType);

            const icon = indicator.querySelector('.health-icon');
            const status = indicator.querySelector('.health-status');

            // Remove existing classes
            indicator.classList.remove('healthy', 'warning', 'error');

            // Apply new status
            indicator.classList.add(result.status);
            icon.textContent = result.icon;
            status.textContent = result.message;
        });
    }

    performHealthCheck(checkType) {
        switch (checkType) {
            case 'scenario':
                const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
                if (scenarioSelect && scenarioSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Scenario selected' };
                }
                return { status: 'error', icon: '❌', message: 'No scenario selected' };

            case 'template':
                const templateSelect = this.modal.querySelector('#quick-template-select');
                if (templateSelect && templateSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Template configured' };
                }
                return { status: 'warning', icon: '⚠️', message: 'Template not selected' };

            case 'profile':
                const profileSelect = this.modal.querySelector('#quick-user-profile-select');
                if (profileSelect && profileSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Profile selected' };
                }
                return { status: 'warning', icon: '⚠️', message: 'No profile selected' };

            case 'execution':
                const executionSelect = this.modal.querySelector('#quick-execution-mode-select');
                if (executionSelect && executionSelect.value) {
                    const mode = executionSelect.value;
                    if (mode === 'mock') {
                        return { status: 'healthy', icon: '✅', message: 'Safe mock mode' };
                    } else if (mode.includes('real')) {
                        return { status: 'warning', icon: '⚠️', message: 'Real mode - costs may apply' };
                    }
                }
                return { status: 'error', icon: '❌', message: 'No execution mode selected' };

            default:
                return { status: 'error', icon: '❓', message: 'Unknown check' };
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
        `;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 2000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, checking for quick test modal...');
    const modal = document.getElementById('quick-test-config-modal');
    if (modal) {
        console.log('Quick test modal found, initializing enhancements...');
        window.quickTestModalEnhancements = new QuickTestModalEnhancements();

        // Add a test function for debugging
        window.testCollapsibleSections = function() {
            console.log('Testing collapsible sections...');
            const sections = modal.querySelectorAll('.form-section.collapsible');
            sections.forEach((section, index) => {
                const content = section.querySelector('.section-content');
                const computedStyle = content ? window.getComputedStyle(content) : null;

                console.log(`Section ${index}:`, {
                    collapsed: section.classList.contains('collapsed'),
                    contentInlineDisplay: content ? content.style.display : 'N/A',
                    contentComputedDisplay: computedStyle ? computedStyle.display : 'N/A',
                    contentComputedVisibility: computedStyle ? computedStyle.visibility : 'N/A',
                    hasClickListener: section.querySelector('.section-header')._hasClickListener || false,
                    sectionClasses: section.className,
                    contentClasses: content ? content.className : 'N/A'
                });

                // Check for conflicting CSS rules
                if (content && computedStyle) {
                    console.log(`Section ${index} CSS specificity check:`, {
                        allAppliedRules: Array.from(document.styleSheets).flatMap(sheet => {
                            try {
                                return Array.from(sheet.cssRules || []).filter(rule => {
                                    return rule.selectorText && content.matches(rule.selectorText);
                                }).map(rule => ({
                                    selector: rule.selectorText,
                                    display: rule.style.display,
                                    visibility: rule.style.visibility
                                }));
                            } catch (e) {
                                return [];
                            }
                        })
                    });
                }
            });
        };
    } else {
        console.log('Quick test modal not found');
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickTestModalEnhancements;
}
