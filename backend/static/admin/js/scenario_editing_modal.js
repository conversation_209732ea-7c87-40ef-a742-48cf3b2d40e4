// ACTIVE_FILE - 29-05-2025
/**
 * Scenario Editing Modal Management
 *
 * This module handles the main scenario editing modal functionality,
 * including tab navigation, form management, and high-level UI interactions.
 * Follows the same pattern as the evaluation template modal.
 */

class ScenarioEditingModal {
    constructor() {
        this.modal = null;
        this.currentTab = 'basic';
        this.scenarioData = {};
        this.isEditMode = false;

        this.init();
    }

    init() {
        this.modal = document.getElementById('scenario-modal');
        if (!this.modal) return;

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('scenario-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSave(e));
        }

        // Close modal events
        this.modal.querySelectorAll('.close, .close-modal').forEach(el => {
            el.addEventListener('click', () => this.close());
        });

        // Click outside to close
        window.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.close();
            }
        });
    }

    show(scenarioId = null) {
        console.log(`ScenarioEditingModal.show() called with scenarioId: ${scenarioId}`);
        if (!this.modal) {
            console.error('ScenarioEditingModal.show(): Modal element not found.');
            return;
        }

        this.isEditMode = !!scenarioId;

        if (scenarioId) {
            this.loadScenario(scenarioId);
        } else {
            this.resetForm();
            window.ScenarioModalUtils.setDefaultValues();
        }

        this.initializeTabs();
        this.modal.style.display = 'block';
    }

    close() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
    }

    resetForm() {
        const form = document.getElementById('scenario-form');
        if (form) form.reset();

        document.getElementById('scenario-modal-title').textContent = 'Create New Scenario';
        document.getElementById('scenario-id').value = '';

        this.scenarioData = {};
        this.currentTab = 'basic';
    }

    initializeTabs() {
        // Create tab navigation if it doesn't exist
        let tabNav = this.modal.querySelector('.scenario-tab-nav');
        if (!tabNav) {
            tabNav = document.createElement('div');
            tabNav.className = 'scenario-tab-nav template-tab-nav';
            tabNav.innerHTML = `
                <button type="button" class="scenario-tab-btn template-tab-btn active" data-tab="basic">
                    <i class="fas fa-info-circle"></i> Basic Info
                </button>
                <button type="button" class="scenario-tab-btn template-tab-btn" data-tab="input">
                    <i class="fas fa-keyboard"></i> Input Data
                </button>
                <button type="button" class="scenario-tab-btn template-tab-btn" data-tab="metadata">
                    <i class="fas fa-cogs"></i> Metadata
                </button>
                <button type="button" class="scenario-tab-btn template-tab-btn" data-tab="context">
                    <i class="fas fa-brain"></i> Context Variables
                </button>
                <button type="button" class="scenario-tab-btn template-tab-btn" data-tab="preview">
                    <i class="fas fa-eye"></i> Preview & Test
                </button>
            `;

            // Insert after the modal title
            const title = this.modal.querySelector('#scenario-modal-title');
            if (title) {
                title.parentNode.insertBefore(tabNav, title.nextSibling);
            }
        }

        // Add tab click handlers
        tabNav.querySelectorAll('.scenario-tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(btn.dataset.tab);
            });
        });

        // Initialize with basic tab active
        this.switchTab('basic');

        // Initialize specialized builders
        this.initializeContextBuilder();
    }

    switchTab(tabName) {
        if (!this.modal) return;

        this.currentTab = tabName;

        // Update tab buttons
        this.modal.querySelectorAll('.scenario-tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Show/hide form sections
        const sections = {
            'basic': ['scenario-name', 'scenario-description', 'scenario-agent-role', 'scenario-tags', 'scenario-is-active'],
            'input': ['scenario-input-data'],
            'metadata': ['scenario-workflow-type', 'scenario-metadata'],
            'context': ['scenario-context-variables'],
            'preview': ['scenario-preview-container']
        };

        // Hide all form groups first
        this.modal.querySelectorAll('.form-group').forEach(group => {
            group.style.display = 'none';
        });

        // Hide all custom tab content containers first
        this.modal.querySelectorAll('.scenario-tab-content').forEach(container => {
            container.style.display = 'none';
        });

        // Show relevant form groups for current tab
        if (sections[tabName]) {
            sections[tabName].forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        formGroup.style.display = 'block';
                    }
                }
            });
        }

        // Special handling for custom containers
        const customContainers = {
            'context': 'scenario-context-builder',
            'preview': 'scenario-preview-container'
        };

        if (customContainers[tabName]) {
            const container = this.modal.querySelector(`#${customContainers[tabName]}`);
            if (container) {
                container.style.display = 'block';
            }
        }

        // Trigger tab-specific initialization
        this.onTabSwitch(tabName);
    }

    onTabSwitch(tabName) {
        switch (tabName) {
            case 'context':
                this.refreshContextBuilder();
                break;
            case 'preview':
                this.refreshPreview();
                break;
        }
    }

    initializeContextBuilder() {
        // Create context variables builder container
        let builderContainer = this.modal.querySelector('#scenario-context-builder');
        if (!builderContainer) {
            builderContainer = document.createElement('div');
            builderContainer.id = 'scenario-context-builder';
            builderContainer.className = 'scenario-tab-content';
            builderContainer.innerHTML = `
                <div class="context-builder">
                    <h4>Context Variables</h4>
                    <p class="help-text">Define contextual variables for this scenario that will be used for evaluation adaptation.</p>

                    <div class="context-controls">
                        <div class="control-group">
                            <label>Trust Level: <span id="scenario-trust-level-value">50</span></label>
                            <input type="range" id="scenario-trust-level-slider" min="0" max="100" value="50" class="form-range">
                            <small class="help-text">User's trust level in the system (0-100)</small>
                        </div>

                        <div class="control-group">
                            <label>Mood Valence: <span id="scenario-valence-value">0.0</span></label>
                            <input type="range" id="scenario-valence-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                            <small class="help-text">Emotional positivity (-1.0 to 1.0)</small>
                        </div>

                        <div class="control-group">
                            <label>Mood Arousal: <span id="scenario-arousal-value">0.0</span></label>
                            <input type="range" id="scenario-arousal-slider" min="-1" max="1" step="0.1" value="0" class="form-range">
                            <small class="help-text">Emotional activation (-1.0 to 1.0)</small>
                        </div>

                        <div class="control-group">
                            <label>Stress Level: <span id="scenario-stress-level-value">30</span></label>
                            <input type="range" id="scenario-stress-level-slider" min="0" max="100" value="30" class="form-range">
                            <small class="help-text">Environmental stress level (0-100)</small>
                        </div>

                        <div class="control-group">
                            <label>Time Pressure: <span id="scenario-time-pressure-value">30</span></label>
                            <input type="range" id="scenario-time-pressure-slider" min="0" max="100" value="30" class="form-range">
                            <small class="help-text">Time pressure in the environment (0-100)</small>
                        </div>
                    </div>

                    <div class="preset-scenarios">
                        <h5>Preset Context Scenarios</h5>
                        <div class="scenario-buttons">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.scenarioEditingModal.loadContextPreset('new-user')">New User</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.scenarioEditingModal.loadContextPreset('stressed-user')">Stressed User</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.scenarioEditingModal.loadContextPreset('confident-user')">Confident User</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.scenarioEditingModal.loadContextPreset('low-mood')">Low Mood</button>
                        </div>
                    </div>
                </div>
            `;

            // Insert into modal form
            const form = this.modal.querySelector('#scenario-form');
            if (form) {
                form.appendChild(builderContainer);
            }
        }

        // Initialize slider event listeners
        this.initializeContextSliders();
    }

    initializeContextSliders() {
        const sliders = [
            { id: 'scenario-trust-level-slider', valueId: 'scenario-trust-level-value' },
            { id: 'scenario-valence-slider', valueId: 'scenario-valence-value' },
            { id: 'scenario-arousal-slider', valueId: 'scenario-arousal-value' },
            { id: 'scenario-stress-level-slider', valueId: 'scenario-stress-level-value' },
            { id: 'scenario-time-pressure-slider', valueId: 'scenario-time-pressure-value' }
        ];

        sliders.forEach(({ id, valueId }) => {
            const slider = document.getElementById(id);
            const valueDisplay = document.getElementById(valueId);

            if (slider && valueDisplay) {
                slider.addEventListener('input', function() {
                    valueDisplay.textContent = this.value;
                });
            }
        });
    }

    refreshContextBuilder() {
        // Refresh context builder when tab is switched
        console.log('Refreshing context builder');
    }

    refreshPreview() {
        // Refresh preview when tab is switched
        this.initializePreviewContainer();
        this.updatePreviewContent();
    }

    initializePreviewContainer() {
        // Create preview container if it doesn't exist
        let previewContainer = this.modal.querySelector('#scenario-preview-container');
        if (!previewContainer) {
            previewContainer = document.createElement('div');
            previewContainer.id = 'scenario-preview-container';
            previewContainer.className = 'scenario-tab-content';
        }

        // Update preview content structure
        previewContainer.innerHTML = `
            <div class="scenario-preview">
                <h4>Scenario Preview & Validation</h4>
                <p class="help-text">Preview and validate your scenario configuration before saving.</p>

                <div class="preview-sections">
                    <div class="preview-section">
                        <h5>Basic Information</h5>
                        <div id="preview-basic-info" class="preview-content">
                            <p class="text-muted">Fill in basic information to see preview.</p>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h5>Context Variables Summary</h5>
                        <div id="preview-context-vars" class="preview-content">
                            <p class="text-muted">Configure context variables to see summary.</p>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h5>JSON Validation</h5>
                        <div id="preview-validation" class="preview-content">
                            <button type="button" class="btn btn-primary" onclick="window.scenarioEditingModal.validateScenarioData()">Validate JSON</button>
                            <div id="validation-results" class="validation-results"></div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h5>Sample Generation</h5>
                        <div class="sample-generation">
                            <label for="sample-workflow-type">Workflow Type:</label>
                            <select id="sample-workflow-type" class="form-control" style="width: 200px; display: inline-block; margin: 0 10px;">
                                <option value="wheel_generation">Wheel Generation</option>
                                <option value="goal_setting">Goal Setting</option>
                                <option value="reflection">Reflection</option>
                            </select>
                            <button type="button" class="btn btn-secondary" onclick="window.scenarioEditingModal.generateSampleData()">Generate Sample</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Ensure it's in the form
        const form = this.modal.querySelector('#scenario-form');
        if (form && !form.contains(previewContainer)) {
            form.appendChild(previewContainer);
        }
    }

    updatePreviewContent() {
        // Update basic info preview
        const name = document.getElementById('scenario-name').value || 'Untitled Scenario';
        const description = document.getElementById('scenario-description').value || 'No description';
        const agentRole = document.getElementById('scenario-agent-role').value || 'Not selected';
        const workflowType = document.getElementById('scenario-workflow-type').value || 'Not selected';
        const isActive = document.getElementById('scenario-is-active').value === 'true' ? 'Active' : 'Inactive';

        const basicInfoElement = document.getElementById('preview-basic-info');
        if (basicInfoElement) {
            basicInfoElement.innerHTML = `
                <div class="info-grid">
                    <div><strong>Name:</strong> ${name}</div>
                    <div><strong>Description:</strong> ${description}</div>
                    <div><strong>Agent Role:</strong> ${agentRole}</div>
                    <div><strong>Workflow Type:</strong> ${workflowType}</div>
                    <div><strong>Status:</strong> ${isActive}</div>
                </div>
            `;
        }

        // Update context variables preview
        this.updateContextPreview();
    }

    updateContextPreview() {
        const contextVarsElement = document.getElementById('preview-context-vars');
        if (!contextVarsElement) return;

        const trustLevel = document.getElementById('scenario-trust-level-slider')?.value || 50;
        const valence = document.getElementById('scenario-valence-slider')?.value || 0;
        const arousal = document.getElementById('scenario-arousal-slider')?.value || 0;
        const stressLevel = document.getElementById('scenario-stress-level-slider')?.value || 30;
        const timePressure = document.getElementById('scenario-time-pressure-slider')?.value || 30;

        // Determine trust phase
        let trustPhase = 'Foundation';
        if (trustLevel >= 70) trustPhase = 'Integration';
        else if (trustLevel >= 40) trustPhase = 'Expansion';

        // Determine mood quadrant
        let moodQuadrant = 'Neutral';
        if (valence > 0 && arousal > 0) moodQuadrant = 'Excited/Happy';
        else if (valence > 0 && arousal < 0) moodQuadrant = 'Calm/Content';
        else if (valence < 0 && arousal > 0) moodQuadrant = 'Stressed/Angry';
        else if (valence < 0 && arousal < 0) moodQuadrant = 'Sad/Depressed';

        contextVarsElement.innerHTML = `
            <div class="context-summary">
                <div class="context-item">
                    <strong>Trust Level:</strong> ${trustLevel} (${trustPhase} Phase)
                </div>
                <div class="context-item">
                    <strong>Mood:</strong> Valence: ${valence}, Arousal: ${arousal} (${moodQuadrant})
                </div>
                <div class="context-item">
                    <strong>Environment:</strong> Stress: ${stressLevel}, Time Pressure: ${timePressure}
                </div>
            </div>
        `;
    }

    validateScenarioData() {
        const validationResults = document.getElementById('validation-results');
        if (!validationResults) return;

        try {
            const scenarioData = window.ScenarioModalUtils.collectFormData();
            const errors = window.ScenarioModalUtils.validateScenarioData(scenarioData);

            if (errors.length === 0) {
                validationResults.innerHTML = '<div class="alert alert-success">✓ Scenario data is valid!</div>';
            } else {
                const errorList = errors.map(error => `<li>${error}</li>`).join('');
                validationResults.innerHTML = `<div class="alert alert-danger">Validation errors:<ul>${errorList}</ul></div>`;
            }
        } catch (error) {
            validationResults.innerHTML = `<div class="alert alert-danger">Error during validation: ${error.message}</div>`;
        }
    }

    generateSampleData() {
        const workflowType = document.getElementById('sample-workflow-type').value;
        const sampleData = window.ScenarioModalUtils.generateSampleData(workflowType);

        // Update form fields with sample data
        document.getElementById('scenario-input-data').value = JSON.stringify(sampleData.input_data, null, 2);
        document.getElementById('scenario-metadata').value = JSON.stringify(sampleData.metadata, null, 2);
        document.getElementById('scenario-workflow-type').value = workflowType;

        // Update preview
        this.updatePreviewContent();

        // Show success message
        if (window.showSuccess) {
            window.showSuccess(`Sample data generated for ${workflowType} workflow`);
        }
    }

    loadContextPreset(presetName) {
        const presets = {
            'new-user': {
                trust_level: 25,
                valence: -0.2,
                arousal: 0.1,
                stress_level: 60,
                time_pressure: 40
            },
            'stressed-user': {
                trust_level: 40,
                valence: -0.5,
                arousal: 0.7,
                stress_level: 85,
                time_pressure: 80
            },
            'confident-user': {
                trust_level: 85,
                valence: 0.6,
                arousal: 0.3,
                stress_level: 20,
                time_pressure: 25
            },
            'low-mood': {
                trust_level: 35,
                valence: -0.7,
                arousal: -0.4,
                stress_level: 50,
                time_pressure: 35
            }
        };

        const preset = presets[presetName];
        if (!preset) return;

        // Update sliders and values
        document.getElementById('scenario-trust-level-slider').value = preset.trust_level;
        document.getElementById('scenario-trust-level-value').textContent = preset.trust_level;

        document.getElementById('scenario-valence-slider').value = preset.valence;
        document.getElementById('scenario-valence-value').textContent = preset.valence;

        document.getElementById('scenario-arousal-slider').value = preset.arousal;
        document.getElementById('scenario-arousal-value').textContent = preset.arousal;

        document.getElementById('scenario-stress-level-slider').value = preset.stress_level;
        document.getElementById('scenario-stress-level-value').textContent = preset.stress_level;

        document.getElementById('scenario-time-pressure-slider').value = preset.time_pressure;
        document.getElementById('scenario-time-pressure-value').textContent = preset.time_pressure;
    }

    async loadScenario(scenarioId) {
        try {
            const data = await window.ScenarioModalUtils.fetchScenario(scenarioId);
            this.scenarioData = data.scenario;

            window.ScenarioModalUtils.populateForm(this.scenarioData);

            document.getElementById('scenario-modal-title').textContent = 'Edit Scenario';

        } catch (error) {
            console.error('Error loading scenario:', error);
            if (window.showError) {
                window.showError(`Error loading scenario: ${error.message}`);
            }
        }
    }

    async handleSave(event) {
        event.preventDefault();

        try {
            const scenarioData = window.ScenarioModalUtils.collectFormData();

            // Validate data
            const validationErrors = window.ScenarioModalUtils.validateScenarioData(scenarioData);
            if (validationErrors.length > 0) {
                if (window.showError) {
                    window.showError(`Validation errors: ${validationErrors.join(', ')}`, 'scenario-error-message');
                }
                return;
            }

            // Save scenario
            await window.ScenarioModalUtils.saveScenario(scenarioData, this.isEditMode);

            // Success
            this.close();

            if (window.showSuccess) {
                window.showSuccess(this.isEditMode ? 'Scenario updated successfully' : 'Scenario created successfully');
            }

            if (window.loadScenarios) {
                window.loadScenarios();
            }

        } catch (error) {
            console.error('Error saving scenario:', error);
            if (window.showError) {
                window.showError(`Error saving scenario: ${error.message}`, 'scenario-error-message');
            }
        }
    }
}

// Initialize when DOM is loaded
function initializeScenarioEditingModal() {
    if (!window.scenarioEditingModal) {
        console.log('Initializing ScenarioEditingModal...');
        window.scenarioEditingModal = new ScenarioEditingModal();
        console.log('ScenarioEditingModal initialized successfully');
    }
}

// Try to initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeScenarioEditingModal);
} else {
    // DOM is already loaded
    initializeScenarioEditingModal();
}

// Export for global access
window.ScenarioEditingModal = ScenarioEditingModal;
window.initializeScenarioEditingModal = initializeScenarioEditingModal;
