// ACTIVE_FILE - 29-05-2025
/**
 * Scenario Modal Utilities
 *
 * This module provides utility functions for the scenario editing modal,
 * including API calls, form data handling, validation, and sample data generation.
 */

class ScenarioModalUtils {
    constructor() {
        this.apiBaseUrl = window.BENCHMARK_SCENARIOS_API_URL || '/admin/benchmarks/api/scenarios/';
    }

    /**
     * Fetch scenario data from the API
     */
    async fetchScenario(scenarioId) {
        const response = await fetch(`${this.apiBaseUrl}${scenarioId}/`);
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        return await response.json();
    }

    /**
     * Save scenario data to the API
     */
    async saveScenario(scenarioData, isEditMode) {
        const url = isEditMode ? `${this.apiBaseUrl}${scenarioData.id}/` : this.apiBaseUrl;
        const method = isEditMode ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify(scenarioData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Get CSRF token from cookies
     */
    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    /**
     * Populate form with scenario data
     */
    populateForm(scenarioData) {
        // Basic info
        document.getElementById('scenario-name').value = scenarioData.name || '';
        document.getElementById('scenario-description').value = scenarioData.description || '';
        document.getElementById('scenario-agent-role').value = scenarioData.agent_role || '';
        document.getElementById('scenario-is-active').value = scenarioData.is_active ? 'true' : 'false';
        document.getElementById('scenario-id').value = scenarioData.id || '';

        // Tags
        if (scenarioData.tags && Array.isArray(scenarioData.tags)) {
            const tagNames = scenarioData.tags.map(tag => tag.name || tag).join(', ');
            document.getElementById('scenario-tags').value = tagNames;
        }

        // Input data
        if (scenarioData.input_data) {
            document.getElementById('scenario-input-data').value = JSON.stringify(scenarioData.input_data, null, 2);
        }

        // Metadata
        if (scenarioData.metadata) {
            document.getElementById('scenario-metadata').value = JSON.stringify(scenarioData.metadata, null, 2);
            
            // Set workflow type from metadata
            const workflowType = scenarioData.metadata.workflow_type;
            if (workflowType) {
                document.getElementById('scenario-workflow-type').value = workflowType;
            }
        }

        // Context variables
        this.populateContextVariables(scenarioData);
    }

    /**
     * Populate context variables from scenario data
     */
    populateContextVariables(scenarioData) {
        let contextVars = {};

        // Try to get context from metadata first
        if (scenarioData.metadata && scenarioData.metadata.context) {
            contextVars = scenarioData.metadata.context;
        }
        // Also check input_data.context_packet for legacy scenarios
        else if (scenarioData.input_data && scenarioData.input_data.context_packet) {
            const contextPacket = scenarioData.input_data.context_packet;
            if (contextPacket.trust_level !== undefined) {
                contextVars.trust_level = contextPacket.trust_level;
            }
        }

        // Set context variable sliders
        if (contextVars.trust_level !== undefined) {
            const slider = document.getElementById('scenario-trust-level-slider');
            const value = document.getElementById('scenario-trust-level-value');
            if (slider && value) {
                slider.value = contextVars.trust_level;
                value.textContent = contextVars.trust_level;
            }
        }

        if (contextVars.mood) {
            if (contextVars.mood.valence !== undefined) {
                const slider = document.getElementById('scenario-valence-slider');
                const value = document.getElementById('scenario-valence-value');
                if (slider && value) {
                    slider.value = contextVars.mood.valence;
                    value.textContent = contextVars.mood.valence;
                }
            }

            if (contextVars.mood.arousal !== undefined) {
                const slider = document.getElementById('scenario-arousal-slider');
                const value = document.getElementById('scenario-arousal-value');
                if (slider && value) {
                    slider.value = contextVars.mood.arousal;
                    value.textContent = contextVars.mood.arousal;
                }
            }
        }

        if (contextVars.environment) {
            if (contextVars.environment.stress_level !== undefined) {
                const slider = document.getElementById('scenario-stress-level-slider');
                const value = document.getElementById('scenario-stress-level-value');
                if (slider && value) {
                    slider.value = contextVars.environment.stress_level;
                    value.textContent = contextVars.environment.stress_level;
                }
            }

            if (contextVars.environment.time_pressure !== undefined) {
                const slider = document.getElementById('scenario-time-pressure-slider');
                const value = document.getElementById('scenario-time-pressure-value');
                if (slider && value) {
                    slider.value = contextVars.environment.time_pressure;
                    value.textContent = contextVars.environment.time_pressure;
                }
            }
        }
    }

    /**
     * Collect form data into scenario object
     */
    collectFormData() {
        const scenarioData = {
            name: document.getElementById('scenario-name').value.trim(),
            description: document.getElementById('scenario-description').value.trim(),
            agent_role: document.getElementById('scenario-agent-role').value,
            is_active: document.getElementById('scenario-is-active').value === 'true'
        };

        // Add ID if editing
        const scenarioId = document.getElementById('scenario-id').value;
        if (scenarioId) {
            scenarioData.id = parseInt(scenarioId);
        }

        // Process tags
        const tagsValue = document.getElementById('scenario-tags').value.trim();
        if (tagsValue) {
            scenarioData.tags = tagsValue.split(',').map(tag => tag.trim()).filter(tag => tag);
        } else {
            scenarioData.tags = [];
        }

        // Process input data
        try {
            const inputDataValue = document.getElementById('scenario-input-data').value.trim();
            scenarioData.input_data = inputDataValue ? JSON.parse(inputDataValue) : {};
        } catch (e) {
            throw new Error('Invalid JSON in Input Data field');
        }

        // Process metadata
        try {
            const metadataValue = document.getElementById('scenario-metadata').value.trim();
            scenarioData.metadata = metadataValue ? JSON.parse(metadataValue) : {};
        } catch (e) {
            throw new Error('Invalid JSON in Metadata field');
        }

        // Ensure workflow_type is consistent
        const workflowType = document.getElementById('scenario-workflow-type').value;
        if (workflowType) {
            scenarioData.metadata.workflow_type = workflowType;
        }

        // Add context variables to metadata
        const contextVars = this.collectContextVariables();
        if (Object.keys(contextVars).length > 0) {
            scenarioData.metadata.context = contextVars;
        }

        return scenarioData;
    }

    /**
     * Collect context variables from sliders
     */
    collectContextVariables() {
        const contextVars = {};

        // Trust level
        const trustLevel = document.getElementById('scenario-trust-level-slider');
        if (trustLevel && trustLevel.value !== '50') { // Only include if not default
            contextVars.trust_level = parseInt(trustLevel.value);
        }

        // Mood variables
        const valence = document.getElementById('scenario-valence-slider');
        const arousal = document.getElementById('scenario-arousal-slider');
        
        if ((valence && valence.value !== '0') || (arousal && arousal.value !== '0')) {
            contextVars.mood = {};
            if (valence && valence.value !== '0') {
                contextVars.mood.valence = parseFloat(valence.value);
            }
            if (arousal && arousal.value !== '0') {
                contextVars.mood.arousal = parseFloat(arousal.value);
            }
        }

        // Environment variables
        const stressLevel = document.getElementById('scenario-stress-level-slider');
        const timePressure = document.getElementById('scenario-time-pressure-slider');
        
        if ((stressLevel && stressLevel.value !== '30') || (timePressure && timePressure.value !== '30')) {
            contextVars.environment = {};
            if (stressLevel && stressLevel.value !== '30') {
                contextVars.environment.stress_level = parseInt(stressLevel.value);
            }
            if (timePressure && timePressure.value !== '30') {
                contextVars.environment.time_pressure = parseInt(timePressure.value);
            }
        }

        return contextVars;
    }

    /**
     * Validate scenario data
     */
    validateScenarioData(scenarioData) {
        const errors = [];

        // Required fields
        if (!scenarioData.name) {
            errors.push('Name is required');
        }
        if (!scenarioData.agent_role) {
            errors.push('Agent role is required');
        }
        if (!scenarioData.input_data || Object.keys(scenarioData.input_data).length === 0) {
            errors.push('Input data is required');
        }
        if (!scenarioData.metadata || Object.keys(scenarioData.metadata).length === 0) {
            errors.push('Metadata is required');
        }

        // Workflow type validation
        if (scenarioData.metadata && !scenarioData.metadata.workflow_type) {
            errors.push('Workflow type is required in metadata');
        }

        return errors;
    }

    /**
     * Set default values for new scenarios
     */
    setDefaultValues() {
        // Set default input data template
        const defaultInputData = {
            "user_message": "Enter the user's message here",
            "context_packet": {
                "workflow_type": "wheel_generation",
                "trust_level": 50
            }
        };

        document.getElementById('scenario-input-data').value = JSON.stringify(defaultInputData, null, 2);

        // Set default metadata template
        const defaultMetadata = {
            "workflow_type": "wheel_generation",
            "user_profile_context": {
                "trust_level": 50,
                "preferences": {
                    "activity_types": ["outdoor", "creative"]
                }
            },
            "expected_quality_criteria": {
                "Content": ["Should be relevant and helpful"],
                "Tone": ["Should be supportive and encouraging"]
            },
            "warmup_runs": 1,
            "benchmark_runs": 3
        };

        document.getElementById('scenario-metadata').value = JSON.stringify(defaultMetadata, null, 2);

        // Set default workflow type
        document.getElementById('scenario-workflow-type').value = 'wheel_generation';
    }

    /**
     * Generate sample scenario data for different workflow types
     */
    generateSampleData(workflowType) {
        const samples = {
            'wheel_generation': {
                input_data: {
                    "user_message": "I want to try something new today",
                    "context_packet": {
                        "workflow_type": "wheel_generation",
                        "trust_level": 50
                    }
                },
                metadata: {
                    "workflow_type": "wheel_generation",
                    "user_profile_context": {
                        "trust_level": 50,
                        "preferences": {
                            "activity_types": ["outdoor", "creative"]
                        }
                    },
                    "expected_quality_criteria": {
                        "Content": ["Relevant activities", "Appropriate difficulty"],
                        "Tone": ["Encouraging", "Supportive"]
                    },
                    "mock_tool_responses": {
                        "get_user_profile": {
                            "response": "{\"id\": \"123\", \"name\": \"Test User\"}"
                        }
                    },
                    "warmup_runs": 1,
                    "benchmark_runs": 3
                }
            }
        };

        return samples[workflowType] || samples['wheel_generation'];
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.ScenarioModalUtils = new ScenarioModalUtils();
});

// Export for global access
window.ScenarioModalUtils = ScenarioModalUtils;
