// ACTIVE_FILE - 29-05-2025
/**
 * Template Modal Utilities
 *
 * This module contains utility functions and business logic for the evaluation
 * template modal, including form data handling, validation, and API interactions.
 */

window.TemplateModalUtils = {

    /**
     * Help content registry for contextual help system
     */
    helpContent: {
        'template-name': {
            title: 'Template Name',
            content: `
                <p>A descriptive name for this evaluation template that clearly indicates its purpose and context-awareness.</p>
                <h6>Best Practices:</h6>
                <ul>
                    <li>Use descriptive names like "Wellness Coaching Evaluation" or "Customer Support Contextual"</li>
                    <li>Include the domain or use case in the name</li>
                    <li>Indicate if it's contextual vs. static evaluation</li>
                </ul>
                <h6>Examples:</h6>
                <ul>
                    <li>"Mental Health Support - Trust Adaptive"</li>
                    <li>"Educational AI Tutor - Stress Aware"</li>
                    <li>"Leadership Coaching - Experience Based"</li>
                </ul>
            `
        },
        'template-category-select': {
            title: 'Template Categories',
            content: `
                <p>Categories help organize and classify evaluation templates based on their evaluation approach.</p>
                <h6>Category Types:</h6>
                <ul>
                    <li><strong>Semantic:</strong> Focuses on meaning and understanding in responses</li>
                    <li><strong>Quality:</strong> General quality assessment criteria</li>
                    <li><strong>Phase-based:</strong> Evaluation varies by workflow phases</li>
                    <li><strong>Contextual:</strong> Adapts based on user context variables</li>
                    <li><strong>Custom:</strong> Specialized evaluation approaches</li>
                </ul>
                <h6>When to Use Contextual:</h6>
                <p>Choose "Contextual" when your template adapts evaluation criteria based on variables like trust level, mood, or environmental factors.</p>
            `
        },
        'template-criteria': {
            title: 'Base Evaluation Criteria',
            content: `
                <p>Base criteria define the fundamental evaluation standards that apply regardless of context. These form the quality baseline for all evaluations.</p>
                <h6>JSON Structure:</h6>
                <pre>{
  "Content": ["Relevance", "Accuracy", "Completeness"],
  "Tone": ["Appropriate", "Supportive", "Professional"],
  "Structure": ["Clear", "Organized", "Logical"]
}</pre>
                <h6>Common Dimensions:</h6>
                <ul>
                    <li><strong>Content:</strong> Information quality and relevance</li>
                    <li><strong>Tone:</strong> Communication style and appropriateness</li>
                    <li><strong>Structure:</strong> Organization and clarity</li>
                    <li><strong>Safety:</strong> Risk awareness and boundaries</li>
                    <li><strong>Personalization:</strong> Adaptation to user needs</li>
                </ul>
            `
        },
        'contextual-criteria-help': {
            title: 'Contextual Evaluation Criteria',
            content: `
                <p>Contextual criteria adapt evaluation standards based on user context variables like trust level, mood, and environmental factors.</p>
                <h6>Supported Variables:</h6>
                <ul>
                    <li><strong>Trust Level (0-100):</strong> User's confidence in the system</li>
                    <li><strong>Mood Valence (-1.0 to 1.0):</strong> Emotional positivity/negativity</li>
                    <li><strong>Mood Arousal (-1.0 to 1.0):</strong> Emotional activation level</li>
                    <li><strong>Stress Level (0-100):</strong> Current environmental stress</li>
                    <li><strong>Time Pressure (0-100):</strong> Urgency in current situation</li>
                </ul>
                <h6>Trust Level Phases:</h6>
                <ul>
                    <li><strong>Foundation (0-39):</strong> Simple, clear, reassuring criteria</li>
                    <li><strong>Expansion (40-69):</strong> Encouraging, supportive criteria</li>
                    <li><strong>Integration (70-100):</strong> Collaborative, empowering criteria</li>
                </ul>
            `
        },
        'variable-ranges-help': {
            title: 'Variable Ranges Configuration',
            content: `
                <p>Define the supported ranges for contextual variables that your template can handle.</p>
                <h6>Range Format Examples:</h6>
                <ul>
                    <li><strong>Integer ranges:</strong> "0-39", "40-69", "70-100"</li>
                    <li><strong>Float ranges:</strong> "-1.0-0.0", "0.0-1.0"</li>
                    <li><strong>Negative ranges:</strong> "-1.0-0.0" for negative mood valence</li>
                </ul>
                <h6>Variable Types:</h6>
                <ul>
                    <li><strong>trust_level:</strong> 0-100 scale for user confidence</li>
                    <li><strong>mood.valence:</strong> -1.0 to 1.0 for emotional positivity</li>
                    <li><strong>mood.arousal:</strong> -1.0 to 1.0 for activation level</li>
                    <li><strong>environment.stress_level:</strong> 0-100 for stress intensity</li>
                    <li><strong>environment.time_pressure:</strong> 0-100 for urgency</li>
                </ul>
            `
        },
        'context-preview-help': {
            title: 'Context Preview & Testing',
            content: `
                <p>Test how your evaluation template adapts to different user contexts using the interactive simulator.</p>
                <h6>Testing Scenarios:</h6>
                <ul>
                    <li><strong>New User:</strong> Low trust (20), cautious mood</li>
                    <li><strong>Stressed User:</strong> High stress (80), negative mood</li>
                    <li><strong>Confident User:</strong> High trust (85), positive mood</li>
                    <li><strong>Overwhelmed:</strong> High stress (90), low trust (15)</li>
                </ul>
                <h6>What to Validate:</h6>
                <ul>
                    <li>Criteria adapt meaningfully to context changes</li>
                    <li>No contradictory requirements across contexts</li>
                    <li>Realistic expectations for each context</li>
                    <li>Smooth transitions between ranges</li>
                </ul>
            `
        },
        'trust-level-help': {
            title: 'Trust Level Psychology',
            content: `
                <p>Trust in AI systems follows predictable patterns based on user experience and system reliability.</p>
                <h6>Trust Development Phases:</h6>
                <ul>
                    <li><strong>Foundation (0-39):</strong> "Can I rely on this system?"
                        <br>Needs: Safety, predictability, clear boundaries
                        <br>Criteria: Consistency, accuracy, appropriate caution</li>
                    <li><strong>Expansion (40-69):</strong> "This system understands me"
                        <br>Needs: Encouragement, balanced challenge, collaboration
                        <br>Criteria: Supportiveness, growth orientation, balanced risk-taking</li>
                    <li><strong>Integration (70-100):</strong> "This is my trusted partner"
                        <br>Needs: Challenge, creativity, independence support
                        <br>Criteria: Innovation, empowerment, sophisticated guidance</li>
                </ul>
            `
        },
        'mood-help': {
            title: 'Mood Variables (Circumplex Model)',
            content: `
                <p>Based on the circumplex model of affect, mood has two key dimensions:</p>
                <h6>Valence (-1.0 to 1.0): Emotional Positivity</h6>
                <ul>
                    <li><strong>Negative (-1.0 to -0.1):</strong> Sad, frustrated, anxious
                        <br>Needs: Gentle approach, patience, understanding</li>
                    <li><strong>Positive (0.1 to 1.0):</strong> Happy, excited, optimistic
                        <br>Needs: Matching energy, building momentum, channeling enthusiasm</li>
                </ul>
                <h6>Arousal (-1.0 to 1.0): Emotional Activation</h6>
                <ul>
                    <li><strong>Low (-1.0 to -0.1):</strong> Calm, relaxed, tired
                        <br>Needs: Gentle stimulation, comfort, steady progress</li>
                    <li><strong>High (0.1 to 1.0):</strong> Alert, excited, energetic
                        <br>Needs: Channel energy, provide focus, match intensity</li>
                </ul>
            `
        },
        'environment-help': {
            title: 'Environmental Context Factors',
            content: `
                <p>Environmental factors significantly impact how users process information and make decisions.</p>
                <h6>Stress Level (0-100):</h6>
                <ul>
                    <li><strong>Low (0-30):</strong> Optimal learning state
                        <br>Can handle complexity, comprehensive explanations</li>
                    <li><strong>Medium (31-70):</strong> Focused performance
                        <br>Prefers structured, relevant information</li>
                    <li><strong>High (71-100):</strong> Survival mode
                        <br>Essential information only, simple language</li>
                </ul>
                <h6>Time Pressure (0-100):</h6>
                <ul>
                    <li><strong>Low (0-30):</strong> Thoughtful planning
                        <br>Long-term thinking, comprehensive planning</li>
                    <li><strong>High (71-100):</strong> Immediate action
                        <br>Quick decisions, immediate implementation</li>
                </ul>
            `
        }
    },

    /**
     * Set default values for a new template
     */
    setDefaultValues() {
        // Set default sample criteria
        const criteriaTextarea = document.getElementById('template-criteria');
        if (criteriaTextarea) {
            criteriaTextarea.value = JSON.stringify({
                "Content": ["Relevance", "Accuracy", "Completeness"],
                "Tone": ["Appropriate", "Supportive", "Professional"],
                "Structure": ["Clear", "Organized", "Logical"]
            }, null, 2);
        }

        // Clear contextual fields
        const contextualTextarea = document.getElementById('template-contextual-criteria');
        if (contextualTextarea) contextualTextarea.value = '';

        const variableRangesTextarea = document.getElementById('template-variable-ranges');
        if (variableRangesTextarea) variableRangesTextarea.value = '';
    },

    /**
     * Fetch template data from API
     */
    async fetchTemplate(templateId) {
        const response = await fetch(`${window.TEMPLATES_API_URL}${templateId}/`);

        let data;
        try {
            data = await response.json();
        } catch (parseError) {
            throw new Error(`Failed to parse server response: ${parseError.message}`);
        }

        if (!response.ok) {
            // Extract detailed error message from the response
            const errorMessage = data.error || `HTTP error ${response.status}`;
            throw new Error(errorMessage);
        }

        return data;
    },

    /**
     * Populate form with template data
     */
    populateForm(templateData) {
        // Populate basic form fields
        document.getElementById('template-name').value = templateData.name || '';
        document.getElementById('template-description').value = templateData.description || '';
        document.getElementById('template-workflow-type-select').value = templateData.workflow_type || '';
        document.getElementById('template-category-select').value = templateData.category || 'quality';
        document.getElementById('template-is-active').value = templateData.is_active ? 'true' : 'false';
        document.getElementById('template-criteria').value = JSON.stringify(templateData.criteria || {}, null, 2);
        document.getElementById('template-contextual-criteria').value = JSON.stringify(templateData.contextual_criteria || {}, null, 2);
        document.getElementById('template-variable-ranges').value = JSON.stringify(templateData.variable_ranges || {}, null, 2);
        document.getElementById('template-id').value = templateData.id;

        // Populate specialized builders
        if (window.contextualCriteriaBuilder && templateData.contextual_criteria) {
            window.contextualCriteriaBuilder.loadData(templateData.contextual_criteria);
        }

        if (window.variableRangesBuilder && templateData.variable_ranges) {
            window.variableRangesBuilder.loadData(templateData.variable_ranges);
        }
    },

    /**
     * Collect form data into template object
     */
    collectFormData() {
        const name = document.getElementById('template-name').value;
        const description = document.getElementById('template-description').value;
        const workflowType = document.getElementById('template-workflow-type-select').value;
        const category = document.getElementById('template-category-select').value;
        const isActive = document.getElementById('template-is-active').value === 'true';
        const criteria = document.getElementById('template-criteria').value;
        const contextualCriteria = document.getElementById('template-contextual-criteria').value;
        const variableRanges = document.getElementById('template-variable-ranges').value;
        const templateId = document.getElementById('template-id').value;

        return {
            id: templateId || undefined,
            name,
            description,
            workflow_type: workflowType,
            category,
            is_active: isActive,
            criteria: JSON.parse(criteria),
            contextual_criteria: contextualCriteria.trim() ? JSON.parse(contextualCriteria) : {},
            variable_ranges: variableRanges.trim() ? JSON.parse(variableRanges) : {}
        };
    },

    /**
     * Validate template data
     */
    validateTemplateData(data) {
        const errors = [];

        if (!data.name) errors.push('Name is required');
        if (!data.criteria) errors.push('Base criteria is required');

        // Validate JSON fields
        try {
            if (typeof data.criteria === 'string') JSON.parse(data.criteria);
        } catch (e) {
            errors.push('Invalid JSON in base criteria');
        }

        try {
            if (data.contextual_criteria && typeof data.contextual_criteria === 'string') {
                JSON.parse(data.contextual_criteria);
            }
        } catch (e) {
            errors.push('Invalid JSON in contextual criteria');
        }

        try {
            if (data.variable_ranges && typeof data.variable_ranges === 'string') {
                JSON.parse(data.variable_ranges);
            }
        } catch (e) {
            errors.push('Invalid JSON in variable ranges');
        }

        return errors;
    },

    /**
     * Save template via API
     */
    async saveTemplate(templateData, isEditMode) {
        const url = isEditMode ?
            `${window.TEMPLATES_API_URL}${templateData.id}/` :
            window.TEMPLATES_API_URL;
        const method = isEditMode ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.getCsrfToken ? window.getCsrfToken() : ''
            },
            body: JSON.stringify(templateData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }

        return await response.json();
    },

    /**
     * Generate sample template data based on category
     */
    generateSampleTemplate(category = 'quality') {
        const templates = {
            semantic: {
                name: "Semantic Evaluation Template",
                description: "Template for semantic analysis and content evaluation",
                category: "semantic",
                criteria: {
                    "Semantic_Accuracy": [
                        "Factual correctness",
                        "Logical consistency",
                        "Contextual appropriateness"
                    ],
                    "Content_Quality": [
                        "Relevance to user query",
                        "Completeness of response",
                        "Clarity of expression"
                    ],
                    "Language_Use": [
                        "Grammar and syntax",
                        "Vocabulary appropriateness",
                        "Tone consistency"
                    ]
                }
            },
            quality: {
                name: "Quality Assessment Template",
                description: "General quality evaluation template",
                category: "quality",
                criteria: {
                    "Content": [
                        "Relevance",
                        "Accuracy",
                        "Completeness",
                        "Depth of analysis"
                    ],
                    "Tone": [
                        "Appropriate for context",
                        "Supportive and encouraging",
                        "Professional",
                        "Empathetic"
                    ],
                    "Structure": [
                        "Clear organization",
                        "Logical flow",
                        "Easy to follow",
                        "Well-formatted"
                    ]
                }
            },
            contextual: {
                name: "Contextual Evaluation Template",
                description: "Context-aware evaluation template with adaptive criteria",
                category: "contextual",
                criteria: {
                    "Content": ["Relevance", "Personalization"],
                    "Tone": ["Appropriate", "Supportive"]
                },
                contextual_criteria: {
                    "trust_level": {
                        "0-39": {
                            "Tone": ["Simple", "Clear", "Reassuring"],
                            "Content": ["Safe options", "Low-risk activities"]
                        },
                        "40-69": {
                            "Tone": ["Encouraging", "Supportive"],
                            "Content": ["Balanced options", "Some stretch goals"]
                        },
                        "70-100": {
                            "Tone": ["Collaborative", "Empowering"],
                            "Content": ["Ambitious goals", "Creative challenges"]
                        }
                    },
                    "mood": {
                        "valence": {
                            "-1.0-0.0": {
                                "Tone": ["Gentle", "Understanding", "Patient"]
                            },
                            "0.0-1.0": {
                                "Tone": ["Enthusiastic", "Energetic", "Positive"]
                            }
                        }
                    }
                },
                variable_ranges: {
                    "trust_level": {"min": 0, "max": 100},
                    "mood": {
                        "valence": {"min": -1.0, "max": 1.0},
                        "arousal": {"min": -1.0, "max": 1.0}
                    },
                    "environment": {
                        "stress_level": {"min": 0, "max": 100},
                        "time_pressure": {"min": 0, "max": 100}
                    }
                }
            },
            custom: {
                name: "Custom Evaluation Template",
                description: "Customizable template for specific evaluation needs",
                category: "custom",
                criteria: {
                    "Criterion_1": [
                        "Evaluation point 1",
                        "Evaluation point 2",
                        "Evaluation point 3"
                    ],
                    "Criterion_2": [
                        "Evaluation point 1",
                        "Evaluation point 2"
                    ]
                }
            }
        };

        const template = templates[category] || templates.quality;
        return {
            ...template,
            is_active: true,
            version: "1.0.0",
            created_at: new Date().toISOString()
        };
    },

    /**
     * Load sample template into form
     */
    loadSampleTemplate(category) {
        const template = this.generateSampleTemplate(category);

        // Populate basic fields
        const nameInput = document.getElementById('template-name');
        const descInput = document.getElementById('template-description');
        const categorySelect = document.getElementById('template-category-select');
        const criteriaTextarea = document.getElementById('template-criteria');
        const contextualTextarea = document.getElementById('template-contextual-criteria');
        const variableRangesTextarea = document.getElementById('template-variable-ranges');

        if (nameInput) nameInput.value = template.name;
        if (descInput) descInput.value = template.description;
        if (categorySelect) categorySelect.value = template.category;
        if (criteriaTextarea) criteriaTextarea.value = JSON.stringify(template.criteria, null, 2);

        if (contextualTextarea && template.contextual_criteria) {
            contextualTextarea.value = JSON.stringify(template.contextual_criteria, null, 2);
        }

        if (variableRangesTextarea && template.variable_ranges) {
            variableRangesTextarea.value = JSON.stringify(template.variable_ranges, null, 2);
        }

        // Update builders if available
        if (window.contextualCriteriaBuilder && template.contextual_criteria) {
            window.contextualCriteriaBuilder.loadData(template.contextual_criteria);
        }

        if (window.variableRangesBuilder && template.variable_ranges) {
            window.variableRangesBuilder.loadData(template.variable_ranges);
        }
    }
};
