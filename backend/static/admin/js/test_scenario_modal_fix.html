<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scenario Modal Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Scenario Modal Initialization Fix Test</h1>
        <p>This page tests the scenario modal initialization fix to ensure the "ScenarioEditingModal not available" error is resolved.</p>
        
        <div class="test-result info">
            <strong>Test Status:</strong> <span id="test-status">Ready to test</span>
        </div>
        
        <div>
            <button onclick="testScenarioModalAvailability()">Test Modal Availability</button>
            <button onclick="testEditScenarioFunction()">Test Edit Scenario Function</button>
            <button onclick="testManualInitialization()">Test Manual Initialization</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="log" id="test-log"></div>
        
        <!-- Mock scenario modal HTML -->
        <div id="scenario-modal" style="display: none;">
            <div>
                <h3 id="scenario-modal-title">Edit Scenario</h3>
                <form id="scenario-form">
                    <input type="hidden" id="scenario-id">
                    <input type="text" id="scenario-name" placeholder="Scenario Name">
                    <textarea id="scenario-description" placeholder="Description"></textarea>
                    <select id="scenario-agent-role">
                        <option value="mentor">Mentor</option>
                        <option value="coach">Coach</option>
                    </select>
                    <input type="text" id="scenario-tags" placeholder="Tags">
                    <select id="scenario-is-active">
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </select>
                    <textarea id="scenario-input-data" placeholder="Input Data JSON"></textarea>
                    <textarea id="scenario-metadata" placeholder="Metadata JSON"></textarea>
                    <select id="scenario-workflow-type">
                        <option value="wheel_generation">Wheel Generation</option>
                        <option value="goal_setting">Goal Setting</option>
                    </select>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Mock console and window functions
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        console.log = function(...args) {
            log(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };
        
        window.showError = function(message) {
            log(`showError called: ${message}`, 'error');
            document.getElementById('test-status').textContent = 'Error: ' + message;
        };
        
        window.showSuccess = function(message) {
            log(`showSuccess called: ${message}`, 'success');
            document.getElementById('test-status').textContent = 'Success: ' + message;
        };
        
        // Mock ScenarioModalUtils
        window.ScenarioModalUtils = {
            fetchScenario: function(id) {
                return Promise.resolve({
                    scenario: {
                        id: id,
                        name: 'Test Scenario',
                        description: 'Test Description',
                        agent_role: 'mentor',
                        is_active: true,
                        tags: [],
                        input_data: {},
                        metadata: {}
                    }
                });
            },
            populateForm: function(data) {
                log('populateForm called with data: ' + JSON.stringify(data));
            },
            collectFormData: function() {
                return {
                    name: 'Test Scenario',
                    description: 'Test Description',
                    agent_role: 'mentor',
                    is_active: true,
                    tags: [],
                    input_data: {},
                    metadata: { workflow_type: 'wheel_generation' }
                };
            },
            validateScenarioData: function(data) {
                return []; // No errors
            },
            saveScenario: function(data, isEdit) {
                return Promise.resolve({ success: true });
            }
        };
        
        // Test functions
        function testScenarioModalAvailability() {
            log('=== Testing Scenario Modal Availability ===');
            
            if (window.scenarioEditingModal) {
                log('✓ window.scenarioEditingModal is available', 'success');
                document.getElementById('test-status').textContent = 'Modal Available';
            } else {
                log('✗ window.scenarioEditingModal is NOT available', 'error');
                
                if (window.ScenarioEditingModal) {
                    log('✓ window.ScenarioEditingModal class is available', 'info');
                } else {
                    log('✗ window.ScenarioEditingModal class is NOT available', 'error');
                }
                
                if (window.initializeScenarioEditingModal) {
                    log('✓ window.initializeScenarioEditingModal function is available', 'info');
                } else {
                    log('✗ window.initializeScenarioEditingModal function is NOT available', 'error');
                }
                
                document.getElementById('test-status').textContent = 'Modal NOT Available';
            }
        }
        
        function testEditScenarioFunction() {
            log('=== Testing Edit Scenario Function ===');
            
            if (typeof editScenario === 'function') {
                log('✓ editScenario function is available', 'success');
                
                try {
                    editScenario('test-123');
                    log('✓ editScenario function executed without throwing errors', 'success');
                } catch (error) {
                    log('✗ editScenario function threw an error: ' + error.message, 'error');
                }
            } else {
                log('✗ editScenario function is NOT available', 'error');
            }
        }
        
        function testManualInitialization() {
            log('=== Testing Manual Initialization ===');
            
            if (window.initializeScenarioEditingModal) {
                log('Calling initializeScenarioEditingModal()...', 'info');
                window.initializeScenarioEditingModal();
                
                if (window.scenarioEditingModal) {
                    log('✓ Manual initialization successful', 'success');
                    document.getElementById('test-status').textContent = 'Manual Initialization Successful';
                } else {
                    log('✗ Manual initialization failed', 'error');
                    document.getElementById('test-status').textContent = 'Manual Initialization Failed';
                }
            } else {
                log('✗ initializeScenarioEditingModal function not available', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('test-log').textContent = '';
            document.getElementById('test-status').textContent = 'Log cleared';
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded', 'info');
            testScenarioModalAvailability();
        });
    </script>
    
    <!-- Include the actual scenario modal scripts -->
    <script src="scenario_modal_utils.js"></script>
    <script src="scenario_editing_modal.js"></script>
    <script>
        // Simplified version of editScenario function for testing
        function editScenario(scenarioId) {
            console.log(`editScenario called for ID: ${scenarioId} - attempting to show modal`);

            // Function to show the modal
            function showModal() {
                if (window.scenarioEditingModal) {
                    console.log('Modal found, calling show()');
                    // Mock the show method for testing
                    if (window.scenarioEditingModal.show) {
                        window.scenarioEditingModal.show(scenarioId);
                    } else {
                        console.log('Modal show method called (mocked)');
                    }
                    return true;
                } else {
                    console.error('ScenarioEditingModal not available');
                    showError('Scenario editor not available. Please refresh the page.');
                    return false;
                }
            }

            // Check if modal is already available
            if (window.scenarioEditingModal) {
                return showModal();
            }

            // If not available, try to initialize it
            if (window.ScenarioEditingModal && !window.scenarioEditingModal) {
                console.log('Initializing ScenarioEditingModal...');
                window.scenarioEditingModal = new window.ScenarioEditingModal();
                return showModal();
            }

            // Try manual initialization as last resort
            if (window.initializeScenarioEditingModal) {
                console.log('Attempting manual initialization...');
                window.initializeScenarioEditingModal();
                if (window.scenarioEditingModal) {
                    return showModal();
                }
            }

            console.error('ScenarioEditingModal not available after all attempts');
            showError('Scenario editor not available. Please refresh the page.');
            return false;
        }
    </script>
</body>
</html>
