// ACTIVE_FILE - 29-05-2025
/**
 * User Profile Modal Management
 *
 * This module handles the user profile modal functionality,
 * including form management, template loading, and profile operations.
 */

class UserProfileModal {
    constructor() {
        this.modal = null;
        this.profileData = {};
        this.isEditMode = false;

        this.init();
    }

    init() {
        this.modal = document.getElementById('user-profile-modal');
        if (!this.modal) return;

        this.setupEventListeners();
        this.initializeSliders();
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('user-profile-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSave(e));
        }

        // Close modal events
        this.modal.querySelectorAll('.close, .close-modal').forEach(el => {
            el.addEventListener('click', () => this.close());
        });

        // Click outside to close
        window.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.close();
            }
        });

        // Template loading
        const loadTemplateBtn = document.getElementById('profile-load-template-btn');
        if (loadTemplateBtn) {
            loadTemplateBtn.addEventListener('click', () => this.showTemplateSelector());
        }

        // Preview profile
        const previewBtn = document.getElementById('profile-preview-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.previewProfile());
        }

        // Archetype change handler
        const archetypeSelect = document.getElementById('profile-archetype');
        if (archetypeSelect) {
            archetypeSelect.addEventListener('change', (e) => this.onArchetypeChange(e.target.value));
        }
    }

    initializeSliders() {
        if (window.UserProfileModalUtils) {
            window.UserProfileModalUtils.initializeSliders();
        }
    }

    show(profileId = null) {
        console.log(`UserProfileModal.show() called with profileId: ${profileId}`);
        if (!this.modal) {
            console.error('UserProfileModal.show(): Modal element not found.');
            return;
        }

        this.isEditMode = !!profileId;

        if (profileId) {
            this.loadProfile(profileId);
        } else {
            this.resetForm();
            if (window.UserProfileModalUtils) {
                window.UserProfileModalUtils.setDefaultValues();
            }
        }

        this.modal.style.display = 'block';
    }

    close() {
        if (this.modal) {
            this.modal.style.display = 'none';
        }
    }

    resetForm() {
        const form = document.getElementById('user-profile-form');
        if (form) form.reset();

        document.getElementById('user-profile-modal-title').textContent = 'Create New User Profile';
        document.getElementById('profile-id').value = '';

        this.profileData = {};

        // Reset trust level display
        if (window.UserProfileModalUtils) {
            window.UserProfileModalUtils.updateTrustLevelDisplay(50);
        }

        // Reset HEXACO value displays
        const hexacoTraits = ['honesty-humility', 'emotionality', 'extraversion', 'agreeableness', 'conscientiousness', 'openness'];
        hexacoTraits.forEach(trait => {
            const valueDisplay = document.getElementById(`${trait}-value`);
            if (valueDisplay) {
                valueDisplay.textContent = '0.5';
            }
        });
    }

    onArchetypeChange(archetype) {
        if (archetype && archetype !== 'custom' && window.UserProfileModalUtils) {
            // Auto-load template when archetype is selected
            const template = window.UserProfileModalUtils.profileTemplates[archetype];
            if (template) {
                // Ask user if they want to load the template
                if (confirm(`Load the "${template.name}" template? This will overwrite current form data.`)) {
                    window.UserProfileModalUtils.loadTemplate(archetype);
                }
            }
        }
    }

    showTemplateSelector() {
        if (!window.UserProfileModalUtils) return;

        const templates = window.UserProfileModalUtils.profileTemplates;
        const templateOptions = Object.keys(templates).map(key => {
            const template = templates[key];
            return `<option value="${key}">${template.name}</option>`;
        }).join('');

        const html = `
            <div class="template-selector">
                <h4>Select Profile Template</h4>
                <p>Choose a predefined template to quickly set up a user profile:</p>
                <select id="template-selector" class="form-control">
                    <option value="">Select a template...</option>
                    ${templateOptions}
                </select>
                <div class="template-preview" id="template-preview" style="margin-top: 15px; display: none;">
                    <h5>Template Preview:</h5>
                    <div id="template-preview-content"></div>
                </div>
                <div style="margin-top: 15px;">
                    <button type="button" class="btn btn-primary" onclick="window.userProfileModal.loadSelectedTemplate()">Load Template</button>
                    <button type="button" class="btn btn-secondary" onclick="window.userProfileModal.closeTemplateSelector()">Cancel</button>
                </div>
            </div>
        `;

        // Create overlay
        const overlay = document.createElement('div');
        overlay.id = 'template-selector-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const container = document.createElement('div');
        container.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        `;
        container.innerHTML = html;

        overlay.appendChild(container);
        document.body.appendChild(overlay);

        // Add template preview functionality
        const selector = container.querySelector('#template-selector');
        selector.addEventListener('change', (e) => {
            const templateKey = e.target.value;
            if (templateKey && templates[templateKey]) {
                this.showTemplatePreview(templates[templateKey], container);
            } else {
                container.querySelector('#template-preview').style.display = 'none';
            }
        });
    }

    showTemplatePreview(template, container) {
        const previewContainer = container.querySelector('#template-preview');
        const previewContent = container.querySelector('#template-preview-content');
        
        if (!previewContainer || !previewContent) return;

        const trustPhase = template.trust_level >= 70 ? 'Integration' : 
                          template.trust_level >= 40 ? 'Expansion' : 'Foundation';

        previewContent.innerHTML = `
            <div class="template-info">
                <p><strong>Description:</strong> ${template.description}</p>
                <p><strong>Trust Level:</strong> ${template.trust_level} (${trustPhase} Phase)</p>
                <p><strong>Key Traits:</strong> 
                    Openness: ${template.hexaco.openness}, 
                    Conscientiousness: ${template.hexaco.conscientiousness},
                    Extraversion: ${template.hexaco.extraversion}
                </p>
                <p><strong>Preferences:</strong> ${Object.keys(template.preferences).join(', ')}</p>
            </div>
        `;
        
        previewContainer.style.display = 'block';
    }

    loadSelectedTemplate() {
        const selector = document.getElementById('template-selector');
        if (selector && selector.value && window.UserProfileModalUtils) {
            window.UserProfileModalUtils.loadTemplate(selector.value);
            this.closeTemplateSelector();
        }
    }

    closeTemplateSelector() {
        const overlay = document.getElementById('template-selector-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    previewProfile() {
        try {
            const profileData = window.UserProfileModalUtils.collectFormData();
            const errors = window.UserProfileModalUtils.validateProfileData(profileData);

            if (errors.length > 0) {
                alert('Please fix the following errors before previewing:\n' + errors.join('\n'));
                return;
            }

            // Create preview modal
            this.showProfilePreview(profileData);

        } catch (error) {
            console.error('Error creating profile preview:', error);
            alert(`Error creating preview: ${error.message}`);
        }
    }

    showProfilePreview(profileData) {
        const trustPhase = profileData.trust_level >= 70 ? 'Integration' : 
                          profileData.trust_level >= 40 ? 'Expansion' : 'Foundation';

        const html = `
            <div class="profile-preview">
                <h4>Profile Preview: ${profileData.name}</h4>
                <div class="preview-sections">
                    <div class="preview-section">
                        <h5>Basic Information</h5>
                        <p><strong>Name:</strong> ${profileData.name}</p>
                        <p><strong>Description:</strong> ${profileData.description || 'No description'}</p>
                        <p><strong>Archetype:</strong> ${profileData.archetype}</p>
                        <p><strong>Trust Level:</strong> ${profileData.trust_level} (${trustPhase} Phase)</p>
                        <p><strong>Status:</strong> ${profileData.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                    
                    <div class="preview-section">
                        <h5>Demographics</h5>
                        <p><strong>Age:</strong> ${profileData.demographics.age || 'Not specified'}</p>
                        <p><strong>Gender:</strong> ${profileData.demographics.gender || 'Not specified'}</p>
                        <p><strong>Location:</strong> ${profileData.demographics.location || 'Not specified'}</p>
                        <p><strong>Occupation:</strong> ${profileData.demographics.occupation || 'Not specified'}</p>
                    </div>
                    
                    <div class="preview-section">
                        <h5>HEXACO Personality Traits</h5>
                        <div class="hexaco-preview">
                            <p><strong>Honesty-Humility:</strong> ${profileData.hexaco.honesty_humility}</p>
                            <p><strong>Emotionality:</strong> ${profileData.hexaco.emotionality}</p>
                            <p><strong>Extraversion:</strong> ${profileData.hexaco.extraversion}</p>
                            <p><strong>Agreeableness:</strong> ${profileData.hexaco.agreeableness}</p>
                            <p><strong>Conscientiousness:</strong> ${profileData.hexaco.conscientiousness}</p>
                            <p><strong>Openness:</strong> ${profileData.hexaco.openness}</p>
                        </div>
                    </div>
                    
                    <div class="preview-section">
                        <h5>Preferences</h5>
                        <pre>${JSON.stringify(profileData.preferences, null, 2)}</pre>
                    </div>
                    
                    <div class="preview-section">
                        <h5>Mock Tool Responses</h5>
                        <pre>${JSON.stringify(profileData.mock_responses, null, 2)}</pre>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="window.userProfileModal.closeProfilePreview()">Close Preview</button>
                </div>
            </div>
        `;

        // Create overlay
        const overlay = document.createElement('div');
        overlay.id = 'profile-preview-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const container = document.createElement('div');
        container.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        `;
        container.innerHTML = html;

        overlay.appendChild(container);
        document.body.appendChild(overlay);
    }

    closeProfilePreview() {
        const overlay = document.getElementById('profile-preview-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    async loadProfile(profileId) {
        try {
            // TODO: Implement API call to load profile
            console.log(`Loading profile ${profileId} - API not yet implemented`);
            
            // For now, show edit mode with empty form
            document.getElementById('user-profile-modal-title').textContent = 'Edit User Profile';
            document.getElementById('profile-id').value = profileId;

        } catch (error) {
            console.error('Error loading profile:', error);
            if (window.showError) {
                window.showError(`Error loading profile: ${error.message}`);
            }
        }
    }

    async handleSave(event) {
        event.preventDefault();

        try {
            const profileData = window.UserProfileModalUtils.collectFormData();

            // Validate data
            const validationErrors = window.UserProfileModalUtils.validateProfileData(profileData);
            if (validationErrors.length > 0) {
                if (window.showError) {
                    window.showError(`Validation errors: ${validationErrors.join(', ')}`);
                }
                return;
            }

            // TODO: Implement API call to save profile
            console.log('Saving profile data:', profileData);
            
            // For now, just show success message
            this.close();

            if (window.showSuccess) {
                window.showSuccess(this.isEditMode ? 'Profile updated successfully' : 'Profile created successfully');
            }

            // TODO: Refresh profiles table
            // if (window.loadUserProfiles) {
            //     window.loadUserProfiles();
            // }

        } catch (error) {
            console.error('Error saving profile:', error);
            if (window.showError) {
                window.showError(`Error saving profile: ${error.message}`);
            }
        }
    }
}

// Initialize when DOM is loaded
function initializeUserProfileModal() {
    if (!window.userProfileModal) {
        console.log('Initializing UserProfileModal...');
        window.userProfileModal = new UserProfileModal();
        console.log('UserProfileModal initialized successfully');
    }
}

// Try to initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeUserProfileModal);
} else {
    // DOM is already loaded
    initializeUserProfileModal();
}

// Export for global access
window.UserProfileModal = UserProfileModal;
window.initializeUserProfileModal = initializeUserProfileModal;
