// ACTIVE_FILE - 29-05-2025
/**
 * User Profile Modal Utilities
 *
 * This module provides utility functions for the user profile management modal,
 * including API calls, form data handling, validation, and template generation.
 */

class UserProfileModalUtils {
    constructor() {
        this.apiBaseUrl = '/admin/benchmarks/api/user-profiles/'; // Will be implemented in backend
        this.profileTemplates = this.getProfileTemplates();
    }

    /**
     * Get predefined profile templates
     */
    getProfileTemplates() {
        return {
            'new_user': {
                name: 'New User - Foundation Phase',
                description: 'A new user just starting with the system, low trust level, cautious approach',
                archetype: 'new_user',
                trust_level: 25,
                demographics: {
                    age: 28,
                    gender: '',
                    location: '',
                    occupation: ''
                },
                hexaco: {
                    honesty_humility: 0.6,
                    emotionality: 0.7,
                    extraversion: 0.4,
                    agreeableness: 0.6,
                    conscientiousness: 0.5,
                    openness: 0.5
                },
                preferences: {
                    activity_types: ['safe', 'familiar'],
                    communication_style: 'supportive',
                    interests: ['learning', 'guidance'],
                    risk_tolerance: 'low'
                },
                mock_responses: {
                    get_user_profile: {
                        response: {
                            id: 'new-user-123',
                            name: 'New User',
                            trust_phase: 'Foundation',
                            trust_level: 25
                        }
                    },
                    get_user_activities: {
                        response: {
                            activities: [],
                            completed_count: 0
                        }
                    }
                }
            },
            'experienced_user': {
                name: 'Experienced User - Expansion Phase',
                description: 'A user familiar with the system, moderate trust level, open to new challenges',
                archetype: 'experienced_user',
                trust_level: 65,
                demographics: {
                    age: 35,
                    gender: '',
                    location: '',
                    occupation: ''
                },
                hexaco: {
                    honesty_humility: 0.7,
                    emotionality: 0.5,
                    extraversion: 0.6,
                    agreeableness: 0.7,
                    conscientiousness: 0.7,
                    openness: 0.7
                },
                preferences: {
                    activity_types: ['varied', 'challenging'],
                    communication_style: 'collaborative',
                    interests: ['growth', 'exploration'],
                    risk_tolerance: 'moderate'
                },
                mock_responses: {
                    get_user_profile: {
                        response: {
                            id: 'exp-user-456',
                            name: 'Experienced User',
                            trust_phase: 'Expansion',
                            trust_level: 65
                        }
                    },
                    get_user_activities: {
                        response: {
                            activities: [
                                { id: 'act1', name: 'Previous Activity 1', completed: true },
                                { id: 'act2', name: 'Previous Activity 2', completed: true }
                            ],
                            completed_count: 15
                        }
                    }
                }
            },
            'confident_user': {
                name: 'Confident User - Integration Phase',
                description: 'A confident user with high trust, ready for collaboration and complex challenges',
                archetype: 'confident_user',
                trust_level: 85,
                demographics: {
                    age: 42,
                    gender: '',
                    location: '',
                    occupation: ''
                },
                hexaco: {
                    honesty_humility: 0.8,
                    emotionality: 0.4,
                    extraversion: 0.8,
                    agreeableness: 0.8,
                    conscientiousness: 0.8,
                    openness: 0.9
                },
                preferences: {
                    activity_types: ['ambitious', 'creative', 'leadership'],
                    communication_style: 'direct',
                    interests: ['innovation', 'mentoring'],
                    risk_tolerance: 'high'
                },
                mock_responses: {
                    get_user_profile: {
                        response: {
                            id: 'conf-user-789',
                            name: 'Confident User',
                            trust_phase: 'Integration',
                            trust_level: 85
                        }
                    },
                    get_user_activities: {
                        response: {
                            activities: [
                                { id: 'act1', name: 'Leadership Project', completed: true },
                                { id: 'act2', name: 'Creative Challenge', completed: true },
                                { id: 'act3', name: 'Mentoring Session', completed: true }
                            ],
                            completed_count: 30
                        }
                    }
                }
            },
            'stressed_user': {
                name: 'Stressed User - Under Pressure',
                description: 'A user under stress, needs quick solutions and clear guidance',
                archetype: 'stressed_user',
                trust_level: 40,
                demographics: {
                    age: 32,
                    gender: '',
                    location: '',
                    occupation: ''
                },
                hexaco: {
                    honesty_humility: 0.6,
                    emotionality: 0.8,
                    extraversion: 0.3,
                    agreeableness: 0.5,
                    conscientiousness: 0.6,
                    openness: 0.4
                },
                preferences: {
                    activity_types: ['quick', 'stress-relief'],
                    communication_style: 'concise',
                    interests: ['efficiency', 'relaxation'],
                    risk_tolerance: 'very_low'
                },
                mock_responses: {
                    get_user_profile: {
                        response: {
                            id: 'stress-user-321',
                            name: 'Stressed User',
                            trust_phase: 'Expansion',
                            trust_level: 40,
                            current_stress: 'high'
                        }
                    },
                    get_user_activities: {
                        response: {
                            activities: [
                                { id: 'act1', name: 'Quick Meditation', completed: false },
                                { id: 'act2', name: 'Breathing Exercise', completed: true }
                            ],
                            completed_count: 8,
                            abandonment_rate: 0.3
                        }
                    }
                }
            }
        };
    }

    /**
     * Load a profile template into the form
     */
    loadTemplate(templateName) {
        const template = this.profileTemplates[templateName];
        if (!template) {
            console.error(`Template ${templateName} not found`);
            return;
        }

        // Populate basic fields
        document.getElementById('profile-name').value = template.name;
        document.getElementById('profile-description').value = template.description;
        document.getElementById('profile-archetype').value = template.archetype;
        document.getElementById('profile-trust-level').value = template.trust_level;

        // Update trust level display
        this.updateTrustLevelDisplay(template.trust_level);

        // Populate demographics
        if (template.demographics) {
            document.getElementById('profile-age').value = template.demographics.age || 30;
            document.getElementById('profile-gender').value = template.demographics.gender || '';
            document.getElementById('profile-location').value = template.demographics.location || '';
            document.getElementById('profile-occupation').value = template.demographics.occupation || '';
        }

        // Populate HEXACO traits
        if (template.hexaco) {
            Object.keys(template.hexaco).forEach(trait => {
                const slider = document.getElementById(`profile-${trait.replace('_', '-')}`);
                const valueDisplay = document.getElementById(`${trait.replace('_', '-')}-value`);
                if (slider && valueDisplay) {
                    slider.value = template.hexaco[trait];
                    valueDisplay.textContent = template.hexaco[trait];
                }
            });
        }

        // Populate preferences
        if (template.preferences) {
            document.getElementById('profile-preferences').value = JSON.stringify(template.preferences, null, 2);
        }

        // Populate mock responses
        if (template.mock_responses) {
            document.getElementById('profile-mock-responses').value = JSON.stringify(template.mock_responses, null, 2);
        }

        // Show success message
        if (window.showSuccess) {
            window.showSuccess(`Template "${template.name}" loaded successfully`);
        }
    }

    /**
     * Update trust level display and phase
     */
    updateTrustLevelDisplay(trustLevel) {
        const valueDisplay = document.getElementById('profile-trust-level-value');
        const phaseDisplay = document.getElementById('profile-trust-phase');
        
        if (valueDisplay) valueDisplay.textContent = trustLevel;
        
        if (phaseDisplay) {
            let phase = 'Foundation';
            if (trustLevel >= 70) phase = 'Integration';
            else if (trustLevel >= 40) phase = 'Expansion';
            
            phaseDisplay.textContent = phase;
            phaseDisplay.className = `trust-phase trust-${phase.toLowerCase()}`;
        }
    }

    /**
     * Initialize slider event listeners
     */
    initializeSliders() {
        // Trust level slider
        const trustSlider = document.getElementById('profile-trust-level');
        if (trustSlider) {
            trustSlider.addEventListener('input', (e) => {
                this.updateTrustLevelDisplay(parseInt(e.target.value));
            });
        }

        // HEXACO trait sliders
        const hexacoTraits = ['honesty-humility', 'emotionality', 'extraversion', 'agreeableness', 'conscientiousness', 'openness'];
        hexacoTraits.forEach(trait => {
            const slider = document.getElementById(`profile-${trait}`);
            const valueDisplay = document.getElementById(`${trait}-value`);
            
            if (slider && valueDisplay) {
                slider.addEventListener('input', function() {
                    valueDisplay.textContent = this.value;
                });
            }
        });
    }

    /**
     * Validate profile data
     */
    validateProfileData(profileData) {
        const errors = [];

        // Required fields
        if (!profileData.name || profileData.name.trim() === '') {
            errors.push('Profile name is required');
        }
        if (!profileData.archetype) {
            errors.push('Profile archetype is required');
        }
        if (profileData.trust_level < 0 || profileData.trust_level > 100) {
            errors.push('Trust level must be between 0 and 100');
        }

        // Validate JSON fields
        try {
            if (profileData.preferences && typeof profileData.preferences === 'string') {
                JSON.parse(profileData.preferences);
            }
        } catch (e) {
            errors.push('Invalid JSON in preferences field');
        }

        try {
            if (profileData.mock_responses && typeof profileData.mock_responses === 'string') {
                JSON.parse(profileData.mock_responses);
            }
        } catch (e) {
            errors.push('Invalid JSON in mock responses field');
        }

        return errors;
    }

    /**
     * Collect form data into profile object
     */
    collectFormData() {
        const profileData = {
            name: document.getElementById('profile-name').value.trim(),
            description: document.getElementById('profile-description').value.trim(),
            archetype: document.getElementById('profile-archetype').value,
            trust_level: parseInt(document.getElementById('profile-trust-level').value),
            is_active: document.getElementById('profile-is-active').value === 'true'
        };

        // Add ID if editing
        const profileId = document.getElementById('profile-id').value;
        if (profileId) {
            profileData.id = parseInt(profileId);
        }

        // Collect demographics
        profileData.demographics = {
            age: parseInt(document.getElementById('profile-age').value) || null,
            gender: document.getElementById('profile-gender').value || null,
            location: document.getElementById('profile-location').value.trim() || null,
            occupation: document.getElementById('profile-occupation').value.trim() || null
        };

        // Collect HEXACO traits
        profileData.hexaco = {
            honesty_humility: parseFloat(document.getElementById('profile-honesty-humility').value),
            emotionality: parseFloat(document.getElementById('profile-emotionality').value),
            extraversion: parseFloat(document.getElementById('profile-extraversion').value),
            agreeableness: parseFloat(document.getElementById('profile-agreeableness').value),
            conscientiousness: parseFloat(document.getElementById('profile-conscientiousness').value),
            openness: parseFloat(document.getElementById('profile-openness').value)
        };

        // Process JSON fields
        try {
            const preferencesValue = document.getElementById('profile-preferences').value.trim();
            profileData.preferences = preferencesValue ? JSON.parse(preferencesValue) : {};
        } catch (e) {
            throw new Error('Invalid JSON in preferences field');
        }

        try {
            const mockResponsesValue = document.getElementById('profile-mock-responses').value.trim();
            profileData.mock_responses = mockResponsesValue ? JSON.parse(mockResponsesValue) : {};
        } catch (e) {
            throw new Error('Invalid JSON in mock responses field');
        }

        return profileData;
    }

    /**
     * Set default values for new profile
     */
    setDefaultValues() {
        // Set default preferences
        const defaultPreferences = {
            activity_types: ['outdoor', 'creative'],
            communication_style: 'supportive',
            interests: ['learning', 'growth'],
            risk_tolerance: 'moderate'
        };
        document.getElementById('profile-preferences').value = JSON.stringify(defaultPreferences, null, 2);

        // Set default mock responses
        const defaultMockResponses = {
            get_user_profile: {
                response: {
                    id: 'user-123',
                    name: 'Test User',
                    trust_phase: 'Expansion',
                    trust_level: 50
                }
            },
            get_user_activities: {
                response: {
                    activities: [],
                    completed_count: 5
                }
            }
        };
        document.getElementById('profile-mock-responses').value = JSON.stringify(defaultMockResponses, null, 2);

        // Initialize trust level display
        this.updateTrustLevelDisplay(50);
    }

    /**
     * Get CSRF token from cookies
     */
    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.UserProfileModalUtils = new UserProfileModalUtils();
});

// Export for global access
window.UserProfileModalUtils = UserProfileModalUtils;
