// ACTIVE_FILE - 29-05-2025
/**
 * Enhanced Variable Ranges Builder
 *
 * This module provides an intuitive interface for configuring variable ranges
 * with visual feedback, validation, and preset configurations.
 */

class VariableRangesBuilder {
    constructor() {
        this.container = null;
        this.data = {};
        this.presets = this.getPresets();

        this.init();
    }

    init() {
        this.createContainer();
        this.setupEventListeners();
    }

    createContainer() {
        const modal = document.getElementById('template-modal');
        if (!modal) return;

        // Remove existing container
        const existing = modal.querySelector('#variable-ranges-builder');
        if (existing) existing.remove();

        this.container = document.createElement('div');
        this.container.id = 'variable-ranges-builder';
        this.container.className = 'template-tab-content';
        this.container.style.display = 'none'; // Initially hidden
        this.container.innerHTML = this.getHTML();

        // Insert into modal form
        const form = modal.querySelector('#template-form');
        if (form) {
            form.appendChild(this.container);
        }

        console.log('Variable ranges builder container created and added to form');
    }

    getHTML() {
        return `
            <div class="variable-ranges-builder enhanced">
                <div class="builder-header">
                    <h4><i class="fas fa-sliders-h"></i> Variable Ranges Configuration</h4>
                    <p class="help-text">Define the supported ranges and descriptions for contextual variables with interactive validation and testing.</p>

                    <div class="builder-toolbar">
                        <div class="preset-selector">
                            <label for="ranges-preset">Configuration Presets:</label>
                            <select id="ranges-preset" class="form-control">
                                <option value="">Choose a preset...</option>
                                <option value="standard">📊 Standard Ranges</option>
                                <option value="extended">📈 Extended Ranges</option>
                                <option value="minimal">📋 Minimal Setup</option>
                                <option value="research">🔬 Research Configuration</option>
                                <option value="clinical">🏥 Clinical Settings</option>
                            </select>
                            <button type="button" class="btn btn-sm btn-secondary" id="load-ranges-preset-btn">
                                <i class="fas fa-download"></i> Load
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" id="preview-ranges-preset-btn" title="Preview selected preset">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <div class="builder-actions">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="test-ranges-btn" title="Test ranges with sample values">
                                <i class="fas fa-vial"></i> Test
                            </button>
                            <button type="button" class="btn btn-sm btn-info" id="validate-ranges-btn">
                                <i class="fas fa-check-circle"></i> Validate
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="sync-ranges-json-btn">
                                <i class="fas fa-sync"></i> Sync to JSON
                            </button>
                        </div>
                    </div>

                    <div class="preset-preview" id="ranges-preset-preview" style="display: none;">
                        <div class="preset-preview-content">
                            <h6>Preset Preview</h6>
                            <div id="ranges-preset-preview-details"></div>
                            <button type="button" class="btn btn-xs btn-outline-secondary" id="close-ranges-preview-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="validation-status" id="ranges-validation-status"></div>

                <div class="range-testing" id="range-testing" style="display: none;">
                    <div class="testing-header">
                        <h6><i class="fas fa-vial"></i> Range Testing</h6>
                        <button type="button" class="btn btn-xs btn-outline-secondary" id="close-testing-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="testing-content" id="testing-content"></div>
                </div>

                <div class="variable-range-sections">
                    ${this.getTrustLevelRangeSection()}
                    ${this.getMoodRangeSection()}
                    ${this.getEnvironmentRangeSection()}
                </div>

                <div class="ranges-summary enhanced">
                    <h6><i class="fas fa-chart-bar"></i> Configuration Summary</h6>
                    <div class="summary-stats" id="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">Variables Configured:</span>
                            <span class="stat-value" id="variables-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Range Coverage:</span>
                            <span class="stat-value" id="coverage-percentage">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Validation Status:</span>
                            <span class="stat-value" id="validation-status">Not validated</span>
                        </div>
                    </div>
                    <div class="summary-grid" id="ranges-summary-grid">
                        <!-- Summary will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;
    }

    getTrustLevelRangeSection() {
        return `
            <div class="range-section enhanced" data-variable="trust_level">
                <div class="section-header">
                    <h5><i class="fas fa-shield-alt"></i> Trust Level</h5>
                    <div class="section-controls">
                        <button type="button" class="btn btn-xs btn-outline-primary" id="trust-ranges-help-btn" title="Show trust level range guidance">
                            <i class="fas fa-question-circle"></i> Help
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-success" id="trust-ranges-auto-btn" title="Auto-configure optimal ranges">
                            <i class="fas fa-magic"></i> Auto-Configure
                        </button>
                    </div>
                </div>

                <div class="trust-ranges-guidance" id="trust-ranges-guidance" style="display: none;">
                    <div class="guidance-content">
                        <h6>Trust Level Configuration Guidelines</h6>
                        <ul>
                            <li><strong>Range:</strong> Typically 0-100 for percentage-based trust</li>
                            <li><strong>Granularity:</strong> Consider how precisely trust can be measured</li>
                            <li><strong>Context:</strong> Trust levels should reflect user-system relationship maturity</li>
                        </ul>
                        <p><small>Higher granularity allows for more nuanced contextual adaptations.</small></p>
                    </div>
                </div>

                <div class="range-visual-bar enhanced">
                    <div class="visual-range interactive" style="background: linear-gradient(to right, #ff6b6b, #ffd93d, #6bcf7f);">
                        <span class="range-label left">Low Trust</span>
                        <span class="range-label center">Moderate</span>
                        <span class="range-label right">High Trust</span>
                    </div>
                    <div class="range-tester">
                        <label>Test Value:</label>
                        <input type="number" class="form-control form-control-sm test-value"
                               id="trust-test-value" min="0" max="100" value="50" style="width: 80px;">
                        <button type="button" class="btn btn-xs btn-outline-info" id="test-trust-value">Test</button>
                        <span class="test-result" id="trust-test-result"></span>
                    </div>
                </div>

                <div class="range-inputs enhanced">
                    <div class="input-group">
                        <label>Minimum Value:</label>
                        <input type="number" class="form-control range-min" value="0" min="0" max="100" data-variable="trust_level">
                        <span class="input-help">Lowest possible trust level</span>
                        <div class="input-validation" id="trust-min-validation"></div>
                    </div>
                    <div class="input-group">
                        <label>Maximum Value:</label>
                        <input type="number" class="form-control range-max" value="100" min="0" max="100" data-variable="trust_level">
                        <span class="input-help">Highest possible trust level</span>
                        <div class="input-validation" id="trust-max-validation"></div>
                    </div>
                    <div class="input-group full-width">
                        <label>Description:</label>
                        <input type="text" class="form-control range-desc" value="User's trust level in the system (0-100)" data-variable="trust_level">
                        <span class="input-help">Explain what this variable represents</span>
                        <div class="char-counter"><span id="trust-desc-count">0</span>/200 characters</div>
                    </div>
                    <div class="range-suggestions" id="trust-suggestions" style="display: none;">
                        <h7>Suggested Configurations:</h7>
                        <div class="suggestion-buttons">
                            <button type="button" class="btn btn-xs btn-outline-secondary suggestion-btn" data-config="percentage">0-100 (Percentage)</button>
                            <button type="button" class="btn btn-xs btn-outline-secondary suggestion-btn" data-config="scale">1-10 (Scale)</button>
                            <button type="button" class="btn btn-xs btn-outline-secondary suggestion-btn" data-config="normalized">0-1 (Normalized)</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getMoodRangeSection() {
        return `
            <div class="range-section enhanced" data-variable="mood">
                <div class="section-header">
                    <h5><i class="fas fa-heart"></i> Mood Variables</h5>
                    <div class="section-controls">
                        <button type="button" class="btn btn-xs btn-outline-primary" id="mood-ranges-help-btn" title="Show mood range guidance">
                            <i class="fas fa-question-circle"></i> Help
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-success" id="mood-ranges-auto-btn" title="Auto-configure mood ranges">
                            <i class="fas fa-magic"></i> Auto-Configure
                        </button>
                    </div>
                </div>

                <div class="mood-ranges-guidance" id="mood-ranges-guidance" style="display: none;">
                    <div class="guidance-content">
                        <h6>Mood Variables Configuration Guidelines</h6>
                        <div class="guidance-grid">
                            <div class="guidance-item">
                                <h7>Valence (Emotional Positivity)</h7>
                                <p>Measures how positive or negative an emotion is. Typically ranges from -1.0 (most negative) to 1.0 (most positive).</p>
                            </div>
                            <div class="guidance-item">
                                <h7>Arousal (Emotional Intensity)</h7>
                                <p>Measures emotional activation level. Ranges from -1.0 (calm/sleepy) to 1.0 (excited/alert).</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mood-space-visual enhanced">
                    <div class="mood-quadrants interactive">
                        <div class="quadrant q1" title="High Arousal, Positive Valence" data-valence="positive" data-arousal="high">
                            <span class="quadrant-label">Excited</span>
                            <span class="quadrant-coords">(+1, +1)</span>
                        </div>
                        <div class="quadrant q2" title="Low Arousal, Positive Valence" data-valence="positive" data-arousal="low">
                            <span class="quadrant-label">Content</span>
                            <span class="quadrant-coords">(+1, -1)</span>
                        </div>
                        <div class="quadrant q3" title="Low Arousal, Negative Valence" data-valence="negative" data-arousal="low">
                            <span class="quadrant-label">Sad</span>
                            <span class="quadrant-coords">(-1, -1)</span>
                        </div>
                        <div class="quadrant q4" title="High Arousal, Negative Valence" data-valence="negative" data-arousal="high">
                            <span class="quadrant-label">Angry</span>
                            <span class="quadrant-coords">(-1, +1)</span>
                        </div>
                    </div>
                    <div class="axes">
                        <span class="axis-label valence">Valence (Negative ← → Positive)</span>
                        <span class="axis-label arousal">Arousal (Low ↑ High)</span>
                    </div>
                    <div class="mood-tester">
                        <div class="tester-inputs">
                            <label>Test Valence:</label>
                            <input type="number" class="form-control form-control-sm" id="valence-test-value"
                                   min="-1" max="1" step="0.1" value="0" style="width: 80px;">
                            <label>Test Arousal:</label>
                            <input type="number" class="form-control form-control-sm" id="arousal-test-value"
                                   min="-1" max="1" step="0.1" value="0" style="width: 80px;">
                            <button type="button" class="btn btn-xs btn-outline-info" id="test-mood-values">Test</button>
                        </div>
                        <div class="test-result" id="mood-test-result"></div>
                    </div>
                </div>

                <div class="mood-ranges">
                    <div class="sub-range enhanced" data-sub="valence">
                        <h6><i class="fas fa-smile"></i> Valence (Emotional Positivity)</h6>
                        <div class="range-inputs enhanced">
                            <div class="input-group">
                                <label>Minimum:</label>
                                <input type="number" class="form-control range-min" value="-1.0" min="-1" max="1" step="0.1" data-variable="mood" data-sub="valence">
                                <span class="input-help">Most negative emotion</span>
                                <div class="input-validation" id="valence-min-validation"></div>
                            </div>
                            <div class="input-group">
                                <label>Maximum:</label>
                                <input type="number" class="form-control range-max" value="1.0" min="-1" max="1" step="0.1" data-variable="mood" data-sub="valence">
                                <span class="input-help">Most positive emotion</span>
                                <div class="input-validation" id="valence-max-validation"></div>
                            </div>
                            <div class="input-group full-width">
                                <label>Description:</label>
                                <input type="text" class="form-control range-desc" value="Emotional valence from negative to positive (-1.0 to 1.0)" data-variable="mood" data-sub="valence">
                                <div class="char-counter"><span id="valence-desc-count">0</span>/200 characters</div>
                            </div>
                        </div>
                    </div>

                    <div class="sub-range enhanced" data-sub="arousal">
                        <h6><i class="fas fa-bolt"></i> Arousal (Emotional Intensity)</h6>
                        <div class="range-inputs enhanced">
                            <div class="input-group">
                                <label>Minimum:</label>
                                <input type="number" class="form-control range-min" value="-1.0" min="-1" max="1" step="0.1" data-variable="mood" data-sub="arousal">
                                <span class="input-help">Most calm state</span>
                                <div class="input-validation" id="arousal-min-validation"></div>
                            </div>
                            <div class="input-group">
                                <label>Maximum:</label>
                                <input type="number" class="form-control range-max" value="1.0" min="-1" max="1" step="0.1" data-variable="mood" data-sub="arousal">
                                <span class="input-help">Most excited state</span>
                                <div class="input-validation" id="arousal-max-validation"></div>
                            </div>
                            <div class="input-group full-width">
                                <label>Description:</label>
                                <input type="text" class="form-control range-desc" value="Emotional arousal from calm to excited (-1.0 to 1.0)" data-variable="mood" data-sub="arousal">
                                <div class="char-counter"><span id="arousal-desc-count">0</span>/200 characters</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getEnvironmentRangeSection() {
        return `
            <div class="range-section enhanced" data-variable="environment">
                <div class="section-header">
                    <h5><i class="fas fa-thermometer-half"></i> Environment Variables</h5>
                    <div class="env-indicators">
                        <div class="indicator stress">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Stress</span>
                        </div>
                        <div class="indicator time">
                            <i class="fas fa-clock"></i>
                            <span>Time Pressure</span>
                        </div>
                    </div>
                </div>

                <div class="env-ranges">
                    <div class="sub-range enhanced" data-sub="stress_level">
                        <h6>Stress Level</h6>
                        <div class="range-inputs enhanced">
                            <div class="input-group">
                                <label>Minimum:</label>
                                <input type="number" class="form-control range-min" value="0" min="0" max="100" data-variable="environment" data-sub="stress_level">
                                <span class="input-help">No stress</span>
                            </div>
                            <div class="input-group">
                                <label>Maximum:</label>
                                <input type="number" class="form-control range-max" value="100" min="0" max="100" data-variable="environment" data-sub="stress_level">
                                <span class="input-help">Maximum stress</span>
                            </div>
                            <div class="input-group full-width">
                                <label>Description:</label>
                                <input type="text" class="form-control range-desc" value="Environmental stress level (0-100)" data-variable="environment" data-sub="stress_level">
                            </div>
                        </div>
                    </div>

                    <div class="sub-range enhanced" data-sub="time_pressure">
                        <h6>Time Pressure</h6>
                        <div class="range-inputs enhanced">
                            <div class="input-group">
                                <label>Minimum:</label>
                                <input type="number" class="form-control range-min" value="0" min="0" max="100" data-variable="environment" data-sub="time_pressure">
                                <span class="input-help">No time pressure</span>
                            </div>
                            <div class="input-group">
                                <label>Maximum:</label>
                                <input type="number" class="form-control range-max" value="100" min="0" max="100" data-variable="environment" data-sub="time_pressure">
                                <span class="input-help">Urgent deadline</span>
                            </div>
                            <div class="input-group full-width">
                                <label>Description:</label>
                                <input type="text" class="form-control range-desc" value="Time pressure in the environment (0-100)" data-variable="environment" data-sub="time_pressure">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        if (!this.container) return;

        // Preset loading and preview
        const loadPresetBtn = this.container.querySelector('#load-ranges-preset-btn');
        if (loadPresetBtn) {
            loadPresetBtn.addEventListener('click', () => this.loadPreset());
        }

        const previewPresetBtn = this.container.querySelector('#preview-ranges-preset-btn');
        if (previewPresetBtn) {
            previewPresetBtn.addEventListener('click', () => this.previewPreset());
        }

        const closePreviewBtn = this.container.querySelector('#close-ranges-preview-btn');
        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', () => this.closePreview());
        }

        // Testing
        const testBtn = this.container.querySelector('#test-ranges-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => this.showRangeTesting());
        }

        const closeTestingBtn = this.container.querySelector('#close-testing-btn');
        if (closeTestingBtn) {
            closeTestingBtn.addEventListener('click', () => this.closeRangeTesting());
        }

        // Validation
        const validateBtn = this.container.querySelector('#validate-ranges-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validate());
        }

        // JSON sync
        const syncBtn = this.container.querySelector('#sync-ranges-json-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.syncToJSON());
        }

        // Help buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('#trust-ranges-help-btn')) {
                this.toggleGuidance('trust-ranges-guidance');
            } else if (e.target.closest('#mood-ranges-help-btn')) {
                this.toggleGuidance('mood-ranges-guidance');
            } else if (e.target.closest('#env-ranges-help-btn')) {
                this.toggleGuidance('env-ranges-guidance');
            }
        });

        // Auto-configure buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('#trust-ranges-auto-btn')) {
                this.autoConfigureTrust();
            } else if (e.target.closest('#mood-ranges-auto-btn')) {
                this.autoConfigureMood();
            } else if (e.target.closest('#env-ranges-auto-btn')) {
                this.autoConfigureEnvironment();
            }
        });

        // Test value buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('#test-trust-value')) {
                this.testTrustValue();
            } else if (e.target.closest('#test-mood-values')) {
                this.testMoodValues();
            } else if (e.target.closest('#test-env-values')) {
                this.testEnvironmentValues();
            }
        });

        // Input changes with enhanced feedback
        this.container.addEventListener('input', (e) => {
            if (e.target.classList.contains('range-min') ||
                e.target.classList.contains('range-max') ||
                e.target.classList.contains('range-desc')) {
                this.handleInputChange(e.target);
                this.updateCharCounter(e.target);
            }
        });

        // Interactive elements
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.quadrant')) {
                this.highlightMoodQuadrant(e.target.closest('.quadrant'));
            } else if (e.target.closest('.suggestion-btn')) {
                this.applySuggestion(e.target.closest('.suggestion-btn'));
            }
        });
    }

    handleInputChange(input) {
        this.updateSummary();
        this.validateRealTime(input);
    }

    validateRealTime(input) {
        const variable = input.dataset.variable;
        const sub = input.dataset.sub;

        if (input.classList.contains('range-min') || input.classList.contains('range-max')) {
            const container = input.closest('.range-inputs, .sub-range');
            const minInput = container.querySelector('.range-min');
            const maxInput = container.querySelector('.range-max');

            if (minInput && maxInput) {
                const min = parseFloat(minInput.value);
                const max = parseFloat(maxInput.value);

                // Remove previous validation classes
                minInput.classList.remove('is-invalid', 'is-valid');
                maxInput.classList.remove('is-invalid', 'is-valid');

                if (min >= max) {
                    minInput.classList.add('is-invalid');
                    maxInput.classList.add('is-invalid');
                } else {
                    minInput.classList.add('is-valid');
                    maxInput.classList.add('is-valid');
                }
            }
        }
    }

    updateSummary() {
        const summaryGrid = this.container.querySelector('#ranges-summary-grid');
        if (!summaryGrid) return;

        const data = this.collectData();
        let html = '';

        Object.entries(data).forEach(([variable, config]) => {
            if (variable === 'mood' || variable === 'environment') {
                Object.entries(config).forEach(([subVar, subConfig]) => {
                    html += this.getSummaryItem(`${variable}.${subVar}`, subConfig);
                });
            } else {
                html += this.getSummaryItem(variable, config);
            }
        });

        summaryGrid.innerHTML = html || '<p class="text-muted">No ranges configured</p>';
    }

    getSummaryItem(name, config) {
        const range = `${config.min} to ${config.max}`;
        const description = config.description || 'No description';

        return `
            <div class="summary-item">
                <div class="summary-name">${name}</div>
                <div class="summary-range">${range}</div>
                <div class="summary-desc">${description}</div>
            </div>
        `;
    }

    getPresets() {
        return {
            standard: {
                name: 'Standard Ranges',
                description: 'Balanced configuration suitable for most applications',
                data: {
                    trust_level: {
                        min: 0,
                        max: 100,
                        description: "User's trust level in the system (0-100)"
                    },
                    mood: {
                        valence: {
                            min: -1.0,
                            max: 1.0,
                            description: "Emotional valence from negative to positive"
                        },
                        arousal: {
                            min: -1.0,
                            max: 1.0,
                            description: "Emotional arousal from calm to excited"
                        }
                    },
                    environment: {
                        stress_level: {
                            min: 0,
                            max: 100,
                            description: "Environmental stress level"
                        },
                        time_pressure: {
                            min: 0,
                            max: 100,
                            description: "Time pressure in the environment"
                        }
                    }
                }
            },
            extended: {
                name: 'Extended Ranges',
                description: 'Comprehensive configuration with detailed descriptions for research applications',
                data: {
                    trust_level: {
                        min: 0,
                        max: 100,
                        description: "Comprehensive trust measurement including system reliability, user confidence, and historical interactions"
                    },
                    mood: {
                        valence: {
                            min: -1.0,
                            max: 1.0,
                            description: "Emotional valence: negative emotions (-1.0) to positive emotions (1.0)"
                        },
                        arousal: {
                            min: -1.0,
                            max: 1.0,
                            description: "Emotional arousal: calm/sleepy (-1.0) to excited/alert (1.0)"
                        }
                    },
                    environment: {
                        stress_level: {
                            min: 0,
                            max: 100,
                            description: "Environmental stress including workload, deadlines, and external pressures"
                        },
                        time_pressure: {
                            min: 0,
                            max: 100,
                            description: "Urgency level from relaxed planning to immediate action required"
                        }
                    }
                }
            },
            minimal: {
                name: 'Minimal Setup',
                description: 'Basic configuration for simple applications',
                data: {
                    trust_level: {
                        min: 0,
                        max: 100,
                        description: "Basic trust level"
                    },
                    mood: {
                        valence: {
                            min: -1.0,
                            max: 1.0,
                            description: "Mood positivity"
                        }
                    }
                }
            },
            research: {
                name: 'Research Configuration',
                description: 'High-precision ranges for academic and research applications',
                data: {
                    trust_level: {
                        min: 0,
                        max: 1,
                        description: "Normalized trust level for statistical analysis (0.0-1.0)"
                    },
                    mood: {
                        valence: {
                            min: -1.0,
                            max: 1.0,
                            description: "Valence dimension of circumplex model of affect"
                        },
                        arousal: {
                            min: -1.0,
                            max: 1.0,
                            description: "Arousal dimension of circumplex model of affect"
                        }
                    },
                    environment: {
                        stress_level: {
                            min: 0,
                            max: 1,
                            description: "Normalized stress level for research analysis"
                        },
                        time_pressure: {
                            min: 0,
                            max: 1,
                            description: "Normalized time pressure measurement"
                        }
                    }
                }
            },
            clinical: {
                name: 'Clinical Settings',
                description: 'Configuration optimized for healthcare and therapeutic applications',
                data: {
                    trust_level: {
                        min: 1,
                        max: 10,
                        description: "Clinical trust scale (1-10) for therapeutic relationship assessment"
                    },
                    mood: {
                        valence: {
                            min: -3.0,
                            max: 3.0,
                            description: "Extended valence range for clinical mood assessment"
                        },
                        arousal: {
                            min: -3.0,
                            max: 3.0,
                            description: "Extended arousal range for clinical activation assessment"
                        }
                    },
                    environment: {
                        stress_level: {
                            min: 0,
                            max: 10,
                            description: "Clinical stress assessment scale (0-10)"
                        }
                    }
                }
            }
        };
    }

    loadPreset() {
        const select = this.container.querySelector('#ranges-preset');
        const presetKey = select.value;

        if (!presetKey || !this.presets[presetKey]) return;

        this.loadData(this.presets[presetKey].data);
        this.syncToJSON();

        // Show success message
        const status = this.container.querySelector('#ranges-validation-status');
        if (status) {
            status.innerHTML = `<div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                "${this.presets[presetKey].name}" configuration loaded successfully!
            </div>`;
            setTimeout(() => status.innerHTML = '', 3000);
        }
    }

    previewPreset() {
        const select = this.container.querySelector('#ranges-preset');
        const presetKey = select.value;

        if (!presetKey || !this.presets[presetKey]) return;

        const preset = this.presets[presetKey];
        const previewContainer = this.container.querySelector('#ranges-preset-preview');
        const previewDetails = this.container.querySelector('#ranges-preset-preview-details');

        if (previewContainer && previewDetails) {
            const variableCount = Object.keys(preset.data).length;
            let totalRanges = 0;
            Object.values(preset.data).forEach(variable => {
                if (typeof variable === 'object' && variable.min !== undefined) {
                    totalRanges++;
                } else {
                    totalRanges += Object.keys(variable).length;
                }
            });

            previewDetails.innerHTML = `
                <h6>${preset.name}</h6>
                <p>${preset.description}</p>
                <div class="preset-stats">
                    <small class="text-muted">
                        Variables: ${variableCount} |
                        Total ranges: ${totalRanges}
                    </small>
                </div>
            `;
            previewContainer.style.display = 'block';
        }
    }

    closePreview() {
        const previewContainer = this.container.querySelector('#ranges-preset-preview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    showRangeTesting() {
        const testingContainer = this.container.querySelector('#range-testing');
        const testingContent = this.container.querySelector('#testing-content');

        if (testingContainer && testingContent) {
            testingContent.innerHTML = `
                <div class="testing-grid">
                    <div class="test-section">
                        <h7>Trust Level Testing</h7>
                        <div class="test-inputs">
                            <input type="number" class="form-control form-control-sm" id="test-trust"
                                   placeholder="Enter trust value" min="0" max="100">
                            <button type="button" class="btn btn-xs btn-primary" onclick="this.closest('.variable-ranges-builder').testTrustValue()">Test</button>
                        </div>
                        <div class="test-result" id="trust-test-result"></div>
                    </div>

                    <div class="test-section">
                        <h7>Mood Testing</h7>
                        <div class="test-inputs">
                            <input type="number" class="form-control form-control-sm" id="test-valence"
                                   placeholder="Valence" min="-1" max="1" step="0.1">
                            <input type="number" class="form-control form-control-sm" id="test-arousal"
                                   placeholder="Arousal" min="-1" max="1" step="0.1">
                            <button type="button" class="btn btn-xs btn-primary" onclick="this.closest('.variable-ranges-builder').testMoodValues()">Test</button>
                        </div>
                        <div class="test-result" id="mood-test-result"></div>
                    </div>
                </div>
            `;
            testingContainer.style.display = 'block';
        }
    }

    closeRangeTesting() {
        const testingContainer = this.container.querySelector('#range-testing');
        if (testingContainer) {
            testingContainer.style.display = 'none';
        }
    }

    toggleGuidance(guidanceId) {
        const guidance = this.container.querySelector(`#${guidanceId}`);
        if (guidance) {
            guidance.style.display = guidance.style.display === 'none' ? 'block' : 'none';
        }
    }

    updateCharCounter(input) {
        if (!input.classList.contains('range-desc')) return;

        const variable = input.dataset.variable;
        const sub = input.dataset.sub;
        let counterId = '';

        if (variable === 'trust_level') {
            counterId = 'trust-desc-count';
        } else if (variable === 'mood' && sub === 'valence') {
            counterId = 'valence-desc-count';
        } else if (variable === 'mood' && sub === 'arousal') {
            counterId = 'arousal-desc-count';
        }

        const counter = this.container.querySelector(`#${counterId}`);
        if (counter) {
            counter.textContent = input.value.length;
        }
    }

    loadData(data) {
        this.data = data;
        this.populateFromData();
        this.updateSummary();
    }

    populateFromData() {
        // Populate trust level
        if (this.data.trust_level) {
            this.populateSection('trust_level', this.data.trust_level);
        }

        // Populate mood variables
        if (this.data.mood) {
            if (this.data.mood.valence) {
                this.populateSubSection('mood', 'valence', this.data.mood.valence);
            }
            if (this.data.mood.arousal) {
                this.populateSubSection('mood', 'arousal', this.data.mood.arousal);
            }
        }

        // Populate environment variables
        if (this.data.environment) {
            if (this.data.environment.stress_level) {
                this.populateSubSection('environment', 'stress_level', this.data.environment.stress_level);
            }
            if (this.data.environment.time_pressure) {
                this.populateSubSection('environment', 'time_pressure', this.data.environment.time_pressure);
            }
        }
    }

    populateSection(variable, config) {
        const section = this.container.querySelector(`[data-variable="${variable}"]`);
        if (!section) return;

        const minInput = section.querySelector('.range-min');
        const maxInput = section.querySelector('.range-max');
        const descInput = section.querySelector('.range-desc');

        if (minInput) minInput.value = config.min;
        if (maxInput) maxInput.value = config.max;
        if (descInput) descInput.value = config.description || '';
    }

    populateSubSection(variable, sub, config) {
        const section = this.container.querySelector(`[data-variable="${variable}"] [data-sub="${sub}"]`);
        if (!section) return;

        const minInput = section.querySelector('.range-min');
        const maxInput = section.querySelector('.range-max');
        const descInput = section.querySelector('.range-desc');

        if (minInput) minInput.value = config.min;
        if (maxInput) maxInput.value = config.max;
        if (descInput) descInput.value = config.description || '';
    }

    collectData() {
        const data = {};

        // Collect trust level
        const trustSection = this.container.querySelector('[data-variable="trust_level"]');
        if (trustSection) {
            data.trust_level = this.collectSectionData(trustSection);
        }

        // Collect mood variables
        const moodSection = this.container.querySelector('[data-variable="mood"]');
        if (moodSection) {
            const moodData = {};

            const valenceData = this.collectSubSectionData(moodSection, 'valence');
            if (valenceData) moodData.valence = valenceData;

            const arousalData = this.collectSubSectionData(moodSection, 'arousal');
            if (arousalData) moodData.arousal = arousalData;

            if (Object.keys(moodData).length > 0) {
                data.mood = moodData;
            }
        }

        // Collect environment variables
        const envSection = this.container.querySelector('[data-variable="environment"]');
        if (envSection) {
            const envData = {};

            const stressData = this.collectSubSectionData(envSection, 'stress_level');
            if (stressData) envData.stress_level = stressData;

            const timeData = this.collectSubSectionData(envSection, 'time_pressure');
            if (timeData) envData.time_pressure = timeData;

            if (Object.keys(envData).length > 0) {
                data.environment = envData;
            }
        }

        return data;
    }

    collectSectionData(section) {
        const minInput = section.querySelector('.range-min');
        const maxInput = section.querySelector('.range-max');
        const descInput = section.querySelector('.range-desc');

        return {
            min: parseFloat(minInput?.value || 0),
            max: parseFloat(maxInput?.value || 100),
            description: descInput?.value || ''
        };
    }

    collectSubSectionData(section, subName) {
        const subSection = section.querySelector(`[data-sub="${subName}"]`);
        if (!subSection) return null;

        return this.collectSectionData(subSection);
    }

    validate() {
        const data = this.collectData();
        const errors = [];

        // Validate each range
        Object.entries(data).forEach(([variable, config]) => {
            if (variable === 'mood' || variable === 'environment') {
                Object.entries(config).forEach(([subVar, subConfig]) => {
                    if (subConfig.min >= subConfig.max) {
                        errors.push(`${variable}.${subVar}: minimum value must be less than maximum value`);
                    }
                });
            } else {
                if (config.min >= config.max) {
                    errors.push(`${variable}: minimum value must be less than maximum value`);
                }
            }
        });

        // Display validation results
        const status = this.container.querySelector('#ranges-validation-status');
        if (status) {
            if (errors.length > 0) {
                status.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Validation Errors:</strong>
                        <ul>${errors.map(e => `<li>${e}</li>`).join('')}</ul>
                    </div>
                `;
            } else {
                status.innerHTML = '<div class="alert alert-success">Variable ranges are valid!</div>';
            }
        }

        return errors.length === 0;
    }

    syncToJSON() {
        const data = this.collectData();
        const textarea = document.getElementById('template-variable-ranges');
        if (textarea) {
            textarea.value = JSON.stringify(data, null, 2);
        }

        // Show sync confirmation
        const status = this.container.querySelector('#ranges-validation-status');
        if (status) {
            status.innerHTML = '<div class="alert alert-info">Synced to JSON successfully!</div>';
            setTimeout(() => status.innerHTML = '', 2000);
        }
    }

    refresh() {
        console.log('Variable ranges builder refresh called');

        // Ensure container exists and is properly set up
        if (!this.container) {
            console.log('Container not found, recreating...');
            this.createContainer();
            this.setupEventListeners();
        }

        this.updateSummary();

        // Try to load data from JSON textarea
        const textarea = document.getElementById('template-variable-ranges');
        if (textarea && textarea.value.trim()) {
            try {
                const data = JSON.parse(textarea.value);
                this.loadData(data);
                console.log('Loaded data from JSON textarea:', data);
            } catch (e) {
                console.warn('Could not parse variable ranges JSON:', e);
            }
        } else {
            console.log('No data in JSON textarea, using defaults');
            // Load default data if no JSON is present
            this.loadData(this.getDefaultData());
        }
    }

    getDefaultData() {
        return {
            trust_level: {
                min: 0,
                max: 100,
                description: "User's trust level in the system (0-100)"
            },
            mood: {
                valence: {
                    min: -1.0,
                    max: 1.0,
                    description: "Emotional valence from negative to positive"
                },
                arousal: {
                    min: -1.0,
                    max: 1.0,
                    description: "Emotional arousal from calm to excited"
                }
            },
            environment: {
                stress_level: {
                    min: 0,
                    max: 100,
                    description: "Environmental stress level"
                },
                time_pressure: {
                    min: 0,
                    max: 100,
                    description: "Time pressure in the environment"
                }
            }
        };
    }

    // Enhanced methods for new features
    testTrustValue() {
        const testValue = this.container.querySelector('#trust-test-value')?.value;
        const resultElement = this.container.querySelector('#trust-test-result');

        if (!testValue || !resultElement) return;

        const value = parseFloat(testValue);
        const data = this.collectData();
        const trustConfig = data.trust_level;

        if (!trustConfig) {
            resultElement.innerHTML = '<span class="text-warning">Trust level not configured</span>';
            return;
        }

        if (value < trustConfig.min || value > trustConfig.max) {
            resultElement.innerHTML = `<span class="text-danger">Value ${value} is outside range [${trustConfig.min}, ${trustConfig.max}]</span>`;
        } else {
            const percentage = ((value - trustConfig.min) / (trustConfig.max - trustConfig.min)) * 100;
            let phase = 'Unknown';
            if (percentage <= 39) phase = 'Foundation';
            else if (percentage <= 69) phase = 'Expansion';
            else phase = 'Integration';

            resultElement.innerHTML = `<span class="text-success">✓ Valid - ${phase} phase (${percentage.toFixed(1)}%)</span>`;
        }
    }

    testMoodValues() {
        const valenceValue = this.container.querySelector('#valence-test-value')?.value;
        const arousalValue = this.container.querySelector('#arousal-test-value')?.value;
        const resultElement = this.container.querySelector('#mood-test-result');

        if (!valenceValue || !arousalValue || !resultElement) return;

        const valence = parseFloat(valenceValue);
        const arousal = parseFloat(arousalValue);
        const data = this.collectData();
        const moodConfig = data.mood;

        if (!moodConfig || !moodConfig.valence || !moodConfig.arousal) {
            resultElement.innerHTML = '<span class="text-warning">Mood variables not configured</span>';
            return;
        }

        const valenceValid = valence >= moodConfig.valence.min && valence <= moodConfig.valence.max;
        const arousalValid = arousal >= moodConfig.arousal.min && arousal <= moodConfig.arousal.max;

        if (!valenceValid || !arousalValid) {
            resultElement.innerHTML = '<span class="text-danger">Values outside configured ranges</span>';
        } else {
            let quadrant = '';
            if (valence >= 0 && arousal >= 0) quadrant = 'Excited';
            else if (valence >= 0 && arousal < 0) quadrant = 'Content';
            else if (valence < 0 && arousal < 0) quadrant = 'Sad';
            else quadrant = 'Angry';

            resultElement.innerHTML = `<span class="text-success">✓ Valid - ${quadrant} quadrant</span>`;
        }
    }

    autoConfigureTrust() {
        const trustSection = this.container.querySelector('[data-variable="trust_level"]');
        if (!trustSection) return;

        const minInput = trustSection.querySelector('.range-min');
        const maxInput = trustSection.querySelector('.range-max');
        const descInput = trustSection.querySelector('.range-desc');

        if (minInput) minInput.value = 0;
        if (maxInput) maxInput.value = 100;
        if (descInput) descInput.value = "User's trust level in the system (0-100)";

        this.handleInputChange(minInput);
        this.showAutoConfigureMessage('Trust level auto-configured with standard 0-100 range');
    }

    autoConfigureMood() {
        const moodSection = this.container.querySelector('[data-variable="mood"]');
        if (!moodSection) return;

        // Configure valence
        const valenceSection = moodSection.querySelector('[data-sub="valence"]');
        if (valenceSection) {
            const minInput = valenceSection.querySelector('.range-min');
            const maxInput = valenceSection.querySelector('.range-max');
            const descInput = valenceSection.querySelector('.range-desc');

            if (minInput) minInput.value = -1.0;
            if (maxInput) maxInput.value = 1.0;
            if (descInput) descInput.value = "Emotional valence from negative to positive (-1.0 to 1.0)";
        }

        // Configure arousal
        const arousalSection = moodSection.querySelector('[data-sub="arousal"]');
        if (arousalSection) {
            const minInput = arousalSection.querySelector('.range-min');
            const maxInput = arousalSection.querySelector('.range-max');
            const descInput = arousalSection.querySelector('.range-desc');

            if (minInput) minInput.value = -1.0;
            if (maxInput) maxInput.value = 1.0;
            if (descInput) descInput.value = "Emotional arousal from calm to excited (-1.0 to 1.0)";
        }

        this.updateSummary();
        this.showAutoConfigureMessage('Mood variables auto-configured with standard circumplex model ranges');
    }

    autoConfigureEnvironment() {
        const envSection = this.container.querySelector('[data-variable="environment"]');
        if (!envSection) return;

        // Configure stress level
        const stressSection = envSection.querySelector('[data-sub="stress_level"]');
        if (stressSection) {
            const minInput = stressSection.querySelector('.range-min');
            const maxInput = stressSection.querySelector('.range-max');
            const descInput = stressSection.querySelector('.range-desc');

            if (minInput) minInput.value = 0;
            if (maxInput) maxInput.value = 100;
            if (descInput) descInput.value = "Environmental stress level (0-100)";
        }

        // Configure time pressure
        const timeSection = envSection.querySelector('[data-sub="time_pressure"]');
        if (timeSection) {
            const minInput = timeSection.querySelector('.range-min');
            const maxInput = timeSection.querySelector('.range-max');
            const descInput = timeSection.querySelector('.range-desc');

            if (minInput) minInput.value = 0;
            if (maxInput) maxInput.value = 100;
            if (descInput) descInput.value = "Time pressure in the environment (0-100)";
        }

        this.updateSummary();
        this.showAutoConfigureMessage('Environment variables auto-configured with standard 0-100 ranges');
    }

    showAutoConfigureMessage(message) {
        const status = this.container.querySelector('#ranges-validation-status');
        if (status) {
            status.innerHTML = `<div class="alert alert-info"><i class="fas fa-magic"></i> ${message}</div>`;
            setTimeout(() => status.innerHTML = '', 3000);
        }
    }

    highlightMoodQuadrant(quadrant) {
        // Remove previous highlights
        this.container.querySelectorAll('.quadrant').forEach(q => {
            q.classList.remove('highlighted');
        });

        // Highlight selected quadrant
        quadrant.classList.add('highlighted');

        // Show quadrant info
        const valence = quadrant.dataset.valence;
        const arousal = quadrant.dataset.arousal;
        const label = quadrant.querySelector('.quadrant-label').textContent;

        const resultElement = this.container.querySelector('#mood-test-result');
        if (resultElement) {
            resultElement.innerHTML = `<span class="text-info">Selected: ${label} (${valence} valence, ${arousal} arousal)</span>`;
        }
    }

    applySuggestion(button) {
        const config = button.dataset.config;
        const trustSection = this.container.querySelector('[data-variable="trust_level"]');

        if (!trustSection) return;

        const minInput = trustSection.querySelector('.range-min');
        const maxInput = trustSection.querySelector('.range-max');
        const descInput = trustSection.querySelector('.range-desc');

        switch (config) {
            case 'percentage':
                if (minInput) minInput.value = 0;
                if (maxInput) maxInput.value = 100;
                if (descInput) descInput.value = "Trust level as percentage (0-100)";
                break;
            case 'scale':
                if (minInput) minInput.value = 1;
                if (maxInput) maxInput.value = 10;
                if (descInput) descInput.value = "Trust level on 1-10 scale";
                break;
            case 'normalized':
                if (minInput) minInput.value = 0;
                if (maxInput) maxInput.value = 1;
                if (descInput) descInput.value = "Normalized trust level (0.0-1.0)";
                break;
        }

        this.handleInputChange(minInput);
    }
}

// Export for global access
window.VariableRangesBuilder = VariableRangesBuilder;
