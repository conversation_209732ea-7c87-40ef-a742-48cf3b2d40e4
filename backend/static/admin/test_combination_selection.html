<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Context Preview Combination Selection Test</title>
    <link rel="stylesheet" href="css/benchmark_management.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .mock-form {
            display: none;
        }
        #template-variable-ranges {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-test-tube"></i> Context Preview Combination Selection Test</h1>
            <p>This page tests the new combination selection functionality for Multi-Range Contextual Evaluation.</p>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> Test Instructions</h3>
            <ol>
                <li>Check the "Multi-Range Contextual Evaluation" checkbox below</li>
                <li>You should see a table of combinations with checkboxes</li>
                <li>Select some combinations using the individual checkboxes</li>
                <li>Try the "Select All" and "Clear Selection" buttons</li>
                <li>Enter a name and save the combination set</li>
                <li>Load the saved set from the dropdown</li>
                <li>Delete the saved set</li>
            </ol>
        </div>

        <!-- Mock form elements that the ContextPreview expects -->
        <div class="mock-form">
            <textarea id="template-variable-ranges">{
  "trust_level": {
    "0-39": {},
    "40-69": {},
    "70-100": {}
  },
  "mood": {
    "valence": {
      "-1.0-0.0": {},
      "0.0-1.0": {}
    },
    "arousal": {
      "-1.0-0.0": {},
      "0.0-1.0": {}
    }
  },
  "environment": {
    "stress_level": {
      "0-30": {},
      "31-70": {},
      "71-100": {}
    },
    "time_pressure": {
      "0-30": {},
      "31-70": {},
      "71-100": {}
    }
  }
}</textarea>
        </div>

        <div class="test-section">
            <h3>Multi-Range Contextual Evaluation Test</h3>
            
            <div class="config-row">
                <div class="config-group">
                    <label>
                        <input type="checkbox" id="multi-range-evaluation">
                        Multi-Range Contextual Evaluation
                    </label>
                    <small class="help-text">Test against all defined variable ranges instead of just the current context</small>
                </div>
            </div>

            <!-- This div will be populated by the ContextPreview class -->
            <div class="multi-range-combinations" id="multi-range-combinations" style="display: none;">
                <h6><i class="fas fa-list"></i> Combinations to be tested:</h6>
                
                <div class="combination-set-controls">
                    <div class="control-row">
                        <div class="control-group">
                            <label for="combination-set-name">Set Name:</label>
                            <input type="text" id="combination-set-name" class="form-control" placeholder="Enter set name...">
                        </div>
                        <div class="control-group">
                            <label for="saved-combination-sets">Load Saved Set:</label>
                            <select id="saved-combination-sets" class="form-control">
                                <option value="">Select a saved set...</option>
                            </select>
                        </div>
                    </div>
                    <div class="control-row">
                        <button type="button" id="save-combination-set-btn" class="btn btn-primary btn-sm">
                            <i class="fas fa-save"></i> Save Set
                        </button>
                        <button type="button" id="select-all-combinations-btn" class="btn btn-secondary btn-sm">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" id="clear-selection-btn" class="btn btn-secondary btn-sm">
                            <i class="fas fa-square"></i> Clear Selection
                        </button>
                        <button type="button" id="delete-combination-set-btn" class="btn btn-danger btn-sm" disabled>
                            <i class="fas fa-trash"></i> Delete Set
                        </button>
                    </div>
                </div>
                
                <div class="combinations-container" id="combinations-container">
                    <p class="text-muted">Enable Multi-Range Contextual Evaluation to see combinations</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <p class="text-muted">Interact with the controls above to test the functionality.</p>
            </div>
        </div>
    </div>

    <!-- Include the ContextPreview JavaScript -->
    <script src="js/context_preview.js"></script>
    
    <script>
        // Initialize the test
        document.addEventListener('DOMContentLoaded', function() {
            // Create a mock container that mimics the modal structure
            const mockContainer = document.querySelector('.test-container');
            
            // Create a simplified ContextPreview instance for testing
            const contextPreview = new ContextPreview();
            
            // Override the container to use our test container
            contextPreview.container = mockContainer;
            
            // Set up the event listeners manually since we're not in a modal
            contextPreview.setupCombinationEventListeners();
            
            // Initialize the multi-range combinations display
            contextPreview.updateMultiRangeCombinations();
            
            // Add some test logging
            const resultsDiv = document.getElementById('test-results');
            
            function logResult(message) {
                const timestamp = new Date().toLocaleTimeString();
                resultsDiv.innerHTML += `<p><strong>[${timestamp}]</strong> ${message}</p>`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
            
            // Override some methods to add logging
            const originalSaveCombinationSet = contextPreview.saveCombinationSet.bind(contextPreview);
            contextPreview.saveCombinationSet = function() {
                logResult('Save Combination Set button clicked');
                originalSaveCombinationSet();
            };
            
            const originalLoadCombinationSet = contextPreview.loadCombinationSet.bind(contextPreview);
            contextPreview.loadCombinationSet = function() {
                logResult('Load Combination Set dropdown changed');
                originalLoadCombinationSet();
            };
            
            const originalSelectAllCombinations = contextPreview.selectAllCombinations.bind(contextPreview);
            contextPreview.selectAllCombinations = function() {
                logResult('Select All button clicked');
                originalSelectAllCombinations();
            };
            
            const originalClearCombinationSelection = contextPreview.clearCombinationSelection.bind(contextPreview);
            contextPreview.clearCombinationSelection = function() {
                logResult('Clear Selection button clicked');
                originalClearCombinationSelection();
            };
            
            logResult('Context Preview Combination Selection Test initialized successfully!');
        });
        
        // Helper function to manually setup combination event listeners
        ContextPreview.prototype.setupCombinationEventListeners = function() {
            // Multi-range evaluation checkbox
            const multiRangeCheckbox = this.container.querySelector('#multi-range-evaluation');
            if (multiRangeCheckbox) {
                multiRangeCheckbox.addEventListener('change', () => this.updateMultiRangeCombinations());
            }

            // Combination set management event listeners
            const saveCombinationSetBtn = this.container.querySelector('#save-combination-set-btn');
            if (saveCombinationSetBtn) {
                saveCombinationSetBtn.addEventListener('click', () => this.saveCombinationSet());
            }

            const savedCombinationSetsSelect = this.container.querySelector('#saved-combination-sets');
            if (savedCombinationSetsSelect) {
                savedCombinationSetsSelect.addEventListener('change', () => this.loadCombinationSet());
            }

            const selectAllBtn = this.container.querySelector('#select-all-combinations-btn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', () => this.selectAllCombinations());
            }

            const clearSelectionBtn = this.container.querySelector('#clear-selection-btn');
            if (clearSelectionBtn) {
                clearSelectionBtn.addEventListener('click', () => this.clearCombinationSelection());
            }

            const deleteCombinationSetBtn = this.container.querySelector('#delete-combination-set-btn');
            if (deleteCombinationSetBtn) {
                deleteCombinationSetBtn.addEventListener('click', () => this.deleteCombinationSet());
            }
        };
    </script>
</body>
</html>
