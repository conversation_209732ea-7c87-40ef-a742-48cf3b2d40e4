/* Shared Modal Base Styles for Benchmark History */

/* Base Modal Structure */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #fefefe;
    margin: 3% auto;
    padding: 0;
    border: 1px solid #888;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
    max-height: 95vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Close Button */
.close {
    color: #aaa;
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    z-index: 10;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close:hover,
.close:focus {
    color: #000;
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* Modal Header Styles */
.modal-header {
    padding: 25px;
    border-radius: 8px;
    margin: 20px 20px 0 20px;
    text-align: center;
    position: relative;
}

.modal-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
    font-weight: 600;
}

.modal-description {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
    line-height: 1.4;
}

/* Modal Body Styles */
.modal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* Loading States */
.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6c757d;
    text-align: center;
}

.modal-loading .loader {
    margin-bottom: 20px;
}

.modal-loading p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

/* Loader Animation */
.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-icon {
    font-size: 16px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-help {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
    line-height: 1.4;
}

/* Section Styles */
.modal-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.modal-section h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.2em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.metric {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: all 0.2s ease;
}

.metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #495057;
}

/* JSON Viewer */
.json-viewer {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}

@media (max-width: 768px) {
    .modal-content {
        width: 98%;
        margin: 1% auto;
        max-height: 98vh;
    }
    
    .modal-header {
        padding: 20px 15px;
        margin: 15px 15px 0 15px;
    }
    
    .modal-header h2 {
        font-size: 1.5em;
    }
    
    .modal-description {
        font-size: 1em;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .close {
        top: 10px;
        right: 15px;
        width: 30px;
        height: 30px;
        font-size: 24px;
    }
}

/* Scrollbar Styling */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
