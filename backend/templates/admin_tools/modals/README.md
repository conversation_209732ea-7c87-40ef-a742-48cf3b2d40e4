# Specialized Benchmark Evaluation Modals

This directory contains specialized modal implementations for the benchmark history interface, providing tailored views for different types of evaluations. The modals have been extracted from the main template to improve maintainability and organization.

## Files Overview

### 1. `agent_evaluation_modal.html`
**Purpose**: Optimized modal for individual agent performance analysis

**Key Features**:
- 🤖 Enhanced agent-focused header with gradient styling
- ⚙️ LLM Configuration optimization insights
- ⚡ Performance metrics with actionable recommendations
- ⭐ Quality scoring and semantic evaluation
- 🛠️ Tool usage analysis with pie chart
- 📋 Context package analysis
- 💡 Dynamic optimization recommendations
- 📱 Responsive design with improved scrolling

**Use Case**: When analyzing single-agent benchmark runs to optimize:
- LLM model selection and temperature tuning
- Prompt engineering and response quality
- Tool usage patterns and efficiency
- Cost optimization strategies

### 2. `wheel_generation_evaluation_modal.html` (RENAMED & ENHANCED)
**Purpose**: Dedicated modal for wheel generation workflow debugging and analysis

**Key Features**:
- 🎡 **Wheel Generation Focus**: Specifically designed for debugging wheel generation workflows
- 🔄 **Agent Coordination Flow**: Visual representation of the multi-agent process (Orchestrator → Resource & Capacity → Engagement & Pattern → Psychological → Strategy → Wheel/Activity → Ethical → Final Integration)
- 🎯 **Wheel Items Deep Dive**: Detailed analysis of each generated wheel item with tailoring process visibility
- 🔧 **Activity Tailoring Process**: Shows how generic activities are transformed into personalized activities
- ⏱️ **Agent Communication Timeline**: Chronological view of agent execution with input/output details
- 🛠️ **Tool Call Analysis**: Breakdown of all tool calls with parameters and responses
- 🔍 **Debug Features**: Interactive elements to trace the creation process of inappropriate items
- 📱 Responsive design with improved scrolling

### 3. `wheel_item_crafting_modal.html` (NEW)
**Purpose**: Dedicated modal for showing the complete crafting sequence of a single wheel item

**Key Features**:
- 🔧 **Complete Crafting Journey**: Step-by-step visualization from generic activity to final wheel item
- 📋 **Generic Activity Source**: Shows the original activity template and properties
- 🧠 **Strategy Framework Decision**: Displays selection criteria and strategy agent decisions
- 🎯 **Activity Tailoring Process**: Detailed view of how the activity was personalized
- 🎡 **Wheel Integration**: Shows probability assignment and strategic placement
- ✅ **Final Result**: Complete wheel item ready for user selection
- 🎯 **Decision Points**: Rationale behind each transformation step
- 🤖 **Agent Contributions**: Detailed agent communication and processing
- 🔧 **Tool Calls**: Complete tool usage with parameters and responses
- 📱 Responsive design with step-by-step flow visualization

**Usage**: Triggered by clicking "Show Tailoring Process" button on any wheel item in the wheel generation evaluation modal

**Interactive Event Tree**:
- **Foldable Events**: Click any event to expand/collapse details
- **Control Buttons**:
  - 📖 Expand All / 📕 Collapse All
  - ⚠️ Issues Only (filter problematic events)
  - 👁️ Show All (reset filters)
- **Smart Detection**:
  - 🎭 Mock tools (yellow border)
  - 🔄 Fallback strategies (blue border)
  - ❌ Errors (red border)
  - ⏱️ Slow execution warnings
- **Tool Call Visibility**: Clear display of agent tool usage with icons
- **Input/Output Analysis**: Expandable JSON data with summaries

### 3. `quick_test_config_modal.html`
**Purpose**: Configuration interface for quick benchmark testing

**Key Features**:
- ⚡ Modern gradient header design
- 📋 Organized form sections for better UX
- 🔧 Comprehensive execution mode selection
- 👤 User profile selection for testing
- 📊 Evaluation template preview
- 💾 Configuration persistence
- 📱 Fully responsive design

**Form Sections**:
- **Test Configuration**: Scenario, runs count, semantic evaluation
- **Execution Settings**: Mode selection, user profiles
- **Evaluation Template**: Template selection with preview

### 4. `comparison_modal.html`
**Purpose**: Side-by-side comparison of benchmark execution sessions

**Key Features**:
- 📊 Professional comparison interface
- 🔍 Side-by-side column layout
- 📈 Performance metrics comparison
- 💰 Resource usage analysis
- 📋 Export and report generation
- 🎯 Comparison summary statistics
- 📱 Responsive design for mobile viewing

**Comparison Sections**:
- **Performance Metrics**: Duration, success rate, semantic scores
- **Configuration**: LLM settings, parameters
- **Resource Usage**: Token counts, estimated costs

### 5. `modal_utilities.js`
**Purpose**: Shared utility functions for all modals

**Functions**:
- `renderSemanticEvaluationDetails()`: Multi-model evaluation rendering
- `renderContextPackageDetails()`: Context analysis utilities
- `renderWorkflowAgentCommunications()`: Agent communication timelines
- `toggleSection()`: Generic section expand/collapse functionality

### 6. Shared Base Styles
**Location**: `backend/static/admin_tools/modals/modal_base_styles.css`

**Purpose**: Common styling foundation for all modals

**Features**:
- 🎨 Consistent modal structure and animations
- 🔄 Smooth slide-in animations
- 📱 Responsive design patterns
- ♿ Accessibility support (focus management, high contrast)
- 🎯 Modern button and form styling
- 📊 Standardized metrics grid layouts
- 🖱️ Enhanced scrollbar styling

## Implementation Details

### Modal Architecture
The modal system follows a modular architecture:

1. **Base Styles**: Shared CSS foundation for consistent appearance
2. **Specialized Templates**: Individual HTML templates for each modal type
3. **Utility Functions**: Shared JavaScript functions for common operations
4. **Main Integration**: Template includes and routing logic

### Modal Routing Logic
The system automatically detects evaluation type based on the `execution_type` field:

```javascript
const isWorkflowEvaluation = data.execution_type && data.execution_type.includes('Workflow');

if (isWorkflowEvaluation) {
    // Show workflow modal with interactive event tree
    workflowModalBody.innerHTML = '<div class="modal-loading"><div class="loader"></div><p>Loading...</p></div>';
    workflowDetailsModal.style.display = 'block';
    await renderWorkflowDetails(data, runId);
} else {
    // Show agent modal with optimization insights
    agentModalBody.innerHTML = '<div class="modal-loading"><div class="loader"></div><p>Loading...</p></div>';
    agentDetailsModal.style.display = 'block';
    await renderAgentDetails(data, runId);
}
```

### Workflow Event Tree Features

#### Issue Detection System
The interactive tree automatically detects and highlights:

1. **Mock Usage** (🎭 Yellow):
   - Detects "mock", "test", "dummy" in input/output
   - Helps identify development vs production runs

2. **Fallback Strategies** (🔄 Blue):
   - Detects "fallback", "default", "backup" in outputs
   - Shows when agents used alternative approaches

3. **Execution Errors** (❌ Red):
   - Failed agent executions
   - Error messages and stack traces

4. **Performance Issues** (⏱️ Orange):
   - Slow executions (>5000ms)
   - Bottleneck identification

#### Tool Call Analysis
Each event shows:
- 🛠️ Tool names with appropriate icons
- Parameter counts for complexity assessment
- Input/output data summaries
- Full JSON data (expandable)

#### Visual Design
- **Chronological Timeline**: Events sorted by timestamp
- **Color-Coded Borders**: Immediate issue identification
- **Gradient Headers**: Visual distinction when expanded
- **Responsive Layout**: Works on different screen sizes
- **Smooth Animations**: Professional expand/collapse effects

## Usage Guidelines

### For Agent Evaluations
Use when you need to:
- Optimize LLM configuration (model, temperature)
- Analyze individual agent performance
- Review semantic quality scores
- Understand tool usage patterns
- Get specific optimization recommendations

### For Wheel Generation Evaluations
Use when you need to:
- **Debug inappropriate wheel items**: Trace why specific activities were selected and how they were tailored
- **Understand agent coordination**: See the complete flow from Orchestrator through all specialized agents
- **Analyze activity tailoring**: Review how generic activities become personalized for the user
- **Identify performance bottlenecks**: Find slow agents or tool calls in the generation process
- **Review tool usage**: See what data was used to make decisions (user profile, activity catalog, etc.)
- **Validate ethical compliance**: Check the ethical validation process and any concerns raised
- **Optimize generation quality**: Understand the strategy framework and domain distribution decisions

#### Wheel Generation Debugging Workflow
1. **Run a wheel generation benchmark** in the admin interface
2. **Click on the benchmark result** to open the wheel generation evaluation modal
3. **Review the agent flow** to understand the coordination sequence
4. **Examine wheel items** to see what was generated and why
5. **Click "Show Tailoring Process"** on any item to see how it was customized
6. **Review the timeline** to identify performance issues or failures
7. **Analyze tool calls** to understand what data was used in decisions

This provides complete traceability for debugging inappropriate wheel items or understanding the decision-making process.

#### Wheel Item Crafting Deep Dive
For detailed analysis of individual wheel items:
1. **Open wheel generation evaluation modal** from benchmark results
2. **Click "Show Tailoring Process"** on any wheel item
3. **Review the 5-step crafting sequence**:
   - **Generic Activity Source**: Original template and properties
   - **Strategy Framework Decision**: Selection criteria and agent decisions
   - **Activity Tailoring Process**: Personalization transformations
   - **Wheel Integration**: Probability assignment and placement
   - **Final Result**: Complete wheel item with quality metrics
4. **Analyze decision points** to understand why specific choices were made
5. **Review agent contributions** to see which agents influenced the item
6. **Examine tool calls** to understand what data was used

This provides the deepest level of debugging for understanding why a specific wheel item was created and how it can be improved.

## Integration

The modals are integrated into the main benchmark history template via:

```html
<!-- Include specialized modals -->
{% include 'admin_tools/modals/agent_evaluation_modal.html' %}
{% include 'admin_tools/modals/wheel_generation_evaluation_modal.html' %}
{% include 'admin_tools/modals/wheel_item_crafting_modal.html' %}
{% include 'admin_tools/modals/quick_test_config_modal.html' %}
{% include 'admin_tools/modals/comparison_modal.html' %}
```

The base styles are included in the template head:

```html
<!-- Modal base styles -->
<link rel="stylesheet" type="text/css" href="{% static 'admin_tools/modals/modal_base_styles.css' %}">
```

The main template handles:
- Modal element declarations and routing
- Event listeners for open/close actions
- Data fetching and API integration
- Shared utility functions and chart management

## Benefits

### Code Organization
1. **Modular Structure**: Each modal is self-contained with its own template and styles
2. **Maintainability**: Easier to update and debug individual modal components
3. **Reusability**: Shared base styles and utilities reduce code duplication
4. **Scalability**: Easy to add new modal types without affecting existing ones

### User Experience
1. **Improved UX**: Context-appropriate information display for each evaluation type
2. **Better Debugging**: Interactive workflow event exploration with issue detection
3. **Enhanced Optimization**: Specific recommendations per evaluation type
4. **Visual Clarity**: Color-coded issue identification and modern styling
5. **Efficient Analysis**: Foldable interface preserves high-level readability
6. **Professional Appearance**: Consistent design with smooth animations
7. **Responsive Design**: Works seamlessly across desktop and mobile devices

### Performance
1. **Faster Loading**: Modular loading reduces initial page size
2. **Better Caching**: Separate CSS files can be cached independently
3. **Improved Scrolling**: Enhanced scrollbar styling and overflow handling

## File Structure Summary

```
backend/templates/admin_tools/modals/
├── README.md                              # This documentation
├── agent_evaluation_modal.html            # Agent performance analysis modal
├── wheel_generation_evaluation_modal.html # Wheel generation debugging modal
├── wheel_item_crafting_modal.html         # Single wheel item crafting sequence modal
├── quick_test_config_modal.html           # Quick test configuration interface
├── comparison_modal.html                  # Benchmark comparison interface
└── modal_utilities.js                     # Shared utility functions

backend/static/admin_tools/modals/
└── modal_base_styles.css               # Shared base styles for all modals
```

## Future Enhancements

Potential improvements:
- **Export Functionality**: CSV/JSON export for comparison data and event trees
- **Advanced Filtering**: Filter by agent, time range, execution mode, etc.
- **Performance Trends**: Analysis across multiple runs with trend visualization
- **Real-time Updates**: Live event streaming for active workflows
- **Integration**: Connect with external monitoring and alerting tools
- **Accessibility**: Enhanced keyboard navigation and screen reader support
- **Theming**: Dark mode and customizable color schemes
