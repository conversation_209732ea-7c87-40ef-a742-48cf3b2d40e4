<!-- Wheel Generation Evaluation Modal -->
<div id="workflow-details-modal" class="modal">
    <div class="modal-content wheel-generation-modal">
        <span class="close">&times;</span>
        <div class="modal-header wheel-generation-header">
            <h2>🎡 Wheel Generation Workflow Analysis</h2>
            <p class="modal-description">
                Deep analysis of the wheel generation process: agent coordination, tool calls, activity tailoring, and decision-making flow. Debug inappropriate items by tracing their creation process.
            </p>
            <div class="architecture-badges">
                <span class="architecture-badge wheel-generation" title="Wheel Generation Workflow">
                    🎡 Wheel Generation
                </span>
                <span class="architecture-badge multi-agent" title="Multi-Agent Coordination">
                    🤖 Multi-Agent
                </span>
                <span class="architecture-badge debugging" title="Debugging & Analysis">
                    🔍 Debug Mode
                </span>
            </div>
        </div>
        <div id="workflow-modal-body" class="workflow-modal-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading wheel generation analysis...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Wheel Generation Modal Styles */
.wheel-generation-modal .modal-content {
    max-width: 1600px;
    width: 98%;
}

.wheel-generation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.wheel-generation-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.architecture-badges {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.architecture-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: help;
}

.architecture-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.architecture-badge.wheel-generation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.architecture-badge.multi-agent {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.architecture-badge.debugging {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced Context Explorer Styles */
.enhanced-context-explorer {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.context-explorer-header {
    margin-bottom: 20px;
}

.context-explorer-header h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1.2em;
}

.architecture-info {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.version-badge, .feature-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    color: white;
}

.version-badge.enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.version-badge.standard {
    background: #6c757d;
}

.feature-badge.mentor {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-badge.workflow {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced Tab System */
.context-tabs-enhanced {
    margin-top: 15px;
}

.tab-buttons-enhanced {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tab-button-enhanced {
    background: #e9ecef;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.tab-button-enhanced:hover {
    background: #dee2e6;
    transform: translateY(-1px);
}

.tab-button-enhanced.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.tab-content-enhanced {
    min-height: 200px;
}

.tab-pane-enhanced {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-pane-enhanced.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Context Overview Grid */
.context-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.overview-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.overview-card .card-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.overview-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.overview-card p {
    margin: 5px 0;
    font-size: 12px;
    color: #6c757d;
}

/* Trust Level Meter */
.trust-level-display {
    margin: 15px 0;
}

.trust-meter {
    position: relative;
    background: #e9ecef;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.trust-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}

.trust-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Confidence Meter */
.confidence-meter {
    position: relative;
    background: #e9ecef;
    height: 16px;
    border-radius: 8px;
    overflow: hidden;
    margin: 5px 0;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.confidence-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.workflow-modal-body {
    max-height: 75vh;
    overflow-y: auto;
}

.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.modal-loading .loader {
    margin-bottom: 15px;
}

/* Enhanced scrollbar for workflow modal body */
.workflow-modal-body::-webkit-scrollbar {
    width: 8px;
}

.workflow-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.workflow-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.workflow-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Interactive Event Tree Styles */
.workflow-event-tree {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.event-tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.tree-controls {
    display: flex;
    gap: 10px;
}

.tree-control-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.tree-control-btn:hover {
    background: #0056b3;
}

.tree-control-btn.secondary {
    background: #6c757d;
}

.tree-control-btn.secondary:hover {
    background: #545b62;
}

/* Event Tree Structure */
.event-tree-container {
    position: relative;
    padding-left: 30px;
}

.event-tree-line {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.workflow-event {
    position: relative;
    margin-bottom: 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.workflow-event:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.workflow-event.has-issues {
    border-left: 4px solid #dc3545;
}

.workflow-event.has-mocks {
    border-left: 4px solid #ffc107;
}

.workflow-event.has-fallbacks {
    border-left: 4px solid #17a2b8;
}

.event-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.event-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.event-header.expanded {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.event-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.event-sequence {
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
}

.event-agent {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.event-header.expanded .event-agent {
    color: white;
}

.event-stage {
    background: #6c757d;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-timestamp {
    color: #6c757d;
    font-size: 12px;
    font-family: monospace;
}

.event-header.expanded .event-timestamp {
    color: rgba(255,255,255,0.8);
}

.event-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    font-size: 16px;
}

.status-indicator.success { color: #28a745; }
.status-indicator.error { color: #dc3545; }
.status-indicator.warning { color: #ffc107; }

.event-duration {
    background: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.event-header.expanded .event-duration {
    background: rgba(255,255,255,0.2);
    color: white;
}

.event-toggle {
    font-size: 14px;
    transition: transform 0.3s ease;
    color: #6c757d;
}

.event-header.expanded .event-toggle {
    transform: rotate(180deg);
    color: white;
}

/* Event Details */
.event-details {
    display: none;
    padding: 20px;
    background: #fdfdfd;
}

.event-details.expanded {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

.event-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.detail-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.detail-panel-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-panel-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.tool-calls-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tool-call-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tool-call-icon {
    font-size: 16px;
}

.tool-call-name {
    font-weight: 600;
    color: #007bff;
}

.tool-call-params {
    font-size: 12px;
    color: #6c757d;
    margin-left: auto;
}

/* Issue Indicators */
.issue-indicators {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.issue-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.issue-badge.mock {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.issue-badge.fallback {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.issue-badge.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.issue-badge.slow {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Enhanced Tool Calls Display */
.tool-calls-enhanced {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.tool-call-enhanced {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.tool-call-enhanced:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tool-call-header {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.tool-call-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-call-name {
    font-weight: 600;
    color: #1a73e8;
    font-size: 14px;
}

.tool-call-index {
    background: #1a73e8;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
}

.tool-call-meta {
    display: flex;
    align-items: center;
    gap: 6px;
}

.tool-duration {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.mock-badge {
    background: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #ffeaa7;
}

.error-badge {
    background: #f8d7da;
    color: #721c24;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #f5c6cb;
}

.tool-call-details {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tool-calls-count {
    background: #1a73e8;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    margin-left: auto;
}

/* Enhanced LLM Call Display */
.llm-call-enhanced {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border: 1px solid #ce93d8;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.llm-call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.llm-model-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.llm-model-name {
    font-weight: 600;
    color: #7b1fa2;
    font-size: 14px;
}

.llm-temperature {
    background: #7b1fa2;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.llm-tokens {
    display: flex;
    gap: 8px;
}

.token-count {
    background: rgba(123, 31, 162, 0.1);
    color: #7b1fa2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(123, 31, 162, 0.3);
}

.llm-cost {
    background: #4caf50;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* JSON Data Display */
.json-data-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 4px 0;
}

.json-data-container.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.json-data-header {
    background: #e9ecef;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.json-data-header:hover {
    background: #dee2e6;
}

.json-data-container.error .json-data-header {
    background: #f8d7da;
    color: #721c24;
}

.json-data-content {
    display: none;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.json-data-content.expanded {
    display: block;
}

.json-data-content pre {
    margin: 0;
    font-size: 11px;
    line-height: 1.4;
    color: #495057;
    background: white;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.toggle-icon {
    transition: transform 0.2s ease;
}

/* Enhanced Insight Cards */
.insight-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.insight-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.insight-card.performance {
    border-left: 4px solid #28a745;
}

.insight-card.agents {
    border-left: 4px solid #007bff;
}

.insight-card.output-quality {
    border-left: 4px solid #6f42c1;
}

.insight-card.cost-analysis {
    border-left: 4px solid #fd7e14;
}

.card-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.8;
}

.card-content h5 {
    margin: 0 0 15px 0;
    color: #495057;
    font-weight: 600;
}

.card-content p {
    margin: 8px 0;
    color: #6c757d;
    font-size: 14px;
}

.optimization-tip {
    background: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
    padding: 8px 12px;
    margin-top: 15px;
    border-radius: 4px;
    font-size: 13px;
    color: #495057 !important;
}

/* Performance Breakdown */
.performance-breakdown {
    margin: 30px 0;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.performance-breakdown h4 {
    margin: 0 0 20px 0;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.metric-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.metric-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-header {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.metric-icon {
    font-size: 1.2em;
}

.metric-title {
    font-weight: 600;
    font-size: 14px;
}

.metric-details {
    padding: 20px;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-value {
    font-weight: 600;
    font-size: 14px;
}

.metric-value.success {
    color: #28a745;
}

.metric-value.warning {
    color: #ffc107;
}

.metric-value.error {
    color: #dc3545;
}

.metric-value.info {
    color: #17a2b8;
}

/* Workflow Summary Cards */
.workflow-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.workflow-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.workflow-summary-card h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    opacity: 0.9;
}

.workflow-summary-card .value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.workflow-summary-card .label {
    font-size: 12px;
    opacity: 0.8;
}

/* New Section Styles */

/* Wheel Output Section */
.wheel-output-section .wheel-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.wheel-meta {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.wheel-info h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1.3em;
}

.trust-phase {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.trust-phase.foundation {
    background: #e3f2fd;
    color: #1976d2;
}

.wheel-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.activity-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.activity-header h5 {
    margin: 0;
    color: #495057;
}

.domain-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    background: #e9ecef;
    color: #495057;
}

.domain-badge.creativity { background: #ffe6e6; color: #d63384; }
.domain-badge.wellness { background: #e6f7ff; color: #0969da; }
.domain-badge.physical { background: #e6ffe6; color: #28a745; }
.domain-badge.learning { background: #fff3cd; color: #856404; }
.domain-badge.personal_growth { background: #f3e5f5; color: #6f42c1; }

.activity-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.activity-meta {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    font-size: 12px;
    color: #6c757d;
}

.value-proposition {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

.value-proposition h6 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #495057;
}

.value-proposition p {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
    color: #6c757d;
}

/* Psychological Section */
.psychological-section .psych-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.trust-analysis {
    margin-bottom: 25px;
}

.trust-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.trust-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.state-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.state-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
}

.beliefs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.belief-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.belief-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.belief-strength {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.strength-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.traits-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.trait-badge {
    padding: 4px 12px;
    background: #e7f3ff;
    color: #0969da;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Ethical Section */
.ethical-section .ethical-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.validation-status {
    margin-bottom: 25px;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.approved {
    background: #d4edda;
    color: #155724;
}

.validation-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.validation-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.principle-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.principle-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.validation-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat {
    text-align: center;
}

.stat .stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #495057;
}

.stat .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

.vulnerability-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.vulnerability-badge {
    padding: 4px 12px;
    background: #fff3cd;
    color: #856404;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.boundaries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.boundary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
}

/* Strategy Section */
.strategy-section .strategy-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.gap-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.gap-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.domain-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.domain-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.domain-percentage {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.percentage-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.percentage-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.domain-reason {
    font-size: 12px;
    color: #6c757d;
    margin: 8px 0 0 0;
}

.distribution-summary {
    margin-top: 20px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.criteria-sections {
    margin-top: 15px;
}

.criteria-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.criteria-section h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.criteria-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.criteria-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.criteria-item:last-child {
    border-bottom: none;
}

/* Semantic Quality Section */
.semantic-section .semantic-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.quality-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quality-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.dimensions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.dimension-badge {
    padding: 4px 12px;
    background: #e7f3ff;
    color: #0969da;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.model-evaluation {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.model-evaluation h5 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
}

.model-score {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dimension-score {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.dimension-name {
    font-weight: 600;
    color: #495057;
}

.dimension-reasoning {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.tone-analysis {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.tone-analysis h6 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
}

.tone-score {
    margin-bottom: 8px;
    font-weight: 600;
}

.overall-reasoning {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.overall-reasoning h6 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
}

/* Score Classes */
.score-excellent { color: #28a745; font-weight: 600; }
.score-good { color: #20c997; font-weight: 600; }
.score-fair { color: #ffc107; font-weight: 600; }
.score-poor { color: #dc3545; font-weight: 600; }
.score-unknown { color: #6c757d; }

/* Execution Modes Section */
.execution-modes-section .execution-modes {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.mode-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.mode-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.mode-value.enabled { color: #28a745; font-weight: 600; }
.mode-value.disabled { color: #dc3545; font-weight: 600; }

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.agent-mode-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.agent-mode-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
}

.agent-mode-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mode-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.mode-detail .enabled { color: #28a745; font-weight: 600; }
.mode-detail .disabled { color: #dc3545; font-weight: 600; }

.comparison-table {
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.comparison-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-row:first-child {
    background: #f8f9fa;
    font-weight: 600;
}

.comparison-label {
    font-weight: 600;
    color: #495057;
}

.comparison-requested.enabled,
.comparison-actual.enabled { color: #28a745; font-weight: 600; }

.comparison-requested.disabled,
.comparison-actual.disabled { color: #dc3545; font-weight: 600; }

.comparison-match.match { color: #28a745; }
.comparison-match.mismatch { color: #ffc107; }

/* Timeline Visualization Styles */
.timeline-visualization {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.timeline-controls {
    display: flex;
    gap: 10px;
}

.timeline-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.timeline-btn:hover {
    background: #138496;
}

.timeline-chart {
    position: relative;
    height: 200px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow-x: auto;
    overflow-y: hidden;
}

.timeline-track {
    position: relative;
    height: 100%;
    min-width: 800px;
    padding: 20px;
}

.timeline-event-marker {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeline-event-marker:hover {
    transform: translateY(-50%) scale(1.3);
    z-index: 10;
}

.timeline-event-marker.success {
    background: #28a745;
}

.timeline-event-marker.error {
    background: #dc3545;
}

.timeline-event-marker.warning {
    background: #ffc107;
}

.timeline-event-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.timeline-event-marker:hover .timeline-event-label {
    opacity: 1;
}

.timeline-axis {
    position: absolute;
    bottom: 10px;
    left: 20px;
    right: 20px;
    height: 2px;
    background: #dee2e6;
}

.timeline-axis-label {
    position: absolute;
    bottom: -20px;
    font-size: 10px;
    color: #6c757d;
    transform: translateX(-50%);
}

/* Error Analysis Styles */
.error-analysis {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.error-analysis h5 {
    color: #c53030;
    margin: 0 0 10px 0;
}

.error-item {
    color: #742a2a;
    background: #fed7d7;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 3px solid #e53e3e;
}

/* Tool Call Analysis Styles */
.tool-call-analysis {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.tool-call-analysis h5 {
    color: #2d3748;
    margin: 0 0 10px 0;
}

.tool-call-json {
    background: #1a202c;
    color: #e2e8f0;
    padding: 12px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
}

/* Technical Mode Styles */
.technical-mode .workflow-event {
    border-left: 4px solid #6c757d;
}

.technical-mode .event-header {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
}

.technical-mode .event-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.technical-details-panel {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 10px 0;
    overflow: hidden;
}

.technical-details-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.technical-details-content {
    padding: 15px;
    display: none;
}

.technical-details-content.expanded {
    display: block;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.performance-metric {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.metric-value {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    display: block;
}

.metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

/* Raw Data Section */
.raw-data-section {
    margin-top: 30px;
}

.raw-data-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #495057;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

.raw-data-content {
    position: relative;
}

.raw-data-tab {
    display: none;
}

.raw-data-tab.active {
    display: block;
}

.raw-data-tab .json-viewer {
    max-height: 500px;
    overflow-y: auto;
}

/* Wheel Generation Specific Styles */
.wheel-generation-flow {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.agent-flow-visualization {
    margin-top: 15px;
}

.agent-flow-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.agent-flow-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    min-width: 150px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.agent-flow-step:hover {
    border-color: #667eea;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.agent-flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.agent-sequence {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.agent-name {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.agent-status {
    font-size: 16px;
}

.agent-duration {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.agent-flow-stage {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.agent-flow-arrow {
    font-size: 20px;
    color: #6c757d;
    margin: 0 5px;
}

.agent-flow-arrow:last-child {
    display: none;
}

/* Wheel Items Analysis */
.wheel-items-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.wheel-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.wheel-item-analysis {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.wheel-item-analysis:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.wheel-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.wheel-item-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.domain-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.percentage-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.wheel-item-details {
    margin-bottom: 15px;
}

.wheel-item-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.show-tailoring-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-tailoring-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Agent Timeline Styles */
.agent-timeline-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.agent-timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 15px;
}

.agent-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-event {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.timeline-event:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.timeline-event.success {
    border-left: 4px solid #28a745;
}

.timeline-event.error {
    border-left: 4px solid #dc3545;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.timeline-event.success .timeline-marker {
    background: #28a745;
}

.timeline-event.error .timeline-marker {
    background: #dc3545;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.timeline-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.timeline-time {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.timeline-duration {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.timeline-stage {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

.show-details-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-details-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Tool Calls Analysis Styles */
.tool-calls-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.tool-calls-summary {
    display: flex;
    gap: 30px;
    margin: 15px 0 25px 0;
    justify-content: center;
}

.summary-stat {
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-width: 120px;
}

.summary-stat .stat-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.summary-stat .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-calls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.tool-analysis-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.tool-analysis-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.tool-count {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.tool-percentage {
    background: #e9ecef;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.show-tool-calls-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-tool-calls-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* Section descriptions */
.section-description {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 15px;
    font-size: 14px;
}

/* No data states */
.no-data, .no-wheel-data, .no-timeline-data, .no-tool-data {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.no-data-with-mock {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
}

.no-data-message {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ffeaa7;
}

.no-data-message p {
    margin: 8px 0;
    color: #856404;
}

.no-data-message ul {
    text-align: left;
    margin: 10px 0;
    padding-left: 20px;
}

.no-data-message li {
    color: #6c757d;
    margin: 5px 0;
}

.mock-agent-flow .agent-flow-step {
    opacity: 0.7;
    border-style: dashed;
}

.mock-agent-flow .agent-flow-step:hover {
    opacity: 1;
    border-style: solid;
}

.mock-step .agent-duration {
    background: #ffeaa7;
    color: #856404;
}

.mock-flow::before {
    content: '⚠️ Mock Data - Typical Workflow Structure';
    display: block;
    text-align: center;
    font-weight: 600;
    color: #856404;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ffeaa7;
}
</style>

<script>
// Helper functions defined first
function renderAgentFlowVisualization(agentCommunications, actualModes) {
    const agents = agentCommunications.agents || [];

    // If no agent communications, create a mock flow based on typical wheel generation workflow
    if (agents.length === 0) {
        return `
            <div class="no-data-with-mock">
                <div class="no-data-message">
                    <p>⚠️ No detailed agent communication data available in this benchmark run.</p>
                    <p>Showing typical wheel generation workflow structure:</p>
                </div>
                <div class="mock-agent-flow">
                    ${renderMockAgentFlow()}
                </div>
            </div>
        `;
    }

    const agentFlow = agents.map((comm, index) => {
        const statusIcon = comm.success !== false ? '✅' : '❌';
        const duration = comm.duration_ms ? `${comm.duration_ms.toFixed(2)}ms` : 'N/A';

        return `
            <div class="agent-flow-step" data-agent="${comm.agent}" data-index="${index}">
                <div class="agent-flow-header">
                    <span class="agent-sequence">${index + 1}</span>
                    <span class="agent-name">${comm.agent}</span>
                    <span class="agent-status">${statusIcon}</span>
                    <span class="agent-duration">${duration}</span>
                </div>
                <div class="agent-flow-stage">${comm.stage || 'Unknown Stage'}</div>
                <div class="agent-flow-arrow">→</div>
            </div>
        `;
    }).join('');

    return `
        <div class="agent-flow-container">
            ${agentFlow}
        </div>
    `;
}

function renderWheelItemsAnalysis(wheel) {
    const activities = wheel.activities || [];
    const items = wheel.items || [];

    // If no activities, try to extract from items or create mock data
    if (activities.length === 0 && items.length === 0) {
        return `
            <div class="no-wheel-data">
                <div class="no-data-message">
                    <p>⚠️ No wheel activities data available in this benchmark run.</p>
                    <p>This could mean:</p>
                    <ul>
                        <li>The benchmark was run with mocked data</li>
                        <li>The wheel generation failed</li>
                        <li>The data structure has changed</li>
                    </ul>
                    <p>Check the raw results tab for more details.</p>
                </div>
            </div>
        `;
    }

    // If we have items but no activities, try to use items data
    const dataToUse = activities.length > 0 ? activities : items;

    const itemsHtml = dataToUse.map((activity, index) => {
        const item = items[index] || activity; // Use activity as fallback if no separate items
        const domain = activity.domain || item.domain || 'Unknown';
        const percentage = item.percentage || activity.percentage || (100 / dataToUse.length);

        return `
            <div class="wheel-item-analysis" data-activity-id="${activity.id}">
                <div class="wheel-item-header">
                    <h5>${activity.name || `Activity ${index + 1}`}</h5>
                    <span class="domain-badge ${domain.toLowerCase()}">${domain}</span>
                    <span class="percentage-badge">${percentage.toFixed(1)}%</span>
                </div>
                <div class="wheel-item-details">
                    <p><strong>Description:</strong> ${activity.description || 'No description available'}</p>
                    <p><strong>Instructions:</strong> ${activity.instructions || 'No instructions available'}</p>
                    ${activity.value_proposition ? `<p><strong>Value:</strong> ${activity.value_proposition}</p>` : ''}
                </div>
                <div class="tailoring-process">
                    <button class="show-tailoring-btn" onclick="showTailoringProcess('${activity.id}')">
                        🔍 Show Tailoring Process
                    </button>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section wheel-items-section">
            <h3>🎯 Wheel Items Deep Dive</h3>
            <p class="section-description">Click on any item to see how it was tailored from generic activities</p>
            <div class="wheel-items-grid">
                ${itemsHtml}
            </div>
        </div>
    `;
}

function renderAgentCommunicationTimeline(agentCommunications) {
    const agents = agentCommunications.agents || [];

    if (agents.length === 0) {
        return '<div class="no-timeline-data">No agent communication timeline available</div>';
    }

    const timelineHtml = agents.map((comm, index) => {
        const timestamp = comm.timestamp ? new Date(comm.timestamp).toLocaleTimeString() : 'N/A';
        const duration = comm.duration_ms ? `${comm.duration_ms.toFixed(2)}ms` : 'N/A';
        const statusClass = comm.success !== false ? 'success' : 'error';
        const statusIcon = comm.success !== false ? '✅' : '❌';

        return `
            <div class="timeline-event ${statusClass}" data-agent="${comm.agent}">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <h5>${comm.agent} ${statusIcon}</h5>
                        <span class="timeline-time">${timestamp}</span>
                        <span class="timeline-duration">${duration}</span>
                    </div>
                    <div class="timeline-stage">${comm.stage || 'Unknown Stage'}</div>
                    <div class="timeline-details">
                        <button class="show-details-btn" onclick="showAgentDetails('${comm.agent}', ${index})">
                            📋 Show Input/Output
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section agent-timeline-section">
            <h3>⏱️ Agent Communication Timeline</h3>
            <p class="section-description">Chronological view of agent execution with input/output details</p>
            <div class="agent-timeline">
                ${timelineHtml}
            </div>
        </div>
    `;
}

function renderToolCallAnalysis(operations, agentCommunications) {
    const toolCalls = operations.tool_breakdown || {};
    const totalCalls = operations.total_tool_calls || 0;

    if (totalCalls === 0) {
        return '<div class="no-tool-data">No tool call data available</div>';
    }

    const toolsHtml = Object.entries(toolCalls).map(([toolName, count]) => {
        const percentage = ((count / totalCalls) * 100).toFixed(1);

        return `
            <div class="tool-analysis-item">
                <div class="tool-header">
                    <h5>🔧 ${toolName}</h5>
                    <span class="tool-count">${count} calls</span>
                    <span class="tool-percentage">${percentage}%</span>
                </div>
                <div class="tool-details">
                    <button class="show-tool-calls-btn" onclick="showToolCallDetails('${toolName}')">
                        📋 Show All Calls
                    </button>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section tool-calls-section">
            <h3>🔧 Tool Call Analysis</h3>
            <p class="section-description">Detailed breakdown of tool usage during wheel generation</p>
            <div class="tool-calls-summary">
                <div class="summary-stat">
                    <span class="stat-value">${totalCalls}</span>
                    <span class="stat-label">Total Tool Calls</span>
                </div>
                <div class="summary-stat">
                    <span class="stat-value">${Object.keys(toolCalls).length}</span>
                    <span class="stat-label">Unique Tools</span>
                </div>
            </div>
            <div class="tool-calls-grid">
                ${toolsHtml}
            </div>
        </div>
    `;
}

// Wheel Generation Details Rendering Function - Make it globally accessible
window.renderWorkflowDetails = async function(workflowModalBody, data, runId) {
    // The modal body element is now passed as an argument
    if (!workflowModalBody) {
        console.error('Workflow modal body element not provided to renderWorkflowDetails');
        return;
    }

    // Store data globally for access by other functions (like technical analysis)
    window.currentWorkflowModalData = data;
    window.currentWorkflowRawResults = data.raw_results || {};

    // Extract data from the benchmark result structure with multiple fallback paths
    const rawResults = data.raw_results || {};
    const lastOutput = rawResults.last_output || {};
    const outputData = lastOutput.output_data || {};

    // Try multiple paths for wheel data
    let wheel = outputData.wheel || {};
    if (!wheel.activities && !wheel.items) {
        // Try alternative paths
        wheel = rawResults.wheel || data.wheel || {};
    }

    const ethicalValidation = outputData.ethical_validation || {};
    const strategyFramework = outputData.strategy_framework || {};
    const psychAssessment = outputData.psychological_assessment || {};
    const semanticQuality = rawResults.semantic_quality || {};
    const performance = rawResults.performance || {};
    const operations = rawResults.operations || {};
    const actualModes = lastOutput.actual_execution_modes || {};

    // Try multiple paths for agent communications
    let agentCommunications = data.agent_communications || {};
    if (!agentCommunications.agents) {
        // Try alternative paths
        agentCommunications = rawResults.agent_communications || lastOutput.agent_communications || {};
    }

    // Debug logging to understand the actual data structure
    console.log('🔍 Debug - Raw data structure:', {
        hasRawResults: !!data.raw_results,
        hasLastOutput: !!lastOutput,
        hasOutputData: !!outputData,
        hasWheel: !!wheel,
        wheelKeys: Object.keys(wheel),
        hasAgentComms: !!agentCommunications,
        agentCommsKeys: Object.keys(agentCommunications),
        agentCommsAgents: agentCommunications.agents?.length || 0
    });

    // Calculate metrics
    const meanDuration = performance.mean_duration_s ? (performance.mean_duration_s * 1000).toFixed(2) : 'N/A';
    const successRate = performance.success_rate !== null ? (performance.success_rate * 100).toFixed(1) + '%' : 'N/A';
    const semanticScore = semanticQuality.overall_score ? semanticQuality.overall_score.toFixed(2) : 'N/A';
    const totalTokens = (operations.total_input_tokens || 0) + (operations.total_output_tokens || 0);
    const agentCount = Object.keys(actualModes).length;
    const wheelItems = wheel.items?.length || 0;
    const trustPhase = psychAssessment.trust_phase?.phase || 'Unknown';
    const ethicalStatus = ethicalValidation.wheel_validation?.status || 'Unknown';

    workflowModalBody.innerHTML = `
        <div class="wheel-generation-evaluation-header">
            <div class="evaluation-type-badge wheel-generation">WHEEL GENERATION</div>
            <div class="evaluation-title">
                <h3>🎡 ${data.workflow_type || 'Wheel Generation'} Workflow Analysis</h3>
                <p class="scenario-info">${data.scenario || 'N/A'}</p>
                <div class="evaluation-focus">
                    🔍 Focus: Activity Tailoring Process & Agent Coordination
                    <span class="debugging-indicator enabled">
                        ✅ Debug Mode Active
                    </span>
                </div>
                <div class="evaluation-meta">
                    <span class="run-id">Run ID: ${runId}</span>
                    <span class="execution-date">${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</span>
                    <span class="workflow-version">Version: ${data.agent_version || 'N/A'}</span>
                </div>
            </div>
            <!-- Wheel Generation Agent Flow -->
            <div class="wheel-generation-flow">
                <h4>🔄 Agent Coordination Flow</h4>
                <div class="agent-flow-visualization">
                    ${renderAgentFlowVisualization(agentCommunications, actualModes)}
                </div>
            </div>

            <div class="wheel-generation-insights">
                <h4>🎡 Wheel Generation Analysis</h4>
                <div class="insight-cards">
                    <div class="insight-card wheel-output">
                        <div class="card-icon">🎡</div>
                        <div class="card-content">
                            <h5>Wheel Output</h5>
                            <p>Items Generated: ${wheelItems}</p>
                            <p>Trust Phase: ${trustPhase}</p>
                            <p>Ethics Status: ${ethicalStatus}</p>
                            <p class="optimization-tip">💡 ${wheelItems >= 6 && wheelItems <= 8 ? 'Optimal item count' : 'Review item count strategy'}</p>
                        </div>
                    </div>
                    <div class="insight-card tailoring-process">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <h5>Activity Tailoring</h5>
                            <p>Generic → Tailored: ${wheelItems}</p>
                            <p>Domains Covered: ${wheel.activities ? [...new Set(wheel.activities.map(a => a.domain))].length : 'N/A'}</p>
                            <p>Customization Level: High</p>
                            <p class="optimization-tip">💡 Click wheel items below to see tailoring process</p>
                        </div>
                    </div>
                    <div class="insight-card agent-performance">
                        <div class="card-icon">🤖</div>
                        <div class="card-content">
                            <h5>Agent Performance</h5>
                            <p>Agents Used: ${agentCount}</p>
                            <p>Duration: ${meanDuration}ms</p>
                            <p>Success Rate: ${successRate}</p>
                            <p class="optimization-tip">💡 ${performance.success_rate === 1 ? 'All agents executed successfully' : 'Check agent failures below'}</p>
                        </div>
                    </div>
                    <div class="insight-card tool-usage">
                        <div class="card-icon">🔧</div>
                        <div class="card-content">
                            <h5>Tool Usage</h5>
                            <p>Tool Calls: ${operations.total_tool_calls || 0}</p>
                            <p>Real Mode: ${lastOutput.real_llm_used ? '✅' : '❌'}</p>
                            <p>Tokens: ${totalTokens.toLocaleString()}</p>
                            <p class="optimization-tip">💡 ${operations.total_tool_calls > 10 ? 'High tool usage - check efficiency' : 'Efficient tool usage'}</p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Performance Metrics -->
                <div class="performance-breakdown">
                    <h4>📊 Detailed Performance Breakdown</h4>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">⏱️</span>
                                <span class="metric-title">Execution Timing</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Mean Duration:</span>
                                    <span class="metric-value">${meanDuration}ms</span>
                                </div>
                                <div class="metric-row">
                                    <span>Success Rate:</span>
                                    <span class="metric-value ${performance.success_rate === 1 ? 'success' : 'warning'}">${successRate}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Agent Count:</span>
                                    <span class="metric-value">${agentCount}</span>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">🧠</span>
                                <span class="metric-title">LLM Usage</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Total Tokens:</span>
                                    <span class="metric-value">${totalTokens.toLocaleString()}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Input/Output:</span>
                                    <span class="metric-value">${(operations.total_input_tokens || 0).toLocaleString()}/${(operations.total_output_tokens || 0).toLocaleString()}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Real LLM:</span>
                                    <span class="metric-value ${lastOutput.real_llm_used ? 'success' : 'info'}">${lastOutput.real_llm_used ? 'Yes' : 'Mock'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">📈</span>
                                <span class="metric-title">Quality Metrics</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Semantic Score:</span>
                                    <span class="metric-value ${semanticScore !== 'N/A' && parseFloat(semanticScore) > 0.7 ? 'success' : semanticScore !== 'N/A' && parseFloat(semanticScore) > 0.5 ? 'warning' : 'error'}">${semanticScore}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Wheel Items:</span>
                                    <span class="metric-value">${wheelItems}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Ethics Status:</span>
                                    <span class="metric-value ${ethicalStatus === 'Approved' ? 'success' : 'warning'}">${ethicalStatus}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-summary-cards">
            <div class="workflow-summary-card">
                <h4>⏱️ Execution Time</h4>
                <div class="value">${meanDuration}</div>
                <div class="label">milliseconds</div>
            </div>
            <div class="workflow-summary-card">
                <h4>🎯 Semantic Quality</h4>
                <div class="value">${semanticScore}</div>
                <div class="label">overall score</div>
            </div>
            <div class="workflow-summary-card">
                <h4>🧠 Token Usage</h4>
                <div class="value">${totalTokens.toLocaleString()}</div>
                <div class="label">total tokens</div>
            </div>
            <div class="workflow-summary-card">
                <h4>✅ Success Rate</h4>
                <div class="value">${successRate}</div>
                <div class="label">completion rate</div>
            </div>
        </div>

        <!-- Wheel Items Deep Dive -->
        ${wheelItems > 0 ? renderWheelItemsAnalysis(wheel) : ''}

        <!-- Agent Communication Timeline -->
        ${renderAgentCommunicationTimeline(agentCommunications)}

        <!-- Tool Call Analysis -->
        ${renderToolCallAnalysis(operations, agentCommunications)}

        <!-- Wheel Generation Output -->
        ${wheelItems > 0 ? `
        <div class="modal-section wheel-output-section">
            <h3>🎡 Generated Wheel Output</h3>
            <div class="wheel-summary">
                <div class="wheel-meta">
                    <div class="wheel-info">
                        <h4>${wheel.metadata?.name || 'Activity Wheel'}</h4>
                        <p>Trust Phase: <span class="trust-phase ${trustPhase.toLowerCase()}">${trustPhase}</span></p>
                        <p>User ID: <code>${wheel.user_id || 'N/A'}</code></p>
                        <p>Strategy: <code>${wheel.metadata?.strategy_id || 'N/A'}</code></p>
                    </div>
                    <div class="wheel-stats">
                        <div class="stat-item">
                            <span class="stat-value">${wheelItems}</span>
                            <span class="stat-label">Activities</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${new Set(wheel.items?.map(item => item.domain) || []).size}</span>
                            <span class="stat-label">Domains</span>
                        </div>
                    </div>
                </div>
                <div class="wheel-activities">
                    ${renderWheelActivities(wheel)}
                </div>
            </div>
        </div>
        ` : ''}

        <!-- Psychological Assessment -->
        ${Object.keys(psychAssessment).length > 0 ? `
        <div class="modal-section psychological-section">
            <h3>🧠 Psychological Assessment</h3>
            <div class="psych-analysis">
                <div class="trust-analysis">
                    <h4>🤝 Trust Phase Analysis</h4>
                    <div class="trust-metrics">
                        <div class="trust-metric">
                            <span class="metric-label">Current Phase:</span>
                            <span class="metric-value trust-phase ${trustPhase.toLowerCase()}">${trustPhase}</span>
                        </div>
                        <div class="trust-metric">
                            <span class="metric-label">Trust Level:</span>
                            <span class="metric-value">${psychAssessment.trust_phase?.trust_level || 'N/A'}</span>
                        </div>
                        <div class="trust-metric">
                            <span class="metric-label">Confidence:</span>
                            <span class="metric-value">${psychAssessment.trust_phase?.confidence ? (psychAssessment.trust_phase.confidence * 100).toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                    </div>
                </div>
                ${renderPsychologicalDetails(psychAssessment)}
            </div>
        </div>
        ` : ''}

        <!-- Ethical Validation -->
        ${Object.keys(ethicalValidation).length > 0 ? `
        <div class="modal-section ethical-section">
            <h3>⚖️ Ethical Validation</h3>
            <div class="ethical-analysis">
                <div class="validation-status">
                    <div class="status-header">
                        <h4>🛡️ Validation Status</h4>
                        <span class="status-badge ${ethicalStatus.toLowerCase()}">${ethicalStatus}</span>
                    </div>
                    <div class="validation-metrics">
                        <div class="validation-metric">
                            <span class="metric-label">Confidence:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.confidence ? (ethicalValidation.wheel_validation.confidence * 100).toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                        <div class="validation-metric">
                            <span class="metric-label">Domain Balance:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.domain_balance || 'N/A'}</span>
                        </div>
                        <div class="validation-metric">
                            <span class="metric-label">Challenge Calibration:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.challenge_calibration || 'N/A'}</span>
                        </div>
                    </div>
                </div>
                ${renderEthicalDetails(ethicalValidation)}
            </div>
        </div>
        ` : ''}

        <!-- Strategy Framework -->
        ${Object.keys(strategyFramework).length > 0 ? `
        <div class="modal-section strategy-section">
            <h3>🎯 Strategy Framework</h3>
            <div class="strategy-analysis">
                ${renderStrategyDetails(strategyFramework)}
            </div>
        </div>
        ` : ''}

        <!-- Semantic Quality Evaluation -->
        ${Object.keys(semanticQuality).length > 0 ? `
        <div class="modal-section semantic-section">
            <h3>📊 Semantic Quality Evaluation</h3>
            <div class="semantic-analysis">
                ${renderSemanticQualityDetails(semanticQuality)}
            </div>
        </div>
        ` : ''}

        <!-- Execution Mode Analysis -->
        ${Object.keys(actualModes).length > 0 ? `
        <div class="modal-section execution-modes-section">
            <h3>🔧 Execution Mode Analysis</h3>
            <div class="execution-modes">
                ${renderExecutionModes(actualModes, lastOutput)}
            </div>
        </div>
        ` : ''}

        <!-- Enhanced Chronological Event Analysis -->
        ${data.agent_communications && data.agent_communications.enabled ? `
        <div class="workflow-event-tree">
            <div class="event-tree-header">
                <h3>🌳 Interactive Workflow Event Tree</h3>
                <div class="tree-controls">
                    <button class="tree-control-btn" onclick="expandAllEvents()">📖 Expand All</button>
                    <button class="tree-control-btn secondary" onclick="collapseAllEvents()">📕 Collapse All</button>
                    <button class="tree-control-btn secondary" onclick="showOnlyIssues()">⚠️ Issues Only</button>
                    <button class="tree-control-btn secondary" onclick="showAllEvents()">👁️ Show All</button>
                    <button class="tree-control-btn secondary" onclick="toggleTimelineView()">📊 Timeline View</button>
                    <button class="tree-control-btn secondary" onclick="toggleTechnicalMode()">🔧 Technical Mode</button>
                </div>
            </div>

            <!-- Timeline Visualization -->
            <div id="timeline-visualization" class="timeline-visualization" style="display: none;">
                <div class="timeline-header">
                    <h4>📈 Execution Timeline</h4>
                    <div class="timeline-controls">
                        <button class="timeline-btn" onclick="zoomTimeline('in')">🔍 Zoom In</button>
                        <button class="timeline-btn" onclick="zoomTimeline('out')">🔍 Zoom Out</button>
                        <button class="timeline-btn" onclick="resetTimelineZoom()">🔄 Reset</button>
                    </div>
                </div>
                <div id="timeline-chart" class="timeline-chart">
                    <!-- Timeline chart will be rendered here -->
                </div>
            </div>

            <div class="event-tree-container">
                <div class="event-tree-line"></div>
                <div id="workflow-events-container">
                    <!-- Events will be rendered here -->
                </div>
            </div>
        </div>
        ` : ''}

        <!-- Raw Data Section -->
        <div class="modal-section raw-data-section">
            <h3>📄 Raw Benchmark Data</h3>
            <div class="raw-data-tabs">
                <button class="tab-btn active" onclick="showRawDataTab('performance')">Performance</button>
                <button class="tab-btn" onclick="showRawDataTab('operations')">Operations</button>
                <button class="tab-btn" onclick="showRawDataTab('full')">Full Results</button>
            </div>
            <div class="raw-data-content">
                <div id="raw-performance" class="raw-data-tab active">
                    <pre class="json-viewer">${JSON.stringify(performance, null, 2)}</pre>
                </div>
                <div id="raw-operations" class="raw-data-tab">
                    <pre class="json-viewer">${JSON.stringify(operations, null, 2)}</pre>
                </div>
                <div id="raw-full" class="raw-data-tab">
                    <pre class="json-viewer">${JSON.stringify(rawResults, null, 2)}</pre>
                </div>
            </div>
        </div>
    `;

    // Render the interactive event tree
    if (data.agent_communications && data.agent_communications.agents) {
        renderInteractiveEventTree(data.agent_communications.agents);
        renderTimelineVisualization(data.agent_communications.agents);
    }
}

// Helper Functions for Rendering Different Sections

function renderWheelActivities(wheel) {
    if (!wheel.items || wheel.items.length === 0) {
        return '<p>No activities generated</p>';
    }

    return `
        <div class="activities-grid">
            ${wheel.items.map((item, index) => `
                <div class="activity-card" style="border-left: 4px solid ${item.color}">
                    <div class="activity-header">
                        <h5>${item.title}</h5>
                        <span class="domain-badge ${item.domain}">${item.domain}</span>
                    </div>
                    <div class="activity-details">
                        <p><strong>Description:</strong> ${wheel.activities?.[index]?.description || 'N/A'}</p>
                        <div class="activity-meta">
                            <span>Duration: ${wheel.activities?.[index]?.duration || 'N/A'}min</span>
                            <span>Challenge: ${wheel.activities?.[index]?.challenge_level || 'N/A'}/100</span>
                            <span>Percentage: ${item.percentage?.toFixed(1) || 'N/A'}%</span>
                        </div>
                    </div>
                    <div class="value-proposition">
                        <h6>💡 Value Proposition</h6>
                        <p>${wheel.value_propositions?.[item.id]?.growth_value || 'No value proposition available'}</p>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function renderPsychologicalDetails(psychAssessment) {
    const currentState = psychAssessment.current_state || {};
    const traitAnalysis = psychAssessment.trait_analysis || {};
    const beliefAnalysis = psychAssessment.belief_analysis || {};

    return `
        <div class="psych-details">
            <div class="current-state">
                <h4>🌟 Current State</h4>
                <div class="state-metrics">
                    <div class="state-metric">
                        <span class="metric-label">Energy Level:</span>
                        <span class="metric-value">${currentState.energy_level || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Stress Level:</span>
                        <span class="metric-value">${currentState.stress_level || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Emotional Balance:</span>
                        <span class="metric-value">${currentState.emotional_balance || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Cognitive Load:</span>
                        <span class="metric-value">${currentState.cognitive_load || 'N/A'}</span>
                    </div>
                </div>
            </div>

            ${Object.keys(beliefAnalysis.core_beliefs || {}).length > 0 ? `
            <div class="belief-analysis">
                <h4>💭 Core Beliefs</h4>
                <div class="beliefs-grid">
                    ${Object.entries(beliefAnalysis.core_beliefs).map(([key, belief]) => `
                        <div class="belief-card">
                            <h5>${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                            <div class="belief-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill" style="width: ${(belief.strength * 100)}%"></div>
                                </div>
                                <span>${(belief.strength * 100).toFixed(0)}%</span>
                            </div>
                            <p>${belief.description}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${traitAnalysis.dominant_traits?.length > 0 ? `
            <div class="trait-analysis">
                <h4>🎭 Dominant Traits</h4>
                <div class="traits-list">
                    ${traitAnalysis.dominant_traits.map(trait => `
                        <span class="trait-badge">${trait}</span>
                    `).join('')}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderEthicalDetails(ethicalValidation) {
    const principles = ethicalValidation.ethical_rationales?.ethical_principles || {};
    const activityValidations = ethicalValidation.activity_validations || [];
    const safetyConsiderations = ethicalValidation.safety_considerations || {};

    return `
        <div class="ethical-details">
            ${Object.keys(principles).length > 0 ? `
            <div class="ethical-principles">
                <h4>📜 Ethical Principles</h4>
                <div class="principles-grid">
                    ${Object.entries(principles).map(([principle, description]) => `
                        <div class="principle-card">
                            <h5>${principle.charAt(0).toUpperCase() + principle.slice(1)}</h5>
                            <p>${description}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${activityValidations.length > 0 ? `
            <div class="activity-validations">
                <h4>✅ Activity Validations</h4>
                <div class="validations-summary">
                    <div class="validation-stats">
                        <div class="stat">
                            <span class="stat-value">${activityValidations.filter(v => v.status === 'Approved').length}</span>
                            <span class="stat-label">Approved</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${activityValidations.filter(v => v.concerns?.length > 0).length}</span>
                            <span class="stat-label">With Concerns</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${(activityValidations.reduce((sum, v) => sum + (v.confidence || 0), 0) / activityValidations.length * 100).toFixed(0)}%</span>
                            <span class="stat-label">Avg Confidence</span>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}

            ${Object.keys(safetyConsiderations).length > 0 ? `
            <div class="safety-considerations">
                <h4>🛡️ Safety Considerations</h4>
                <div class="safety-details">
                    ${safetyConsiderations.vulnerability_areas?.length > 0 ? `
                    <div class="vulnerability-areas">
                        <h5>⚠️ Vulnerability Areas</h5>
                        <div class="vulnerability-list">
                            ${safetyConsiderations.vulnerability_areas.map(area => `
                                <span class="vulnerability-badge">${area}</span>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}

                    ${safetyConsiderations.challenge_boundaries ? `
                    <div class="challenge-boundaries">
                        <h5>🎯 Challenge Boundaries</h5>
                        <div class="boundaries-grid">
                            <div class="boundary-item">
                                <span class="boundary-label">Max Difficulty:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.max_difficulty_level || 'N/A'}</span>
                            </div>
                            <div class="boundary-item">
                                <span class="boundary-label">Max Duration:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.max_duration_minutes || 'N/A'} min</span>
                            </div>
                            <div class="boundary-item">
                                <span class="boundary-label">Exit Strategy:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.exit_strategy_required ? '✅' : '❌'}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderStrategyDetails(strategyFramework) {
    const gapAnalysis = strategyFramework.gap_analysis || {};
    const growthAlignment = strategyFramework.growth_alignment || {};
    const domainDistribution = strategyFramework.domain_distribution || {};
    const selectionCriteria = strategyFramework.selection_criteria || {};

    return `
        <div class="strategy-details">
            ${Object.keys(gapAnalysis).length > 0 ? `
            <div class="gap-analysis">
                <h4>📊 Gap Analysis</h4>
                <div class="gap-metrics">
                    <div class="gap-metric">
                        <span class="metric-label">Primary Focus:</span>
                        <span class="metric-value">${gapAnalysis.overall_assessment?.primary_focus || 'N/A'}</span>
                    </div>
                    <div class="gap-metric">
                        <span class="metric-label">Average Challenge:</span>
                        <span class="metric-value">${gapAnalysis.overall_assessment?.average_challenge || 'N/A'}</span>
                    </div>
                    <div class="gap-metric">
                        <span class="metric-label">Trust Phase Impact:</span>
                        <span class="metric-value">${gapAnalysis.trust_phase_impact || 'N/A'}</span>
                    </div>
                </div>
            </div>
            ` : ''}

            ${domainDistribution.domains ? `
            <div class="domain-distribution">
                <h4>🎯 Domain Distribution</h4>
                <div class="domains-grid">
                    ${Object.entries(domainDistribution.domains).map(([key, domain]) => `
                        <div class="domain-card">
                            <h5>${domain.name}</h5>
                            <div class="domain-percentage">
                                <div class="percentage-bar">
                                    <div class="percentage-fill" style="width: ${domain.percentage}%"></div>
                                </div>
                                <span>${domain.percentage?.toFixed(1)}%</span>
                            </div>
                            <p class="domain-reason">${domain.reason}</p>
                        </div>
                    `).join('')}
                </div>
                <div class="distribution-summary">
                    <p><strong>Primary Domain:</strong> ${domainDistribution.summary?.primary_domain || 'N/A'}</p>
                    <p><strong>Rationale:</strong> ${domainDistribution.summary?.distribution_rationale || 'N/A'}</p>
                </div>
            </div>
            ` : ''}

            ${Object.keys(selectionCriteria).length > 0 ? `
            <div class="selection-criteria">
                <h4>⚙️ Selection Criteria</h4>
                <div class="criteria-sections">
                    ${selectionCriteria.time_criteria ? `
                    <div class="criteria-section">
                        <h5>⏰ Time Criteria</h5>
                        <div class="criteria-items">
                            <div class="criteria-item">
                                <span>Duration Range:</span>
                                <span>${selectionCriteria.time_criteria.min_duration}-${selectionCriteria.time_criteria.max_duration} min</span>
                            </div>
                            <div class="criteria-item">
                                <span>Preferred Time:</span>
                                <span>${selectionCriteria.time_criteria.preferred_time_of_day}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${selectionCriteria.content_criteria ? `
                    <div class="criteria-section">
                        <h5>📝 Content Criteria</h5>
                        <div class="criteria-items">
                            <div class="criteria-item">
                                <span>Guidance Level:</span>
                                <span>${selectionCriteria.content_criteria.guidance_level}</span>
                            </div>
                            <div class="criteria-item">
                                <span>Complexity:</span>
                                <span>${selectionCriteria.content_criteria.complexity_level}</span>
                            </div>
                            <div class="criteria-item">
                                <span>Abstraction:</span>
                                <span>${selectionCriteria.content_criteria.abstraction_level}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderSemanticQualityDetails(semanticQuality) {
    const evaluations = semanticQuality.evaluations || {};
    const meta = evaluations._meta || {};
    const overallScore = semanticQuality.overall_score;

    return `
        <div class="semantic-details">
            <div class="semantic-overview">
                <h4>📊 Quality Overview</h4>
                <div class="quality-metrics">
                    <div class="quality-metric">
                        <span class="metric-label">Overall Score:</span>
                        <span class="metric-value score-${getScoreClass(overallScore)}">${overallScore?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div class="quality-metric">
                        <span class="metric-label">Primary Model:</span>
                        <span class="metric-value">${meta.primary_model || 'N/A'}</span>
                    </div>
                    <div class="quality-metric">
                        <span class="metric-label">Trust Phase:</span>
                        <span class="metric-value">${meta.phase || 'N/A'}</span>
                    </div>
                </div>
            </div>

            ${meta.criteria_dimensions?.length > 0 ? `
            <div class="evaluation-dimensions">
                <h4>📏 Evaluation Dimensions</h4>
                <div class="dimensions-list">
                    ${meta.criteria_dimensions.map(dimension => `
                        <span class="dimension-badge">${dimension}</span>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${Object.keys(evaluations).filter(key => key !== '_meta').length > 0 ? `
            <div class="model-evaluations">
                <h4>🤖 Model Evaluations</h4>
                ${Object.entries(evaluations).filter(([key]) => key !== '_meta').map(([model, evaluation]) => `
                    <div class="model-evaluation">
                        <h5>${model}</h5>
                        <div class="model-score">
                            <span class="score-label">Overall Score:</span>
                            <span class="score-value score-${getScoreClass(evaluation.overall_score)}">${evaluation.overall_score?.toFixed(2) || 'N/A'}</span>
                        </div>

                        ${evaluation.dimensions ? `
                        <div class="dimension-scores">
                            ${Object.entries(evaluation.dimensions).map(([dimension, data]) => `
                                <div class="dimension-score">
                                    <div class="dimension-header">
                                        <span class="dimension-name">${dimension}</span>
                                        <span class="dimension-value score-${getScoreClass(data.score)}">${data.score?.toFixed(2) || 'N/A'}</span>
                                    </div>
                                    <p class="dimension-reasoning">${data.reasoning || 'No reasoning provided'}</p>
                                </div>
                            `).join('')}
                        </div>
                        ` : ''}

                        ${evaluation.tone_analysis ? `
                        <div class="tone-analysis">
                            <h6>🎭 Tone Analysis</h6>
                            <div class="tone-score">
                                <span>Score: ${evaluation.tone_analysis.score?.toFixed(2) || 'N/A'}</span>
                            </div>
                            <p>${evaluation.tone_analysis.reasoning || 'No tone analysis available'}</p>
                        </div>
                        ` : ''}

                        <div class="overall-reasoning">
                            <h6>💭 Overall Assessment</h6>
                            <p>${evaluation.overall_reasoning || 'No overall reasoning provided'}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : ''}
        </div>
    `;
}

function renderExecutionModes(actualModes, lastOutput) {
    const requestedMode = lastOutput.requested_execution_mode || {};

    return `
        <div class="execution-modes-details">
            <div class="modes-overview">
                <h4>🔧 Execution Mode Summary</h4>
                <div class="mode-summary">
                    <div class="mode-item">
                        <span class="mode-label">Real LLM:</span>
                        <span class="mode-value ${lastOutput.real_llm_used ? 'enabled' : 'disabled'}">${lastOutput.real_llm_used ? '✅' : '❌'}</span>
                    </div>
                    <div class="mode-item">
                        <span class="mode-label">Real Tools:</span>
                        <span class="mode-value ${lastOutput.real_tools_used ? 'enabled' : 'disabled'}">${lastOutput.real_tools_used ? '✅' : '❌'}</span>
                    </div>
                    <div class="mode-item">
                        <span class="mode-label">Real DB:</span>
                        <span class="mode-value ${lastOutput.real_db_used ? 'enabled' : 'disabled'}">${lastOutput.real_db_used ? '✅' : '❌'}</span>
                    </div>
                </div>
            </div>

            <div class="agent-modes">
                <h4>🤖 Agent-Specific Modes</h4>
                <div class="agents-grid">
                    ${Object.entries(actualModes).map(([agent, modes]) => `
                        <div class="agent-mode-card">
                            <h5>${agent}</h5>
                            <div class="agent-mode-details">
                                <div class="mode-detail">
                                    <span>LLM:</span>
                                    <span class="${modes.real_llm ? 'enabled' : 'disabled'}">${modes.real_llm ? '✅' : '❌'}</span>
                                </div>
                                <div class="mode-detail">
                                    <span>Tools:</span>
                                    <span class="${modes.real_tools ? 'enabled' : 'disabled'}">${modes.real_tools ? '✅' : '❌'}</span>
                                </div>
                                <div class="mode-detail">
                                    <span>DB:</span>
                                    <span class="${modes.real_db ? 'enabled' : 'disabled'}">${modes.real_db ? '✅' : '❌'}</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            ${Object.keys(requestedMode).length > 0 ? `
            <div class="requested-vs-actual">
                <h4>🎯 Requested vs Actual</h4>
                <div class="comparison-table">
                    <div class="comparison-row">
                        <span class="comparison-label">LLM</span>
                        <span class="comparison-requested ${requestedMode.use_real_llm ? 'enabled' : 'disabled'}">${requestedMode.use_real_llm ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_llm_used ? 'enabled' : 'disabled'}">${lastOutput.real_llm_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_llm === lastOutput.real_llm_used ? 'match' : 'mismatch'}">${requestedMode.use_real_llm === lastOutput.real_llm_used ? '✅' : '⚠️'}</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Tools</span>
                        <span class="comparison-requested ${requestedMode.use_real_tools ? 'enabled' : 'disabled'}">${requestedMode.use_real_tools ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_tools_used ? 'enabled' : 'disabled'}">${lastOutput.real_tools_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_tools === lastOutput.real_tools_used ? 'match' : 'mismatch'}">${requestedMode.use_real_tools === lastOutput.real_tools_used ? '✅' : '⚠️'}</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Database</span>
                        <span class="comparison-requested ${requestedMode.use_real_db ? 'enabled' : 'disabled'}">${requestedMode.use_real_db ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_db_used ? 'enabled' : 'disabled'}">${lastOutput.real_db_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_db === lastOutput.real_db_used ? 'match' : 'mismatch'}">${requestedMode.use_real_db === lastOutput.real_db_used ? '✅' : '⚠️'}</span>
                    </div>
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function getScoreClass(score) {
    if (!score) return 'unknown';
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'fair';
    return 'poor';
}

// Raw Data Tab Functions
function showRawDataTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.raw-data-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    const selectedTab = document.getElementById(`raw-${tabName}`);
    const selectedBtn = event.target;

    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    if (selectedBtn) {
        selectedBtn.classList.add('active');
    }
}

// Interactive Event Tree Functions
function renderInteractiveEventTree(agents) {
    const container = document.getElementById('workflow-events-container');
    if (!container) return;

    // Sort agents by timestamp for chronological order
    const sortedAgents = [...agents].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    container.innerHTML = sortedAgents.map((agent, index) => {
        const issues = detectEventIssues(agent);
        const hasIssues = issues.length > 0;
        const issueClasses = issues.map(issue => issue.type).join(' ');

        return `
            <div class="workflow-event ${hasIssues ? 'has-issues' : ''} ${issueClasses}" data-event-index="${index}">
                <div class="event-header" onclick="toggleEventDetails(${index})">
                    <div class="event-info">
                        <div class="event-sequence">${index + 1}</div>
                        <div class="event-agent">🤖 ${agent.agent}</div>
                        <div class="event-stage">${agent.stage}</div>
                        <div class="event-timestamp">${formatTimestamp(agent.timestamp)}</div>
                    </div>
                    <div class="event-status">
                        <div class="status-indicator ${agent.success ? 'success' : 'error'}">
                            ${agent.success ? '✅' : '❌'}
                        </div>
                        <div class="event-duration">${agent.duration_ms ? agent.duration_ms.toFixed(0) + 'ms' : 'N/A'}</div>
                        <div class="event-toggle">▼</div>
                    </div>
                </div>
                <div class="event-details" id="event-details-${index}">
                    ${renderEventDetails(agent, issues)}
                </div>
            </div>
        `;
    }).join('');
}

function renderEventDetails(agent, issues) {
    const toolCalls = extractToolCalls(agent.input);
    const mockDetected = detectMockUsage(agent);
    const fallbackDetected = detectFallbackUsage(agent);

    return `
        <div class="event-details-grid">
            <div class="detail-panel">
                <div class="detail-panel-header">
                    <span>📥</span> Input Data
                </div>
                <div class="detail-panel-content">
                    ${renderInputSummary(agent.input)}
                    ${renderEnhancedToolCalls(agent)}
                    ${renderLLMCallInfo(agent)}
                </div>
            </div>

            <div class="detail-panel">
                <div class="detail-panel-header">
                    <span>📤</span> Output Data
                </div>
                <div class="detail-panel-content">
                    ${renderOutputSummary(agent.output)}
                </div>
            </div>
        </div>

        ${issues.length > 0 ? `
            <div class="issue-indicators">
                ${issues.map(issue => `
                    <div class="issue-badge ${issue.type}">
                        <span>${issue.icon}</span>
                        <span>${issue.message}</span>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        <div class="json-data-container">
            <div class="json-data-header" onclick="toggleJsonData('input-${agent.timestamp}')">
                <span>📋 Full Input Data</span>
                <span class="toggle-icon">▶</span>
            </div>
            <div class="json-data-content" id="input-${agent.timestamp}">
                <pre>${JSON.stringify(agent.input || {}, null, 2)}</pre>
            </div>
        </div>

        <div class="json-data-container">
            <div class="json-data-header" onclick="toggleJsonData('output-${agent.timestamp}')">
                <span>📋 Full Output Data</span>
                <span class="toggle-icon">▶</span>
            </div>
            <div class="json-data-content" id="output-${agent.timestamp}">
                <pre>${JSON.stringify(agent.output || {}, null, 2)}</pre>
            </div>
        </div>

        <div class="event-metadata">
            <h5>⚙️ Execution Metadata</h5>
            <div class="metadata-grid">
                <div class="metadata-item">
                    <span class="metadata-label">Agent:</span>
                    <span class="metadata-value">${agent.agent}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Stage:</span>
                    <span class="metadata-value">${agent.stage}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Duration:</span>
                    <span class="metadata-value">${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Success:</span>
                    <span class="metadata-value ${agent.success ? 'success' : 'failure'}">${agent.success ? 'Yes' : 'No'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Timestamp:</span>
                    <span class="metadata-value">${agent.timestamp}</span>
                </div>
                ${agent.error ? `
                <div class="metadata-item error">
                    <span class="metadata-label">Error:</span>
                    <span class="metadata-value">${agent.error}</span>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

function detectEventIssues(agent) {
    const issues = [];

    // Check for mocking
    if (detectMockUsage(agent)) {
        issues.push({
            type: 'mock',
            icon: '🎭',
            message: 'Mock tools detected'
        });
    }

    // Check for fallbacks
    if (detectFallbackUsage(agent)) {
        issues.push({
            type: 'fallback',
            icon: '🔄',
            message: 'Fallback strategy used'
        });
    }

    // Check for errors
    if (!agent.success || agent.error) {
        issues.push({
            type: 'error',
            icon: '❌',
            message: agent.error || 'Execution failed'
        });
    }

    // Check for slow execution
    if (agent.duration_ms && agent.duration_ms > 5000) {
        issues.push({
            type: 'slow',
            icon: '⏱️',
            message: `Slow execution (${agent.duration_ms.toFixed(0)}ms)`
        });
    }

    return issues;
}

function detectMockUsage(agent) {
    const inputStr = JSON.stringify(agent.input || {});
    const outputStr = JSON.stringify(agent.output || {});
    return inputStr.toLowerCase().includes('mock') ||
           outputStr.toLowerCase().includes('mock') ||
           inputStr.toLowerCase().includes('test') ||
           outputStr.toLowerCase().includes('dummy');
}

function detectFallbackUsage(agent) {
    const outputStr = JSON.stringify(agent.output || {});
    return outputStr.toLowerCase().includes('fallback') ||
           outputStr.toLowerCase().includes('default') ||
           outputStr.toLowerCase().includes('backup');
}

function extractToolCalls(input) {
    const tools = [];
    if (!input || typeof input !== 'object') return tools;

    // Look for common tool call patterns
    if (input.tool_calls) {
        return input.tool_calls.map(call => ({
            name: call.function?.name || call.name || 'Unknown',
            paramCount: Object.keys(call.function?.arguments || call.arguments || {}).length
        }));
    }

    // Look for other tool patterns
    Object.keys(input).forEach(key => {
        if (key.includes('tool') || key.includes('function')) {
            tools.push({
                name: key,
                paramCount: typeof input[key] === 'object' ? Object.keys(input[key]).length : 1
            });
        }
    });

    return tools;
}

function extractDetailedToolCalls(input, output) {
    const toolCalls = [];
    if (!input || typeof input !== 'object') return toolCalls;

    // Look for tool calls in input
    if (input.tool_calls && Array.isArray(input.tool_calls)) {
        input.tool_calls.forEach((call, index) => {
            const toolCall = {
                name: call.function?.name || call.name || 'Unknown Tool',
                parameters: call.function?.arguments || call.arguments || call.parameters,
                is_mocked: detectMockInData(call),
                success: true, // Default to true unless we find error indicators
                duration: call.duration || null,
                result: null,
                error: null
            };

            // Try to find corresponding result in output
            if (output && output.tool_results && Array.isArray(output.tool_results)) {
                const result = output.tool_results[index];
                if (result) {
                    toolCall.result = result.content || result.result || result;
                    toolCall.success = !result.error && !result.is_error;
                    toolCall.error = result.error;
                }
            }

            toolCalls.push(toolCall);
        });
    }

    // Look for other tool patterns in input/output
    ['tools', 'functions', 'actions'].forEach(key => {
        if (input[key] && Array.isArray(input[key])) {
            input[key].forEach(tool => {
                toolCalls.push({
                    name: tool.name || tool.type || 'Unknown Tool',
                    parameters: tool.parameters || tool.args || tool.input,
                    is_mocked: detectMockInData(tool),
                    success: tool.success !== false,
                    duration: tool.duration || null,
                    result: tool.result || tool.output,
                    error: tool.error
                });
            });
        }
    });

    return toolCalls;
}

function extractLLMInfo(agent) {
    const llmInfo = {
        hasLLMCall: false,
        model: null,
        temperature: null,
        inputTokens: null,
        outputTokens: null,
        cost: null,
        prompt: null,
        response: null
    };

    // Check input for LLM call information
    if (agent.input) {
        // Look for model information
        llmInfo.model = agent.input.model || agent.input.llm_model || agent.input.engine;
        llmInfo.temperature = agent.input.temperature;
        llmInfo.prompt = agent.input.messages || agent.input.prompt || agent.input.text;

        // Look for token information
        llmInfo.inputTokens = agent.input.input_tokens || agent.input.prompt_tokens;

        if (llmInfo.model || llmInfo.prompt) {
            llmInfo.hasLLMCall = true;
        }
    }

    // Check output for LLM response information
    if (agent.output) {
        llmInfo.response = agent.output.content || agent.output.text || agent.output.message;
        llmInfo.outputTokens = agent.output.output_tokens || agent.output.completion_tokens;
        llmInfo.cost = agent.output.cost || agent.output.estimated_cost;

        // Look for usage information
        if (agent.output.usage) {
            llmInfo.inputTokens = llmInfo.inputTokens || agent.output.usage.prompt_tokens;
            llmInfo.outputTokens = llmInfo.outputTokens || agent.output.usage.completion_tokens;
        }

        if (llmInfo.response) {
            llmInfo.hasLLMCall = true;
        }
    }

    return llmInfo;
}

function detectMockInData(data) {
    if (!data) return false;
    const dataStr = JSON.stringify(data).toLowerCase();
    return dataStr.includes('mock') ||
           dataStr.includes('test') ||
           dataStr.includes('dummy') ||
           dataStr.includes('fake');
}

function getToolIcon(toolName) {
    const iconMap = {
        'web_search': '🔍',
        'file_read': '📖',
        'file_write': '✏️',
        'database': '🗄️',
        'api_call': '🌐',
        'email': '📧',
        'calendar': '📅',
        'calculator': '🧮',
        'default': '🛠️'
    };

    const lowerName = toolName.toLowerCase();
    for (const [key, icon] of Object.entries(iconMap)) {
        if (lowerName.includes(key)) return icon;
    }
    return iconMap.default;
}

function renderInputSummary(input) {
    if (!input || typeof input !== 'object') {
        return '<p>No input data available</p>';
    }

    const keys = Object.keys(input);
    const summary = keys.slice(0, 3).join(', ') + (keys.length > 3 ? '...' : '');

    return `
        <div class="data-summary">
            <span class="data-count">${keys.length} fields</span>
            <span class="data-keys">${summary}</span>
        </div>
    `;
}

function renderOutputSummary(output) {
    if (!output || typeof output !== 'object') {
        return '<p>No output data available</p>';
    }

    const keys = Object.keys(output);
    const summary = keys.slice(0, 3).join(', ') + (keys.length > 3 ? '...' : '');

    return `
        <div class="data-summary">
            <span class="data-count">${keys.length} fields</span>
            <span class="data-keys">${summary}</span>
        </div>
    `;
}

function renderEnhancedToolCalls(agent) {
    const toolCalls = extractDetailedToolCalls(agent.input, agent.output);

    if (toolCalls.length === 0) {
        return '<p style="color: #6c757d; font-style: italic; margin: 10px 0;">No tool calls detected</p>';
    }

    return `
        <div style="margin: 15px 0;">
            <h5 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center; gap: 8px;">
                🛠️ Tool Calls
                <span class="tool-calls-count">${toolCalls.length}</span>
            </h5>
            <div class="tool-calls-enhanced">
                ${toolCalls.map((call, index) => `
                    <div class="tool-call-enhanced">
                        <div class="tool-call-header">
                            <div class="tool-call-info">
                                <span class="tool-call-icon">${call.is_mocked ? '🎭' : getToolIcon(call.name)}</span>
                                <span class="tool-call-name">${call.name}</span>
                                <span class="tool-call-index">#${index + 1}</span>
                            </div>
                            <div class="tool-call-meta">
                                ${call.duration ? `<span class="tool-duration">${call.duration}ms</span>` : ''}
                                ${call.is_mocked ? '<span class="mock-badge">MOCKED</span>' : ''}
                                ${call.success === false ? '<span class="error-badge">FAILED</span>' : ''}
                            </div>
                        </div>
                        <div class="tool-call-details">
                            ${call.parameters ? `
                                <div class="json-data-container">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        📥 Input Parameters
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.parameters, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                            ${call.result ? `
                                <div class="json-data-container">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        📤 Output Result
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.result, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                            ${call.error ? `
                                <div class="json-data-container error">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        ❌ Error Details
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.error, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function renderLLMCallInfo(agent) {
    const llmInfo = extractLLMInfo(agent);

    if (!llmInfo.hasLLMCall) {
        return '';
    }

    return `
        <div style="margin: 15px 0;">
            <h5 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center; gap: 8px;">
                🧠 LLM Call Information
            </h5>
            <div class="llm-call-enhanced">
                <div class="llm-call-header">
                    <div class="llm-model-info">
                        <span class="llm-model-name">${llmInfo.model || 'Unknown Model'}</span>
                        ${llmInfo.temperature !== null ? `<span class="llm-temperature">T: ${llmInfo.temperature}</span>` : ''}
                    </div>
                    <div class="llm-tokens">
                        ${llmInfo.inputTokens ? `<span class="token-count">📥 ${llmInfo.inputTokens}</span>` : ''}
                        ${llmInfo.outputTokens ? `<span class="token-count">📤 ${llmInfo.outputTokens}</span>` : ''}
                        ${llmInfo.cost ? `<span class="llm-cost">💰 $${llmInfo.cost}</span>` : ''}
                    </div>
                </div>
                ${llmInfo.prompt ? `
                    <div class="json-data-container">
                        <div class="json-data-header" onclick="toggleJsonData(this)">
                            💬 Prompt/Messages
                            <span class="toggle-icon">▼</span>
                        </div>
                        <div class="json-data-content">
                            <pre>${JSON.stringify(llmInfo.prompt, null, 2)}</pre>
                        </div>
                    </div>
                ` : ''}
                ${llmInfo.response ? `
                    <div class="json-data-container">
                        <div class="json-data-header" onclick="toggleJsonData(this)">
                            🤖 LLM Response
                            <span class="toggle-icon">▼</span>
                        </div>
                        <div class="json-data-content">
                            <pre>${JSON.stringify(llmInfo.response, null, 2)}</pre>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// Event Tree Control Functions
function toggleEventDetails(index) {
    const header = document.querySelector(`[data-event-index="${index}"] .event-header`);
    const details = document.getElementById(`event-details-${index}`);
    const toggle = header.querySelector('.event-toggle');

    if (details.classList.contains('expanded')) {
        details.classList.remove('expanded');
        header.classList.remove('expanded');
        toggle.style.transform = 'rotate(0deg)';
    } else {
        details.classList.add('expanded');
        header.classList.add('expanded');
        toggle.style.transform = 'rotate(180deg)';
    }
}

function expandAllEvents() {
    document.querySelectorAll('.workflow-event').forEach((event, index) => {
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        const toggle = event.querySelector('.event-toggle');

        details.classList.add('expanded');
        header.classList.add('expanded');
        toggle.style.transform = 'rotate(180deg)';
    });
}

function collapseAllEvents() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        const toggle = event.querySelector('.event-toggle');

        details.classList.remove('expanded');
        header.classList.remove('expanded');
        toggle.style.transform = 'rotate(0deg)';
    });
}

function showOnlyIssues() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        if (event.classList.contains('has-issues')) {
            event.style.display = 'block';
        } else {
            event.style.display = 'none';
        }
    });
}

function showAllEvents() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        event.style.display = 'block';
    });
}

function toggleJsonData(headerElement) {
    // Handle both old ID-based calls and new element-based calls
    let content, header, toggle;

    if (typeof headerElement === 'string') {
        // Old ID-based approach
        content = document.getElementById(headerElement);
        header = content.previousElementSibling;
        toggle = header.querySelector('.toggle-icon');
    } else {
        // New element-based approach
        header = headerElement;
        content = header.nextElementSibling;
        toggle = header.querySelector('.toggle-icon');
    }

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        toggle.textContent = '▶';
    } else {
        content.classList.add('expanded');
        toggle.textContent = '▼';
    }
}

// Enhanced Timeline and Technical Mode Functions
let timelineZoomLevel = 1;
let technicalModeEnabled = false;

function renderTimelineVisualization(agents) {
    const timelineChart = document.getElementById('timeline-chart');
    if (!timelineChart || !agents || agents.length === 0) return;

    // Sort agents by timestamp
    const sortedAgents = [...agents].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Calculate timeline dimensions
    const startTime = new Date(sortedAgents[0].timestamp);
    const endTime = new Date(sortedAgents[sortedAgents.length - 1].timestamp);
    const totalDuration = endTime - startTime;

    // Create timeline track
    const track = document.createElement('div');
    track.className = 'timeline-track';

    // Add timeline axis
    const axis = document.createElement('div');
    axis.className = 'timeline-axis';
    track.appendChild(axis);

    // Add time labels
    const labelCount = 5;
    for (let i = 0; i <= labelCount; i++) {
        const labelTime = new Date(startTime.getTime() + (totalDuration * i / labelCount));
        const label = document.createElement('div');
        label.className = 'timeline-axis-label';
        label.style.left = `${(i / labelCount) * 100}%`;
        label.textContent = labelTime.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        track.appendChild(label);
    }

    // Add event markers
    sortedAgents.forEach((agent, index) => {
        const eventTime = new Date(agent.timestamp);
        const position = totalDuration > 0 ? ((eventTime - startTime) / totalDuration) * 100 : (index / sortedAgents.length) * 100;

        const marker = document.createElement('div');
        marker.className = `timeline-event-marker ${agent.success ? 'success' : 'error'}`;
        marker.style.left = `${position}%`;
        marker.onclick = () => scrollToEvent(index);

        const label = document.createElement('div');
        label.className = 'timeline-event-label';
        label.textContent = `${agent.agent} (${agent.duration_ms ? agent.duration_ms.toFixed(0) + 'ms' : 'N/A'})`;
        marker.appendChild(label);

        track.appendChild(marker);
    });

    timelineChart.innerHTML = '';
    timelineChart.appendChild(track);
}

function toggleTimelineView() {
    const timeline = document.getElementById('timeline-visualization');
    const button = event.target;

    if (timeline.style.display === 'none') {
        timeline.style.display = 'block';
        button.textContent = '📊 Hide Timeline';
    } else {
        timeline.style.display = 'none';
        button.textContent = '📊 Timeline View';
    }
}

function toggleTechnicalMode() {
    const container = document.querySelector('.workflow-event-tree');
    const button = event.target;

    technicalModeEnabled = !technicalModeEnabled;

    if (technicalModeEnabled) {
        container.classList.add('technical-mode');
        button.textContent = '🔧 Exit Technical';
        enhanceEventsWithTechnicalDetails();
    } else {
        container.classList.remove('technical-mode');
        button.textContent = '🔧 Technical Mode';
        removeEnhancedTechnicalDetails();
    }
}

function enhanceEventsWithTechnicalDetails() {
    document.querySelectorAll('.workflow-event').forEach((event, index) => {
        const details = event.querySelector('.event-details');
        if (!details) return;

        // Add technical details panel if not already present
        if (!details.querySelector('.technical-details-panel')) {
            const techPanel = document.createElement('div');
            techPanel.className = 'technical-details-panel';
            techPanel.innerHTML = `
                <div class="technical-details-header" onclick="toggleTechnicalDetails(this)">
                    <span>🔧 Technical Analysis</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="technical-details-content">
                    ${renderTechnicalAnalysis(index)}
                </div>
            `;
            details.appendChild(techPanel);
        }
    });
}

function removeEnhancedTechnicalDetails() {
    document.querySelectorAll('.technical-details-panel').forEach(panel => {
        panel.remove();
    });
}

function renderTechnicalAnalysis(eventIndex) {
    // Get actual agent data from the current modal data
    const modalData = window.currentWorkflowModalData;
    const agents = modalData?.agent_communications?.agents || [];
    const agent = agents[eventIndex];

    if (!agent) {
        return `
            <div class="technical-insights">
                <h5>🔍 Technical Analysis</h5>
                <p>No agent data available for analysis.</p>
            </div>
        `;
    }

    // Calculate metrics from actual data
    const executionTime = agent.duration_ms || 0;
    const tokenUsage = (agent.input_tokens || 0) + (agent.output_tokens || 0);
    const estimatedCost = ((agent.input_tokens || 0) * 0.000005) + ((agent.output_tokens || 0) * 0.000015);
    const toolCalls = agent.tool_calls || 0;
    const success = agent.success !== false;

    // Generate insights based on actual data
    const insights = [];
    if (executionTime > 0) {
        if (executionTime < 1000) {
            insights.push("⚡ Fast execution time");
        } else if (executionTime < 5000) {
            insights.push("✅ Normal execution time");
        } else {
            insights.push("⚠️ Slow execution time - consider optimization");
        }
    }

    if (tokenUsage > 0) {
        if (tokenUsage < 500) {
            insights.push("💚 Efficient token usage");
        } else if (tokenUsage < 1500) {
            insights.push("✅ Moderate token usage");
        } else {
            insights.push("⚠️ High token usage - review prompt efficiency");
        }
    }

    if (success) {
        insights.push("✅ Agent execution successful");
    } else {
        insights.push("❌ Agent execution failed");
    }

    if (agent.real_llm_used) {
        insights.push("🤖 Real LLM used");
    } else {
        insights.push("🎭 Mock LLM used");
    }

    if (agent.errors && agent.errors.length > 0) {
        insights.push(`⚠️ ${agent.errors.length} error(s) detected`);
    }

    return `
        <div class="performance-metrics">
            <div class="performance-metric">
                <span class="metric-value">${executionTime > 0 ? (executionTime / 1000).toFixed(2) + 's' : 'N/A'}</span>
                <span class="metric-label">Execution Time</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">${tokenUsage.toLocaleString()}</span>
                <span class="metric-label">Tokens Used</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">$${estimatedCost.toFixed(4)}</span>
                <span class="metric-label">Estimated Cost</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">${toolCalls}</span>
                <span class="metric-label">Tool Calls</span>
            </div>
        </div>
        <div class="technical-insights">
            <h5>🔍 Performance Insights</h5>
            <ul>
                ${insights.map(insight => `<li>${insight}</li>`).join('')}
            </ul>
        </div>
        ${agent.errors && agent.errors.length > 0 ? `
        <div class="error-analysis">
            <h5>❌ Error Analysis</h5>
            <ul>
                ${agent.errors.map(error => `<li class="error-item">${error}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        ${agent.tool_call_details ? `
        <div class="tool-call-analysis">
            <h5>🔧 Tool Call Details</h5>
            <pre class="tool-call-json">${JSON.stringify(agent.tool_call_details, null, 2)}</pre>
        </div>
        ` : ''}
    `;
}

function toggleTechnicalDetails(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.toggle-icon');

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.textContent = '▼';
    } else {
        content.classList.add('expanded');
        icon.textContent = '▲';
    }
}

function zoomTimeline(direction) {
    const track = document.querySelector('.timeline-track');
    if (!track) return;

    if (direction === 'in') {
        timelineZoomLevel *= 1.5;
    } else if (direction === 'out') {
        timelineZoomLevel /= 1.5;
    }

    timelineZoomLevel = Math.max(0.5, Math.min(5, timelineZoomLevel));
    track.style.minWidth = `${800 * timelineZoomLevel}px`;
}

function resetTimelineZoom() {
    timelineZoomLevel = 1;
    const track = document.querySelector('.timeline-track');
    if (track) {
        track.style.minWidth = '800px';
    }
}

function scrollToEvent(index) {
    const event = document.querySelector(`[data-event-index="${index}"]`);
    if (event) {
        event.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight the event briefly
        event.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.5)';
        setTimeout(() => {
            event.style.boxShadow = '';
        }, 2000);

        // Expand the event if it's not already expanded
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        if (!details.classList.contains('expanded')) {
            toggleEventDetails(index);
        }
    }
}

function renderMockAgentFlow() {
    const mockAgents = [
        { name: 'Orchestrator', stage: 'Initial Coordination', status: '✅' },
        { name: 'Resource & Capacity', stage: 'Resource Assessment', status: '✅' },
        { name: 'Engagement & Pattern', stage: 'Pattern Analysis', status: '✅' },
        { name: 'Psychological', stage: 'Psychological Assessment', status: '✅' },
        { name: 'Strategy', stage: 'Strategy Formulation', status: '✅' },
        { name: 'Wheel/Activity', stage: 'Activity Selection', status: '✅' },
        { name: 'Ethical', stage: 'Ethical Validation', status: '✅' },
        { name: 'Orchestrator', stage: 'Final Integration', status: '✅' }
    ];

    const agentFlow = mockAgents.map((agent, index) => {
        return `
            <div class="agent-flow-step mock-step" data-agent="${agent.name}" data-index="${index}">
                <div class="agent-flow-header">
                    <span class="agent-sequence">${index + 1}</span>
                    <span class="agent-name">${agent.name}</span>
                    <span class="agent-status">${agent.status}</span>
                    <span class="agent-duration">N/A</span>
                </div>
                <div class="agent-flow-stage">${agent.stage}</div>
                <div class="agent-flow-arrow">→</div>
            </div>
        `;
    }).join('');

    return `
        <div class="agent-flow-container mock-flow">
            ${agentFlow}
        </div>
    `;
}

// Interactive functions for detailed views
function showTailoringProcess(activityId) {
    // Get the current wheel data and agent communications
    const wheelData = window.currentWorkflowModalData?.raw_results?.last_output?.output_data?.wheel || {};
    const agentCommunications = window.currentWorkflowModalData?.agent_communications || {};

    // Show the dedicated crafting modal
    if (typeof window.showWheelItemCrafting === 'function') {
        window.showWheelItemCrafting(activityId, wheelData, agentCommunications);
    } else {
        console.error('showWheelItemCrafting function not available');
        alert(`Crafting sequence for activity ${activityId} - The detailed crafting modal is not available`);
    }
}

function showAgentDetails(agentName, index) {
    // This would show detailed input/output for the specific agent
    console.log('Showing agent details for:', agentName, 'at index:', index);
    const comm = window.currentWorkflowModalData?.agent_communications?.agents?.[index];
    if (comm) {
        const details = `
Agent: ${agentName}
Stage: ${comm.stage || 'Unknown'}
Duration: ${comm.duration_ms ? comm.duration_ms.toFixed(2) + 'ms' : 'N/A'}
Success: ${comm.success !== false ? 'Yes' : 'No'}

Input Data: ${JSON.stringify(comm.input_data || {}, null, 2)}

Output Data: ${JSON.stringify(comm.output_data || {}, null, 2)}
        `;
        alert(details);
    }
}

function showToolCallDetails(toolName) {
    // This would show all calls for the specific tool
    console.log('Showing tool call details for:', toolName);
    alert(`Tool call details for ${toolName} - This would show all individual calls with parameters and responses`);
}
</script>
