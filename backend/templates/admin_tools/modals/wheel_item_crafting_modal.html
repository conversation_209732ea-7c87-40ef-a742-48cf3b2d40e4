<!-- Wheel Item Crafting Sequence Modal -->
<div id="wheel-item-crafting-modal" class="modal">
    <div class="modal-content crafting-modal">
        <span class="close">&times;</span>
        <div class="modal-header crafting-header">
            <h2>🔧 Wheel Item Crafting Sequence</h2>
            <p class="modal-description">
                Complete journey from generic activity to personalized wheel item. Trace every decision, transformation, and agent contribution.
            </p>
            <div class="crafting-badges">
                <span class="crafting-badge generic" title="Generic Activity Source">
                    📋 Generic Activity
                </span>
                <span class="crafting-badge tailoring" title="Tailoring Process">
                    🎯 Tailoring Process
                </span>
                <span class="crafting-badge wheel-item" title="Final Wheel Item">
                    🎡 Wheel Item
                </span>
            </div>
        </div>
        <div id="crafting-modal-body" class="crafting-modal-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading crafting sequence...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Crafting Modal Specific Styles */
.crafting-modal .modal-content {
    max-width: 1200px;
    width: 95%;
}

.crafting-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.crafting-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.crafting-badges {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.crafting-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: help;
}

.crafting-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.crafting-badge.generic {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.crafting-badge.tailoring {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.crafting-badge.wheel-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Crafting Sequence Styles */
.crafting-sequence {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 20px 0;
}

.crafting-step {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    position: relative;
    transition: all 0.3s ease;
}

.crafting-step:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.crafting-step::after {
    content: '↓';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    color: #6c757d;
    background: white;
    padding: 5px 10px;
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.crafting-step:last-child::after {
    display: none;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.step-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.step-title h3 {
    margin: 0;
    color: #495057;
    font-size: 1.4em;
}

.step-icon {
    font-size: 1.5em;
}

.step-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.step-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.step-badge.source {
    background: #e9ecef;
    color: #6c757d;
}

.step-badge.process {
    background: #fff3cd;
    color: #856404;
}

.step-badge.result {
    background: #d4edda;
    color: #155724;
}

.step-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.content-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.data-field {
    margin-bottom: 15px;
}

.data-field:last-child {
    margin-bottom: 0;
}

.field-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.field-value {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.field-value.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 12px;
}

/* Decision Points */
.decision-points {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.decision-points h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 16px;
}

.decision-item {
    background: white;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.decision-item:last-child {
    margin-bottom: 0;
}

.decision-criterion {
    font-weight: 600;
    color: #856404;
    margin-bottom: 5px;
}

.decision-rationale {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Agent Contributions */
.agent-contributions {
    background: #e7f3ff;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.agent-contributions h4 {
    margin: 0 0 10px 0;
    color: #0c5460;
    font-size: 16px;
}

.agent-contribution {
    background: white;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.agent-contribution:last-child {
    margin-bottom: 0;
}

.agent-name {
    font-weight: 600;
    color: #0c5460;
    margin-bottom: 5px;
}

.agent-input {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Tool Calls */
.tool-calls {
    background: #f3e5f5;
    border: 1px solid #e1bee7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.tool-calls h4 {
    margin: 0 0 10px 0;
    color: #6f42c1;
    font-size: 16px;
}

.tool-call {
    background: white;
    border: 1px solid #e1bee7;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.tool-call:last-child {
    margin-bottom: 0;
}

.tool-name {
    font-weight: 600;
    color: #6f42c1;
    margin-bottom: 5px;
}

.tool-params {
    color: #6c757d;
    font-size: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    margin-top: 5px;
}

.crafting-modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

/* Enhanced scrollbar for crafting modal body */
.crafting-modal-body::-webkit-scrollbar {
    width: 8px;
}

.crafting-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.crafting-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.crafting-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
    .step-content {
        grid-template-columns: 1fr;
    }
    
    .step-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
</style>

<script>
// Wheel Item Crafting Modal Functions - Make it globally accessible
window.showWheelItemCrafting = function(activityId, wheelData, agentCommunications) {
    const modal = document.getElementById('wheel-item-crafting-modal');
    const modalBody = document.getElementById('crafting-modal-body');
    
    if (!modal || !modalBody) {
        console.error('Crafting modal elements not found');
        return;
    }
    
    // Show modal
    modal.style.display = 'block';
    
    // Load crafting sequence
    renderCraftingSequence(modalBody, activityId, wheelData, agentCommunications);
}

function renderCraftingSequence(modalBody, activityId, wheelData, agentCommunications) {
    // Find the specific activity and wheel item
    const activities = wheelData.activities || [];
    const items = wheelData.items || [];

    // Try to find activity by ID, or use index-based matching
    let activity = activities.find(a => a.id === activityId);
    let wheelItem = items.find(i => i.activity_id === activityId || i.id === activityId);

    // If not found by ID, try index-based matching
    if (!activity && activities.length > 0) {
        const activityIndex = activities.findIndex((a, index) => index.toString() === activityId || a.name === activityId);
        if (activityIndex >= 0) {
            activity = activities[activityIndex];
            wheelItem = items[activityIndex] || activity;
        }
    }

    // If still not found, try to use the first activity as an example
    if (!activity && activities.length > 0) {
        activity = activities[0];
        wheelItem = items[0] || activity;
        console.warn(`Activity ${activityId} not found, using first activity as example`);
    }

    if (!activity) {
        modalBody.innerHTML = `
            <div class="no-data">
                <h3>⚠️ Activity Data Not Available</h3>
                <p>The requested activity (ID: ${activityId}) could not be found in the benchmark data.</p>
                <p>This could mean:</p>
                <ul>
                    <li>The benchmark was run with mocked data</li>
                    <li>The wheel generation failed</li>
                    <li>The data structure has changed</li>
                    <li>The activity ID format is different than expected</li>
                </ul>
                <p>Available data:</p>
                <ul>
                    <li>Activities: ${activities.length}</li>
                    <li>Items: ${items.length}</li>
                    <li>Agent Communications: ${agentCommunications.agents?.length || 0}</li>
                </ul>
                <details>
                    <summary>Debug Information</summary>
                    <pre>${JSON.stringify({
                        requestedId: activityId,
                        availableActivities: activities.map(a => ({ id: a.id, name: a.name })),
                        availableItems: items.map(i => ({ id: i.id, name: i.name }))
                    }, null, 2)}</pre>
                </details>
            </div>
        `;
        return;
    }
    
    // Extract agent communications related to this activity
    const agents = agentCommunications.agents || [];
    const relevantAgents = agents.filter(agent => 
        agent.output_data && 
        (JSON.stringify(agent.output_data).includes(activityId) || 
         agent.agent === 'Wheel/Activity Agent' || 
         agent.agent === 'Strategy Agent')
    );
    
    modalBody.innerHTML = `
        <div class="crafting-sequence">
            ${renderGenericActivityStep(activity)}
            ${renderStrategyDecisionStep(activity, relevantAgents)}
            ${renderTailoringStep(activity, relevantAgents)}
            ${renderWheelIntegrationStep(activity, wheelItem, relevantAgents)}
            ${renderFinalResultStep(activity, wheelItem)}
        </div>
    `;
}

function renderGenericActivityStep(activity) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">📋</span>
                    <h3>Generic Activity Source</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge source">Source</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>📋</span> Original Activity Template
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Activity ID</div>
                            <div class="field-value code">${activity.id || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Name</div>
                            <div class="field-value">${activity.name || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Domain</div>
                            <div class="field-value">${activity.domain || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Base Challenge Rating</div>
                            <div class="field-value">${activity.challenge_rating || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Generic Properties
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Duration Range</div>
                            <div class="field-value">${activity.duration_range || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Resources Required</div>
                            <div class="field-value">${activity.resources_required || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Instructions</div>
                            <div class="field-value">${activity.instructions || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Generic Activity Code</div>
                            <div class="field-value code">${activity.generic_activity_code || 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderStrategyDecisionStep(activity, relevantAgents) {
    const strategyAgent = relevantAgents.find(agent => agent.agent === 'Strategy Agent');
    const strategyData = strategyAgent?.output_data?.strategy_framework || {};

    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🧠</span>
                    <h3>Strategy Framework Decision</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Strategy</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Selection Criteria
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Domain Distribution</div>
                            <div class="field-value code">${JSON.stringify(strategyData.domain_distribution || {}, null, 2)}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Calibration</div>
                            <div class="field-value code">${JSON.stringify(strategyData.challenge_calibration || {}, null, 2)}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Selection Constraints</div>
                            <div class="field-value code">${JSON.stringify(strategyData.selection_constraints || {}, null, 2)}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🤖</span> Agent Decision Process
                    </div>
                    <div class="panel-content">
                        ${strategyAgent ? `
                            <div class="data-field">
                                <div class="field-label">Agent</div>
                                <div class="field-value">${strategyAgent.agent}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Stage</div>
                                <div class="field-value">${strategyAgent.stage || 'N/A'}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Duration</div>
                                <div class="field-value">${strategyAgent.duration_ms ? strategyAgent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Success</div>
                                <div class="field-value">${strategyAgent.success !== false ? '✅ Yes' : '❌ No'}</div>
                            </div>
                        ` : '<div class="field-value">No strategy agent data available</div>'}
                    </div>
                </div>
            </div>
            ${renderDecisionPoints(activity)}
        </div>
    `;
}

function renderTailoringStep(activity, relevantAgents) {
    const wheelAgent = relevantAgents.find(agent => agent.agent === 'Wheel/Activity Agent');

    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🎯</span>
                    <h3>Activity Tailoring Process</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Tailoring</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🔧</span> Tailoring Transformations
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Tailorization Level</div>
                            <div class="field-value">${activity.tailorization_level || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Personalized Name</div>
                            <div class="field-value">${activity.name || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Customized Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Adjusted Challenge Rating</div>
                            <div class="field-value">${activity.challenge_rating || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Personalized Instructions</div>
                            <div class="field-value">${activity.instructions || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎨</span> Customization Details
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Value Proposition</div>
                            <div class="field-value">${activity.value_proposition || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User Context Applied</div>
                            <div class="field-value">${activity.user_context_applied || 'User profile, preferences, and environmental factors'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Psychological Alignment</div>
                            <div class="field-value">${activity.psychological_alignment || 'Aligned with user trust phase and growth goals'}</div>
                        </div>
                    </div>
                </div>
            </div>
            ${renderAgentContributions(wheelAgent)}
            ${renderToolCalls(wheelAgent)}
        </div>
    `;
}

function renderWheelIntegrationStep(activity, wheelItem, relevantAgents) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🎡</span>
                    <h3>Wheel Integration</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Integration</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>⚖️</span> Probability Assignment
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Wheel Item ID</div>
                            <div class="field-value code">${wheelItem?.id || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Assigned Percentage</div>
                            <div class="field-value">${wheelItem?.percentage ? wheelItem.percentage.toFixed(1) + '%' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Position in Wheel</div>
                            <div class="field-value">${wheelItem?.position !== undefined ? wheelItem.position : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Color Assignment</div>
                            <div class="field-value">
                                ${wheelItem?.color ? `<span style="display: inline-block; width: 20px; height: 20px; background: ${wheelItem.color}; border-radius: 3px; margin-right: 8px; vertical-align: middle;"></span>${wheelItem.color}` : 'N/A'}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Strategic Placement
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Domain Balance</div>
                            <div class="field-value">Contributes to ${activity.domain || 'Unknown'} domain representation</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Distribution</div>
                            <div class="field-value">Provides ${activity.challenge_rating || 'Unknown'} level challenge</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User Growth Alignment</div>
                            <div class="field-value">Supports user's current trust phase and development goals</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderFinalResultStep(activity, wheelItem) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">✅</span>
                    <h3>Final Wheel Item</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge result">Complete</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎡</span> Ready for User Selection
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Final Activity Name</div>
                            <div class="field-value"><strong>${activity.name || 'N/A'}</strong></div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User-Facing Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Selection Probability</div>
                            <div class="field-value">${wheelItem?.percentage ? wheelItem.percentage.toFixed(1) + '%' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Estimated Duration</div>
                            <div class="field-value">${activity.duration_range || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>📊</span> Quality Metrics
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Personalization Score</div>
                            <div class="field-value">${activity.tailorization_level || 'N/A'}/100</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Appropriateness</div>
                            <div class="field-value">${activity.challenge_rating ? 'Calibrated to user level' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Value Proposition</div>
                            <div class="field-value">${activity.value_proposition || 'Supports user growth and engagement'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderDecisionPoints(activity) {
    const decisionPoints = activity.decision_points || [];

    if (decisionPoints.length === 0) {
        return `
            <div class="decision-points">
                <h4>🎯 Decision Points</h4>
                <div class="decision-item">
                    <div class="decision-criterion">Activity Selection</div>
                    <div class="decision-rationale">Selected based on domain distribution strategy and user profile alignment</div>
                </div>
            </div>
        `;
    }

    const pointsHtml = decisionPoints.map(point => `
        <div class="decision-item">
            <div class="decision-criterion">${point.criterion || 'Decision Point'}</div>
            <div class="decision-rationale">${point.rationale || 'No rationale provided'}</div>
        </div>
    `).join('');

    return `
        <div class="decision-points">
            <h4>🎯 Decision Points</h4>
            ${pointsHtml}
        </div>
    `;
}

function renderAgentContributions(agent) {
    if (!agent) {
        return `
            <div class="agent-contributions">
                <h4>🤖 Agent Contributions</h4>
                <div class="agent-contribution">
                    <div class="agent-name">Wheel/Activity Agent</div>
                    <div class="agent-input">No detailed agent communication data available</div>
                </div>
            </div>
        `;
    }

    return `
        <div class="agent-contributions">
            <h4>🤖 Agent Contributions</h4>
            <div class="agent-contribution">
                <div class="agent-name">${agent.agent}</div>
                <div class="agent-input">
                    <strong>Stage:</strong> ${agent.stage || 'N/A'}<br>
                    <strong>Duration:</strong> ${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}<br>
                    <strong>Success:</strong> ${agent.success !== false ? '✅ Yes' : '❌ No'}<br>
                    <strong>Input Data:</strong> ${Object.keys(agent.input_data || {}).length} fields<br>
                    <strong>Output Data:</strong> ${Object.keys(agent.output_data || {}).length} fields
                </div>
            </div>
        </div>
    `;
}

function renderToolCalls(agent) {
    const toolCalls = agent?.tool_calls || [];

    if (toolCalls.length === 0) {
        return `
            <div class="tool-calls">
                <h4>🔧 Tool Calls</h4>
                <div class="tool-call">
                    <div class="tool-name">Activity Catalog Query</div>
                    <div class="tool-params">No detailed tool call data available</div>
                </div>
            </div>
        `;
    }

    const callsHtml = toolCalls.map(call => `
        <div class="tool-call">
            <div class="tool-name">${call.tool_name || 'Unknown Tool'}</div>
            <div class="tool-params">${JSON.stringify(call.input_params || {}, null, 2)}</div>
        </div>
    `).join('');

    return `
        <div class="tool-calls">
            <h4>🔧 Tool Calls</h4>
            ${callsHtml}
        </div>
    `;
}

// Modal close functionality
document.addEventListener('DOMContentLoaded', function() {
    const craftingModal = document.getElementById('wheel-item-crafting-modal');
    const craftingCloseButton = craftingModal ? craftingModal.querySelector('.close') : null;

    if (craftingCloseButton) {
        craftingCloseButton.onclick = function() {
            craftingModal.style.display = 'none';
        };
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target == craftingModal) {
            craftingModal.style.display = 'none';
        }
    });
});
</script>
