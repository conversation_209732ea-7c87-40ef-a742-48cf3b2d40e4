# Comprehensive Benchmark System Fixes

This document provides a comprehensive overview of fixes implemented in the Goali benchmarking system, consolidating information from various sources. The fixes address issues related to parameter handling, database integrity, UI/error reporting, agent/workflow logic, data validation, testing, and critical workflow routing issues.

## 0. Critical Workflow Routing Fixes

### 0.1. Wheel Generation Workflow Routing Logic
**Problem**: Workflow benchmarks were failing with "Unexpected state in resource routing" errors, causing "Silent fallbacks detected" false positives where real mode was incorrectly reported as mock mode.

**Root Cause**:
- Stage transitions were happening at inconsistent times in the workflow execution
- Agent nodes were updating the stage internally, but routing functions expected both the correct stage AND the correct `last_agent` to be set
- The resource routing logic expected the stage to be `resource_assessment` and `last_agent` to be `resource`, but the stage was still `orchestration_initial` when the routing function ran

**Solution**:
- Removed stage updates from individual agent nodes in `backend/apps/main/graphs/wheel_generation_graph.py`
- Made routing logic more flexible to accept multiple valid stages
- Updated `route_from_resource` to accept both `orchestration_initial` and `resource_assessment` stages
- Simplified all routing functions to focus primarily on `last_agent` rather than requiring exact stage matches

**Impact**:
- ✅ Eliminates "Unexpected state in resource routing" errors
- ✅ Allows workflows to execute properly in real mode
- ✅ Fixes false "Silent fallbacks detected" errors
- ✅ Enables proper execution mode tracking

**Files Changed**:
- [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py) - Lines 305-625 (routing logic)

**Testing**:
- Verified workflow can execute without routing errors
- Confirmed multiple agents participate in workflow execution
- Validated that real mode execution tracking works properly

## 1. Parameter Handling & Execution Mode

### 1.1. Execution Mode Parameter Propagation
**Problem**: Execution mode parameters (`use_real_llm`, `use_real_tools`, `use_real_db`) sent from the frontend were not consistently passed through to Celery tasks or to the actual benchmark execution methods.
**Root Cause**:
    - `BenchmarkRunView` in [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py:590-596) not extracting/passing parameters to the task.
    - `benchmark_params` dictionary in Celery tasks missing these parameters.
**Solution**:
    - Modified [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py:590-596) to extract and pass execution mode parameters.
    - Enhanced `run_template_test` task in [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py) to extract and include these parameters in `benchmark_params` for both multi-range and standard evaluation paths, ensuring they reach workflow and agent benchmark managers.
**Files Changed**:
    - [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py)
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)

### 1.2. User Profile ID Parameter Extraction
**Problem**: Workflow benchmarks launched from the quick test interface were failing with "Missing required field 'user_profile_id' in input_data" error, even when `user_profile_id` was provided in the request parameters.
**Root Cause**: `user_profile_id` was correctly sent by `quick_test.js` within a `params` object. While `views.py` passed it to Celery, the task itself in [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py:176-179) didn't extract it from `params` if the direct `user_profile_id` argument was `None`.
**Solution**:
    - **Celery Task Update**: Modified [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py:176-179) to extract `user_profile_id` from `params` if the direct argument is `None`.
    - **Workflow Manager Enhancement**: Added similar extraction logic in [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:592-595).
**Files Changed**:
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)

### 1.3. Prevention of Silent Execution Mode Fallbacks
**Problem**: "Full real" mode selections in the admin interface could silently fall back to mock mode without user warning, making it impossible to reliably evaluate real mode performance.
**Root Cause**:
    - Silent LLM fallbacks in `_configure_agent_for_execution_mode()` in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:22-144) if LLM configs were missing or failed to configure.
    - Real tools mode was effectively disabled and would raise an error instead of working.
    - The benchmark history UI showed the requested mode, not the actual execution mode.
    - There was no mechanism to detect and report when real mode requests fell back to mock mode.
    - **CRITICAL**: The `_actual_execution_modes` field was not properly defined in the `WheelGenerationState` Pydantic model, causing execution mode tracking to fail silently.
**Solution**:
    - **Fix Execution Mode Tracking**: Added `actual_execution_modes` as a proper field in the `WheelGenerationState` Pydantic model to enable reliable execution mode tracking.
    - **Fix Stage Transition Logic**: Updated all agent nodes to properly handle stage transitions and prevent routing errors that were causing workflows to take error paths instead of executing all agents.
    - **Prevent Silent Fallbacks**: Modified `_configure_agent_for_execution_mode` in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:22-144) to raise errors if real components (LLM, DB, Tools) cannot be configured as requested, instead of silently falling back.
    - **Enable Real Tools Mode**: Updated logic to attempt using the real tool registry from the database.
    - **Actual Execution Mode Tracking**: Implemented tracking of actual execution modes (real_llm, real_db, real_tools) for each agent, stored in the workflow state's `actual_execution_modes` field.
    - **Enhanced Workflow Result Metadata**: Workflow results (in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:888-931)) now include `actual_execution_modes`, `requested_execution_mode`, and flags like `has_silent_fallbacks` and `silent_fallbacks` list.
    - **Enhanced Benchmark Result Storage**: [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:1746-1772) (lines 1746-1772, 2120-2223) extracts and stores detailed actual execution mode information from `raw_results`, including detected silent fallbacks.
    - **UI Warnings**: [`backend/templates/admin_tools/benchmark_history.html`](backend/templates/admin_tools/benchmark_history.html:1044-1054) (lines 1044-1054, 2497-2569) updated to display warnings if the requested real mode fell back to mock for any component.
**Files Changed**:
    - [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py)
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)
    - [`backend/templates/admin_tools/benchmark_history.html`](backend/templates/admin_tools/benchmark_history.html)

### 1.4. OrchestratorAgent Parameter Errors
#### 1.4.1. Unexpected `tool_registry` Argument
**Problem**: Workflow benchmarks were failing with the error: `OrchestratorAgent.__init__() got an unexpected keyword argument 'tool_registry'`.
**Root Cause**: The `_configure_agent_for_execution_mode` function in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:75-82) was incorrectly trying to pass a `tool_registry` parameter to agent constructors. Agents load tools through their database service instead.
**Solution**: Removed the `tool_registry` parameter from agent kwargs in `_configure_agent_for_execution_mode`. Agents now properly configure mock tools via the state's `mock_tools` property.
**Files Changed**:
    - [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py)
    - [`backend/apps/main/agents/benchmarking.py`](backend/apps/main/agents/benchmarking.py)

#### 1.4.2. Unexpected `_actual_execution_mode` Argument
**Problem**: Workflow benchmarks were failing with `OrchestratorAgent.__init__() got an unexpected keyword argument '_actual_execution_mode'`.
**Root Cause**: `_configure_agent_for_execution_mode` in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:122-140) was incorrectly adding an `_actual_execution_mode` parameter to agent constructor kwargs. This parameter was intended for execution mode tracking within the workflow state but was being passed directly to agent constructors, which do not accept it.
**Solution**: Removed `agent_kwargs['_actual_execution_mode']` assignment from `_configure_agent_for_execution_mode` function. Execution mode tracking is correctly handled through `state._actual_execution_modes`. Added comments explaining that execution mode tracking is handled through state, not agent parameters.
**Files Changed**:
    - [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py)

## 2. Database Integrity & Constraints

### 2.1. `tool_call_details` Field Constraint
**Problem**: The `tool_call_details` field in the `BenchmarkRun` model had a NOT NULL constraint in the database but no default value, causing `IntegrityError` when creating benchmark runs.
**Root Cause**: Migration inconsistency where the field was added with a default in the model but the database constraint was not properly set.
**Solution**: Created migration [`backend/apps/main/migrations/0017_fix_tool_call_details_constraint.py`](backend/apps/main/migrations/0017_fix_tool_call_details_constraint.py) to:
    - Update existing NULL values to an empty dict `{}`.
    - Set a proper default value `'{}'::jsonb` in the database.
    - Ensure the NOT NULL constraint with this default.
**Files Changed**:
    - [`backend/apps/main/migrations/0017_fix_tool_call_details_constraint.py`](backend/apps/main/migrations/0017_fix_tool_call_details_constraint.py)

### 2.2. `agent_communications` Field Constraint
**Problem**: Celery tasks were failing with `null value in column "agent_communications"` database constraint violations when running wheel generation benchmarks via the admin interface.
**Root Cause**: The `agent_communications` field was added to the `BenchmarkRun` model with `default=dict` but without `null=True`. The `BenchmarkRun.objects.create()` call in `benchmark_manager.py` was missing this required field.
**Solution**:
    - Added `agent_communications={}` to `BenchmarkRun.objects.create()` in [`backend/apps/main/services/benchmark_manager.py`](backend/apps/main/services/benchmark_manager.py:708).
    - Enhanced error handling for database constraint violations with context.
    - Updated relevant test files to include the missing field.
**Files Changed**:
    - [`backend/apps/main/services/benchmark_manager.py`](backend/apps/main/services/benchmark_manager.py)
    - Test files (e.g., [`backend/apps/main/tests/test_services/test_workflow_benchmark_manager.py`](backend/apps/main/tests/test_services/test_workflow_benchmark_manager.py:291), [`backend/apps/main/tests/test_integration/test_workflow_benchmarking.py`](backend/apps/main/tests/test_integration/test_workflow_benchmarking.py:154)).

### 2.3. `GenericAgent` Unique Constraint Violation in Tests
**Problem**: Workflow benchmark tests were failing with persistent `UniqueViolation` errors: `duplicate key value violates unique constraint "main_genericagent_role_key"`.
**Root Cause**: The issue stemmed from the interaction between Django's test transaction isolation and asynchronous database operations via `sync_to_async`. The "filter then create" pattern used in test helpers (e.g., `create_test_benchmark_run_async` in [`backend/apps/main/tests/test_integration/test_workflow_benchmarking.py`](backend/apps/main/tests/test_integration/test_workflow_benchmarking.py:89-128)) for fixed role names like `AgentRole.MENTOR` led to race conditions. Multiple tests attempting to create agents with the same fixed role could result in one test's `filter().first()` check not seeing another test's agent due to transaction isolation nuances, then attempting to `create()` the same role.
**Solution**: Replaced the "filter then create" pattern with Django's atomic `get_or_create()` method for shared, fixed-role agents in asynchronous test setup helpers.
**Files Changed**:
    - [`backend/apps/main/tests/test_integration/test_workflow_benchmarking.py`](backend/apps/main/tests/test_integration/test_workflow_benchmarking.py)

## 3. UI, Error Reporting & Display

### 3.1. Improved UI Error Display & Surfacing
**Problem**:
    - WebSocket error messages with `debug_info` structure (containing a `content` field) were not being properly parsed and displayed in the UI by `context_preview.js`, showing generic errors instead of detailed information.
    - The `'list' object has no attribute 'get'` error, while defensively handled in the backend, was not properly surfacing in the UI, making it difficult to diagnose.
**Solution**:
    - **UI Parsing**: Modified [`backend/static/admin/js/context_preview.js`](backend/static/admin/js/context_preview.js) to handle both old (`data.data`) and new (`data.content`) `debug_info` message structures for improved error message extraction.
    - **Error Detection & Reporting for UI**:
        - Enhanced error detection in `_store_results` ([`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:1752-1771)) for specific errors like `'list' object has no attribute 'get'`, with detailed reporting via `EventService` to make them surface in the UI.
        - Added specific error detection in Celery task handlers ([`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py:204-206)) with "CRITICAL PARAMETER TYPE ERROR" markers for easy identification and UI broadcasting.
        - Ensured errors are broadcast to the UI via `EventService` with detailed diagnostic information.
**Files Changed**:
    - [`backend/static/admin/js/context_preview.js`](backend/static/admin/js/context_preview.js)
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)

### 3.2. WebSocket Error Broadcasting Enhancement
**Problem**: Errors occurring in Celery tasks (e.g., database constraint violations) were being logged but not displayed in the UI, making it difficult to debug benchmark failures from the user's perspective.
**Root Cause**: Errors were not being consistently broadcast through the `EventService` from the backend to the `benchmark_dashboard` WebSocket group.
**Solution**:
    - Added `EventService` error broadcasting in [`backend/apps/main/services/benchmark_manager.py`](backend/apps/main/services/benchmark_manager.py) (in `_store_results_sync` method) and [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py) (in both standard and multi-range evaluation error handlers).
    - Created user-friendly error messages for different error types.
    - Ensured errors are sent to the `benchmark_dashboard` group for immediate UI display.
    - Used appropriate `EventService` methods for sync/async contexts and included fallback error handling.
**Files Changed**:
    - [`backend/apps/main/services/benchmark_manager.py`](backend/apps/main/services/benchmark_manager.py)
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)

### 3.3. Workflow Benchmark Display & Modal Issues
#### 3.3.1. Incorrect "Agent Evaluation" Display in History
**Problem**: Workflow benchmarks were being displayed as "Agent Evaluation" instead of "Workflow (workflow_type)" in the benchmark history interface.
**Root Cause**: The `BenchmarkRunView.get` list response in [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py:528-548) was missing the `execution_type` field.
**Solution**: Added `execution_type` determination to the list response in `BenchmarkRunView.get`, using the `_determine_execution_type` function from benchmark views. Also added the `parameters` field to the response for better error checking capabilities.
**Files Changed**:
    - [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py)

#### 3.3.2. Incorrect Modal Type for Workflow Benchmark Details
**Problem**: When opening the detail view of a workflow benchmark run from the benchmark history page, it was being displayed with the agent evaluation modal instead of the workflow evaluation modal.
**Root Cause**: The `BenchmarkRunView.get` method for individual run details in [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py:293-353) was missing several critical fields needed to determine the correct modal type, such as `execution_type`, `workflow_type`, and `agent_communications`.
**Solution**: Enhanced the API response for individual benchmark runs in `BenchmarkRunView.get` to include:
    - `execution_type` (determined using `_determine_execution_type`).
    - `workflow_type` (extracted from scenario metadata or parameters for workflow evaluations).
    - `agent_communications` (extracted from raw results for workflow evaluations).
    - `tool_call_details` (with mock vs real call breakdown, extracted by new helper `_extract_tool_call_details()`).
    - `comparison_results` (statistical comparison data if available, extracted by new helper `_extract_comparison_results()`).
The existing frontend JavaScript already correctly handles this enhanced API response.
**Files Changed**:
    - [`backend/apps/admin_tools/views.py`](backend/apps/admin_tools/views.py) (lines 293-353 for main method, 643-680 for helper methods).

### 3.4. Structured Error Handling in `BenchmarkRun` Model
**Problem**: Errors from workflow benchmarks were being stored as simple strings and thus not being properly displayed in the benchmark history detailed view, which expected errors in a structured format.
**Solution**: Enhanced the `BenchmarkRun` model in [`backend/apps/main/models.py`](backend/apps/main/models.py:1026-1101) with comprehensive error handling methods:
    - `has_errors()`: Checks if the run has any errors.
    - `has_critical_errors()`: Checks for critical errors specifically.
    - `get_error_summary()`: Gets a structured summary of errors.
    - `get_errors_by_type()`: Filters errors by type.
    Ensured that the error storage format matches frontend expectations, allowing existing error display logic in `benchmark_history.html` to work correctly.
**Files Changed**:
    - [`backend/apps/main/models.py`](backend/apps/main/models.py)

## 4. Agent & Workflow Logic, Data Flow, Robustness

### 4.1. Type Safety and Defensive Programming in `_create_benchmark_run_sync`
**Problem**: Persistent `AttributeError: 'list' object has no attribute 'get'` errors were occurring in workflow benchmarks, particularly within the `_create_benchmark_run_sync` function in [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py). This was often due to parameters like `parameters`, `raw_results`, or `stage_performance_details` being passed as lists instead of the expected dictionaries. Logger objects could also be corrupted during test execution or mocking.
**Root Cause**:
    - Inconsistent data types being passed for critical parameters.
    - Potential for logger object corruption in test environments.
    - Failures in dictionary comprehensions (e.g., for `stage_performance_details` if `result.stage_timings` contained unexpected data types).
**Solution**: Implemented a comprehensive defensive programming approach in [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:1709-1918) (various lines within this range):
    - **Enhanced Parameter Validation**: Added `isinstance()` checks for all dictionary parameters (`parameters`, `raw_results`, `stage_performance_details`) in `_create_benchmark_run_sync`. If an incorrect type is detected, it defaults to an empty dictionary (`{}`) and logs a warning.
    - **Robust Data Structure Creation**: Enhanced dictionary comprehensions, such as the one for `stage_performance_details` in `_store_results`, with defensive type checking for source data like `result.stage_timings`.
    - **Logger Object Validation**: Added validation (`hasattr()` and `callable()` checks) before calling any logger methods. Implemented cascading fallback mechanisms, including `print` statements or attempts to use a re-initialized logger, if the primary logger object is corrupted.
    - **Defensive Raw Results Handling**: In `_store_results`, added checks to ensure `result.to_dict()` returns a dictionary. If not, it logs a critical error and converts the data to an error dictionary to prevent downstream failures. Added debug logging for the `result` object's type and the return type of `result.to_dict()`.
**Files Changed**:
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py) (throughout `_create_benchmark_run_sync` and `_store_results` methods).

### 4.2. Agent User ID Conversion for Benchmark IDs
**Problem**: Workflow benchmarks were failing with an error like `Invalid user_profile_id format: benchmark-user-37376da1. Cannot convert to int for DB.` This initial `ValueError` then led to cascading errors, often culminating in an `AttributeError: 'list' object has no attribute 'get'` during benchmark result storage.
**Root Cause**: The workflow benchmark system generates string-based user IDs (e.g., `"benchmark-user-37376da1"`) for scenarios without a specific user profile. However, agents were attempting to convert these string IDs to integers for database operations.
**Solution**: Extended the existing pattern for handling test IDs (which start with 'test-') to also handle these benchmark user IDs (which start with 'benchmark-user-'). This was applied consistently across all affected agents. For both test and benchmark IDs, agents now use a default integer ID (e.g., 1) for database operations, while logging the use of the default ID.
**Files Changed**:
    - [`backend/apps/main/agents/ethical_agent.py`](backend/apps/main/agents/ethical_agent.py:128-135)
    - [`backend/apps/main/agents/strategy_agent.py`](backend/apps/main/agents/strategy_agent.py:117-124)
    - [`backend/apps/main/agents/orchestrator_agent.py`](backend/apps/main/agents/orchestrator_agent.py:117-124)
    - [`backend/apps/main/agents/mentor_agent.py`](backend/apps/main/agents/mentor_agent.py:151-158)
    - [`backend/apps/main/agents/psy_agent.py`](backend/apps/main/agents/psy_agent.py:131-138)
    - [`backend/apps/main/agents/wheel_activity_agent.py`](backend/apps/main/agents/wheel_activity_agent.py:133-140)

### 4.3. Agent Communications Data Flow Standardization (Architectural Fix)
**Problem**: An `AttributeError: 'list' object has no attribute 'get'` was occurring in `_create_benchmark_run_sync` when trying to process agent communications data.
**Root Cause**: An architectural inconsistency in how agent communications data flowed. `AgentCommunicationTracker.export_data()` returned a dictionary like `{'enabled': True, 'workflow_id': '...', 'agents': [...], ...}`. `BenchmarkResult.to_dict()` stored this data. However, `_create_benchmark_run_sync` had inconsistent logic for extracting this data, sometimes expecting the full dictionary and sometimes just the `agents` list.
**Solution**: Implemented a standardized agent communications interface in [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:1863-2010) (lines 1863-1912 for usage, 1940-2010 for new methods):
    - Created a `_extract_agent_communications()` method as the single point of truth for extracting agent communications data from various possible locations within `raw_results`.
    - Created a `_normalize_agent_communications_format()` method to handle different input formats (e.g., the full dictionary from `export_data()`, or a direct list of agent communications) and normalize them to a consistent standard dictionary structure.
    - Updated `BenchmarkResult.from_dict` (line 356) to correctly handle cases where `last_output` might contain a list format for agent communications.
**Files Changed**:
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)

### 4.4. Tool Call Details Extraction Logic (Data Flow Fix)
**Problem**: An `AttributeError: 'list' object has no attribute 'get'` was occurring in `_create_benchmark_run_sync` when trying to extract tool call details, specifically when attempting to access `raw_results['last_output_data']`.
**Root Cause**: A data flow mismatch. `BenchmarkResult.to_dict()` stores `self.last_output_data` under the key `'last_output'` in the dictionary it returns (which becomes `raw_results`). The code was incorrectly looking for a key named `'last_output_data'` within `raw_results`.
**Solution**: Corrected the data flow understanding in [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py:1866-1876) (lines 1866-1876 for tool calls, 1930-1935 for related agent comms):
    - Removed the redundant search for `'last_output_data'` in `raw_results` when extracting tool call details and agent communications.
    - Streamlined the logic to look in the correct locations based on the actual data structure produced by `BenchmarkResult.to_dict()`.
**Files Changed**:
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)

### 4.5. Workflow vs. Agent Benchmark Routing Logic
**Problem**: Template tests were using `AgentBenchmarker` for all scenarios, including workflow scenarios. This caused workflow benchmarks to execute individual agents instead of the full workflows.
**Root Cause**: The `run_template_test` task in [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py) was not checking if a scenario was a workflow scenario and always defaulted to using `AgentBenchmarker`.
**Solution**:
    - Implemented automatic detection of workflow scenarios based on the presence of `workflow_type` metadata in the scenario.
    - Added routing logic to use `WorkflowBenchmarker` for workflow scenarios and `AgentBenchmarker` for agent scenarios.
    - Updated benchmark execution calls to use the correct method (`execute_benchmark` for workflows vs `run_benchmark` for agents).
**Files Changed**:
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)

## 5. Schema & Data Validation

### 5.1. Schema Validation Fixes for Mock Tool Responses
**Problem**: Schema validation was failing for workflow benchmark scenarios (e.g., for `mock_tool_responses/get_user_profile`) with an error indicating the provided mock response was not valid under any of the given schemas.
**Root Cause**: The `mock_tool_responses` in scenario JSON files were missing a required `response` wrapper. According to the schema, mock tool responses should have the format: `"tool_name": { "response": "{\"key\": \"value\", ...}" }`. However, they were incorrectly formatted as: `"tool_name": { "key": "value", ... }`. Additionally, the value of the "response" field needed to be a JSON string.
**Solution**:
    - An automated Python script was created and executed to scan all scenario files in `backend/testing/benchmark_data/scenarios/`.
    - The script identified mock tool responses missing the `response` wrapper and automatically converted the direct object format to the required schema format.
    - It also ensured that the response values were correctly formatted as JSON strings.
**Files Fixed**: Multiple JSON scenario files located in `backend/testing/benchmark_data/scenarios/` (e.g., `mentor_postactfb_65.json`, `mentor_discussion_85.json`, `mentor_discussion_65.json`, etc.).

## 6. Testing & Evaluation

### 6.1. General Test Fixes for Benchmark Runs
**Problem**: Several test files were creating `BenchmarkRun` objects without the `tool_call_details` field, causing test failures after this field became non-nullable. Other issues included duplicate key violations when creating `BenchmarkScenario` objects and missing required fields like `input_data`.
**Solution**:
    - Added `tool_call_details={}` to all relevant test `BenchmarkRun.objects.create()` calls.
    - Fixed test fixtures to use `get_or_create` instead of `create` for `BenchmarkScenario` objects to avoid duplicate key violations.
    - Ensured missing required fields like `input_data` were added for `BenchmarkScenario` objects in tests.
**Files Changed**:
    - [`backend/apps/main/tests/test_integration/test_workflow_benchmarking.py`](backend/apps/main/tests/test_integration/test_workflow_benchmarking.py)
    - [`backend/apps/main/tests/test_services/test_workflow_benchmark_manager.py`](backend/apps/main/tests/test_services/test_workflow_benchmark_manager.py)
    - [`backend/apps/admin_tools/benchmark/tests/test_benchmark_history.py`](backend/apps/admin_tools/benchmark/tests/test_benchmark_history.py)

### 6.2. `SemanticEvaluator` `RealLLMClient` Initialization
**Problem**: Celery tasks were failing with `RealLLMClient.__init__() got an unexpected keyword argument 'model_name'` when running wheel generation benchmarks that included semantic evaluation.
**Root Cause**: The `SemanticEvaluator._get_llm_client()` method was attempting to create `RealLLMClient(model_name=model_name)`. However, `RealLLMClient` constructor only accepts an `llm_config` parameter, not `model_name` directly. The semantic evaluator was receiving model names (e.g., "mistral/mistral-small") but needed to map them to `LLMConfig` instances from the database. Database queries in an async context were also causing additional issues.
**Solution**:
    - Completely rewrote the `_get_llm_client()` method in [`backend/apps/main/services/semantic_evaluator.py`](backend/apps/main/services/semantic_evaluator.py:512-582) to:
        - Properly find `LLMConfig` instances by `model_name` using `sync_to_async` for database queries.
        - Implement intelligent mapping from model names to `LLMConfig` entries, including exact matches, partial matches (e.g., "mistral/mistral-small" to "mistral-small-latest"), and fallbacks to default evaluation config, regular default config, or creating a temporary config as a last resort.
        - Pass the resolved `llm_config` object to the `RealLLMClient` constructor.
    - Enhanced error handling and logging throughout this process.
**Files Changed**:
    - [`backend/apps/main/services/semantic_evaluator.py`](backend/apps/main/services/semantic_evaluator.py)

## 7. General Debugging Experience Improvements

### 7.1. Workflow Benchmark Debugging Enhancements
**Problem**: Users experienced various issues when launching workflow benchmarks for wheel generation scenarios, including database constraint violations (e.g., `tool_call_details` being null) and silent Celery task failures where errors were not visible in the UI, leading to a poor debugging experience.
**Solution Summary (details covered in other sections)**:
    - **Database Constraint Fix for `tool_call_details`**: Ensured `tool_call_details` is properly handled in `_create_benchmark_run_sync` within [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py) by correctly extracting it from `raw_results` with appropriate fallbacks.
    - **Enhanced Error Broadcasting in Celery Tasks**: Modified [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py) for comprehensive error capture, including specific error type detection, and enhanced error broadcasting via `EventService` to WebSocket clients. This includes detailed error context like traceback, scenario, and workflow type.
    - **Improved UI Error Display**: Updated [`backend/templates/admin_tools/benchmark_history.html`](backend/templates/admin_tools/benchmark_history.html) to include a "Status" column in the benchmark history table with visual error indicators (color-coded styling) and row-level error highlighting. Enhanced error tooltips with detailed error counts.
    - **Enhanced WebSocket Error Handling in Frontend**: Improved WebSocket error message parsing in [`backend/static/admin/js/context_preview.js`](backend/static/admin/js/context_preview.js), added global error notifications for critical errors, and enhanced error detail display with structured information and expandable details.
**Key Files Involved**:
    - [`backend/apps/main/services/async_workflow_manager.py`](backend/apps/main/services/async_workflow_manager.py)
    - [`backend/apps/main/tasks/benchmark_tasks.py`](backend/apps/main/tasks/benchmark_tasks.py)
    - [`backend/templates/admin_tools/benchmark_history.html`](backend/templates/admin_tools/benchmark_history.html)
    - [`backend/static/admin/js/context_preview.js`](backend/static/admin/js/context_preview.js)
## 8. Validation of 13 Test Error Fixes (Implemented Q1 2025)

This section outlines the validation strategy, risk assessment, and preventative measures for a set of 13 test errors that were addressed through three targeted fixes.

**Overall Goal:** To rigorously validate the implemented fixes for all 13 test errors using targeted `pytest` executions within the `web-test` Docker container, assess potential impacts, and establish measures to prevent future occurrences, ensuring overall system stability and maintainability.

**Summary of Fixes Addressed:**

1.  **MockDatabaseService Import Errors (3 errors resolved)**:
    *   Fixed incorrect import path in `backend/apps/main/graphs/wheel_generation_graph.py`
    *   Changed from `from apps.main.services.database_service import MockDatabaseService`
    *   To: `from apps.main.testing.mock_database_service import MockDatabaseService`
2.  **Database Unique Constraint Violations (8 errors resolved)**:
    *   Updated `backend/apps/main/tests/test_workflow_benchmark_fixes.py`
    *   Updated `backend/apps/main/tests/test_workflow_benchmark_integration.py`
    *   Replaced direct `GenericAgent.objects.create()` calls with `GenericAgentFactory()` to ensure unique roles.
3.  **Database Access Without Markers (1 error resolved)**:
    *   Added `@pytest.mark.django_db` decorator to `test_workflow_benchmark_parameter_handling` in `backend/apps/main/tests/test_orchestrator_agent_parameter_fix.py`.

### 8.1. Targeted Validation Strategy

The following `docker compose` commands will execute `pytest` against the specific test files relevant to each fix, running within the `web-test` container. The paths are relative to the `/usr/src/app` directory inside the container (which corresponds to the `backend` directory).

*   **A. For MockDatabaseService Import Errors (3 errors resolved):**
    *   **Fix Location**: `backend/apps/main/graphs/wheel_generation_graph.py`
    *   **Test File**: `backend/apps/main/tests/test_graphs/test_wheel_generation_graph.py`
    *   **Validation Command**:
        ```bash
        docker compose -f backend/docker-compose.yml run --rm web-test python -Xfrozen_modules=off -m pytest -c backend/pytest.ini --reuse-db -p testing.error_reporter_plugin apps/main/tests/test_graphs/test_wheel_generation_graph.py
        ```

*   **B. For Database Unique Constraint Violations (8 errors resolved):**
    *   **Fix Locations**:
        *   `backend/apps/main/tests/test_workflow_benchmark_fixes.py`
        *   `backend/apps/main/tests/test_workflow_benchmark_integration.py`
    *   **Test Files**:
        *   `backend/apps/main/tests/test_workflow_benchmark_fixes.py`
        *   `backend/apps/main/tests/test_workflow_benchmark_integration.py`
    *   **Validation Commands**:
        ```bash
        docker compose -f backend/docker-compose.yml run --rm web-test python -Xfrozen_modules=off -m pytest -c backend/pytest.ini --reuse-db -p testing.error_reporter_plugin apps/main/tests/test_workflow_benchmark_fixes.py
        ```
        ```bash
        docker compose -f backend/docker-compose.yml run --rm web-test python -Xfrozen_modules=off -m pytest -c backend/pytest.ini --reuse-db -p testing.error_reporter_plugin apps/main/tests/test_workflow_benchmark_integration.py
        ```

*   **C. For Database Access Without Markers (1 error resolved):**
    *   **Fix Location**: `backend/apps/main/tests/test_orchestrator_agent_parameter_fix.py`
    *   **Test File**: `backend/apps/main/tests/test_orchestrator_agent_parameter_fix.py`
    *   **Validation Command**:
        ```bash
        docker compose -f backend/docker-compose.yml run --rm web-test python -Xfrozen_modules=off -m pytest -c backend/pytest.ini --reuse-db -p testing.error_reporter_plugin apps/main/tests/test_orchestrator_agent_parameter_fix.py
        ```

*   **Verification Steps for each command**:
    1.  Ensure the `docker compose run` command completes successfully.
    2.  Verify that the `pytest` output shows all tests within the specified file(s) passing.
    3.  Confirm no new errors are introduced.

*   **Full Test Suite Execution**:
    *   The execution of the *full* test suite (e.g., via `scripts/run_docker_tests.sh` or by running the `web-test` service without specific file arguments) will be handled by the user. The user will then check the new error report (`test-results/error_report.txt` or `test-results/junit.xml`) once ready.

### 8.2. Risk Assessment

*   **MockDatabaseService Import Errors (Fix 1):** Minimal risk.
*   **Database Unique Constraint Violations (Fix 2 - `GenericAgentFactory`):** Medium risk (data consistency, test interdependence, factory logic).
*   **Database Access Without Markers (Fix 3 - `@pytest.mark.django_db`):** Low risk.

### 8.3. Testing Recommendations (Beyond immediate validation)

*   **Full Regression Suite**: Emphasize the importance of the user-initiated full suite run for comprehensive coverage.
*   **Integration Tests for `GenericAgent`**: Review/enhance.
*   **Data Integrity Tests**: For `GenericAgentFactory()`.
*   **Exploratory Testing**: Manual testing in staging.

### 8.4. Documentation Updates (Related to these fixes)

*   **Primary Fix Document**: This section in `docs/backend/BENCHMARK_FIXES_COMPREHENSIVE.md` serves as the primary documentation for these fixes.
*   **Test Case Descriptions**: Update any relevant test comments.
*   **Developer Onboarding/Contribution Guides**:
    *   In `docs/testing/TESTING_GUIDE.md`:
        *   Reiterate using `GenericAgentFactory()`.
        *   Reinforce `@pytest.mark.django_db`.
*   **Changelog/Release Notes**: Summarize these fixes.
*   **Code Comments**: Ensure clarity in fixed Python files.

### 8.5. Prevention Measures

*   **Static Analysis & Linters**.
*   **Pre-commit Hooks**.
*   **Enhanced Code Review Practices**.
*   **Standardized Test Data Creation**.
*   **Comprehensive CI/CD Pipeline** (running the full suite).
*   **Knowledge Sharing & Training**.
*   **Database Schema Reviews**.

### 8.6. Process Visualization

```mermaid
graph TD
    A[Start: 13 Test Errors Fixed] --> B{Targeted Validation Strategy};
    B -- Run Specific Pytest Commands in 'web-test' Container --> C{All Targeted Tests Pass?};
    C -- Yes --> D[User Runs Full Test Suite & Reviews Error Report];
    D -- All Good --> E[Conduct Risk Assessment Review];
    E --> F[Implement Further Testing Recommendations (if needed by team)];
    F --> G[Update Documentation (this section)];
    G --> H[Implement/Reinforce Prevention Measures];
    H --> I[End: System Verified, Stable & Documented];
    C -- No --> J[Re-evaluate & Debug Fixes for Targeted Tests];
```