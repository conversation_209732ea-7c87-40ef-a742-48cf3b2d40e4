# Enhanced Topic Analysis for Document Processing

This document describes the enhanced topic analysis functionality that has been added to the document processing tool, providing deeper insights into document content, topics, and coverage angles.

## Overview

The enhanced similar content analyzer now includes:

1. **Local LLM Integration** via Ollama for sophisticated topic extraction
2. **Document Angle Classification** (conceptual, technical, practical, etc.)
3. **Topic Coverage Analysis** to identify gaps and overlaps
4. **Multi-dimensional Similarity** using embeddings, topics, and traditional metrics

## Features

### 1. Topic Extraction with LLM

The system can now extract main topics from documents using a local LLM (via Ollama):

- **Main Topics**: 3-5 key topics/themes per document
- **Document Angle**: Primary approach (conceptual, technical, practical, reference, troubleshooting, strategic)
- **Technical Depth**: Rated 1-5 (beginner to expert level)
- **Topic Coverage**: How thoroughly each topic is covered (1-5 scale)

### 2. Document Angle Classification

Documents are classified into six main angles:

- **Conceptual**: Theory, overviews, principles, philosophy
- **Technical**: Implementation, code, APIs, architecture
- **Practical**: Tutorials, guides, how-tos, examples
- **Reference**: Documentation, specifications, parameters
- **Troubleshooting**: Debug guides, problem-solving, FAQs
- **Strategic**: Planning, roadmaps, business objectives

### 3. Topic Coverage Analysis

The system analyzes:

- **Multi-angle Topics**: Topics covered from multiple perspectives
- **Single-angle Topics**: Topics covered from only one angle (potential gaps)
- **Well-covered Topics**: Topics with comprehensive coverage (3+ angles)
- **Coverage Gaps**: Topics that might need additional documentation

### 4. Enhanced Similarity Detection

Similarity is now calculated using multiple factors:

- **Traditional**: Title, heading, and token similarity (30% weight)
- **Topic Similarity**: LLM-based semantic topic comparison (25% weight)
- **Embedding Similarity**: Vector-based content similarity (20% weight)

## Setup and Configuration

### Prerequisites

1. **Ollama Installation** (optional but recommended):
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh

   # Start Ollama service
   ollama serve

   # Pull a model (e.g., Llama 3.2)
   ollama pull mistral
   ```

2. **Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Configuration Options

The enhanced analyzer accepts these options:

```python
options = {
    'use_ollama': True,  # Enable/disable Ollama integration
    'ollama_host': 'http://localhost:11434',  # Ollama server URL
    'ollama_model': 'mistral',  # Model to use for analysis
    'workers': 4,  # Number of parallel workers
}
```

## Usage

### Basic Usage

```python
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore

# Initialize document store
doc_store = CachedDocumentStore()

# Configure options
options = {
    'use_ollama': True,
    'ollama_model': 'mistral'
}

# Run enhanced analysis
results = analyze_similar_content(docs_data, doc_store, options)
```

### Command Line Usage

```bash
# Run with enhanced topic analysis
python scripts/doc_processing/inventory_analyzer.py \
    --inventory doc_index.json \
    --output enhanced_analysis \
    --format md \
    --use-ollama \
    --ollama-model mistral
```

### Testing the Setup

```bash
# Test the enhanced functionality
python scripts/doc_processing/test_enhanced_analysis.py
```

## Output Format

The enhanced analyzer returns additional information:

```json
{
  "similar_pairs": [
    {
      "doc1": {"path": "...", "title": "..."},
      "doc2": {"path": "...", "title": "..."},
      "similarity": 0.85,
      "topic_similarity": 0.75,
      "embedding_similarity": 0.80,
      "doc1_topics": ["authentication", "security"],
      "doc2_topics": ["auth", "API security"],
      "doc1_angle": "technical",
      "doc2_angle": "practical",
      "doc1_technical_depth": 4,
      "doc2_technical_depth": 2
    }
  ],
  "topic_coverage": {
    "total_unique_topics": 25,
    "most_common_topics": {"authentication": 5, "API": 4},
    "angle_distribution": {"technical": 10, "practical": 8},
    "multi_angle_topics": {
      "authentication": {
        "total_docs": 5,
        "angles": {"technical": 2, "practical": 2, "reference": 1},
        "angle_count": 3
      }
    },
    "well_covered_topics": ["authentication", "API design"],
    "topic_coverage_gaps": ["advanced security", "performance"]
  },
  "ollama_used": true,
  "documents_with_topics": 45
}
```

## Performance Considerations

1. **LLM Processing**: Limited to first 50 documents to avoid overwhelming the LLM
2. **Concurrent Requests**: Limited to 4 concurrent LLM calls
3. **Timeout**: 60-second timeout per document for LLM analysis
4. **Fallback**: Heuristic classification when LLM is unavailable

## Robustness and Error Handling

The system has been enhanced with comprehensive error handling and logging:

### Enhanced Ollama Client
- **Connection validation** with detailed error messages
- **Automatic model pulling** if model is not available
- **Request retries** with exponential backoff
- **Comprehensive logging** for debugging
- **Statistics tracking** for performance monitoring
- **Graceful timeout handling** with user-friendly messages

### Robust Document Analysis
- **Content validation** before processing
- **Smart content truncation** preserving document structure
- **Enhanced JSON parsing** handling malformed LLM responses
- **Detailed progress tracking** with success/failure counts
- **Fallback mechanisms** when individual documents fail
- **Comprehensive error logging** for debugging

### Testing Tools

```bash
# Test Ollama connection
python scripts/doc_processing/test_ollama_connection.py

# Test robustness with edge cases
python scripts/doc_processing/test_robust_analysis.py
```

## Troubleshooting

### Common Issues

1. **Ollama Not Available**:
   - Check if Ollama is running: `ollama serve`
   - Verify the model is installed: `ollama pull mistral`
   - Test connection: `curl http://localhost:11434/api/tags`
   - Check the host URL in configuration

2. **Analysis Failures**:
   - Check logs for specific error messages
   - Verify document content is accessible
   - Try reducing `--max-docs` parameter
   - Use `--verbose` for detailed logging

3. **Slow Performance**:
   - Reduce the number of documents processed
   - Use a smaller/faster model (e.g., `mistral:1b`)
   - Increase timeout values with `--timeout`
   - Reduce concurrent workers with `--workers`

4. **Memory Issues**:
   - Reduce concurrent workers (`--workers 2`)
   - Use smaller content chunks
   - Process documents in smaller batches
   - Monitor system resources during analysis

5. **JSON Parsing Errors**:
   - The system now handles malformed JSON automatically
   - Check logs for specific parsing issues
   - Try different models if problems persist

### Fallback Behavior

When Ollama is not available, the system:
- Uses heuristic angle classification based on keywords
- Falls back to traditional similarity metrics
- Still provides topic coverage analysis with available data

## Dedicated Merge Analysis Mode

A specialized mode has been added for deep content analysis focused on identifying documents that should be merged:

### Features

- **Deep Content Analysis**: Uses LLM to analyze each document's subject, approach, and content type
- **Merge Candidate Detection**: Identifies documents covering the same subject with the same approach
- **Priority Scoring**: Ranks merge candidates by overlap score and potential impact
- **Detailed Reporting**: Generates comprehensive reports with actionable recommendations

### Usage

```bash
# Run dedicated merge analysis
./scripts/doc_processing/run_merge_analysis.sh

# Or use the Python script directly
python scripts/doc_processing/merge_analyzer.py --inventory doc_index.json

# With custom parameters
python scripts/doc_processing/merge_analyzer.py \
    --inventory doc_index.json \
    --similarity-threshold 0.8 \
    --max-docs 50 \
    --format md
```

### Output

The merge analysis generates a detailed report including:

- **Priority Merge Recommendations**: Top candidates ranked by merge potential
- **Subject-based Analysis**: Documents grouped by topic and approach
- **Individual Document Analysis**: Detailed breakdown of each document's characteristics
- **Actionable Recommendations**: Specific guidance on which documents to merge first

### Test the Functionality

```bash
# Test merge analysis with sample data
python scripts/doc_processing/test_merge_analysis.py
```

## Future Enhancements

Planned improvements include:
- Support for additional LLM providers (OpenAI, Anthropic)
- Caching of topic analysis results
- Interactive topic exploration interface
- Integration with knowledge graphs
- Automated documentation gap recommendations
- Merge conflict detection and resolution suggestions
