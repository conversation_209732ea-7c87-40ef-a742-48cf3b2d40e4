"""
Comprehensive documentation optimization analyzer.
Provides deep analysis for cleaning, organizing, and optimizing documentation.
"""
import time
import logging
from collections import defaultdict, Counter
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import json
import re
import os

from ..core.constants import (
    MIN_DOC_BYTES, DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST, 
    DOCUMENT_ANGLES, COMPREHENSIVE_ANALYSIS_PROMPT
)
from ..core.ollama_client import OllamaClient

logger = logging.getLogger(__name__)

def analyze_document_comprehensive(doc_summary: Any, doc_store: Any, ollama_client: OllamaClient) -> Optional[Dict[str, Any]]:
    """
    Perform comprehensive analysis on a single document for optimization with enhanced error handling.

    Args:
        doc_summary: Document summary object
        doc_store: Document store for content access
        ollama_client: Ollama client for LLM analysis

    Returns:
        Dictionary with comprehensive analysis or None if failed
    """
    doc_path = getattr(doc_summary, 'path', 'unknown')
    logger.debug(f"Starting comprehensive analysis for: {doc_path}")

    try:
        # Get document content with error handling
        try:
            content = doc_store.get_content(doc_summary.path)
        except Exception as e:
            logger.error(f"Failed to get content for {doc_path}: {e}")
            return None

        if not content:
            logger.warning(f"No content found for {doc_path}")
            return None

        content_length = len(content.strip())
        if content_length < 50:  # Very short content
            logger.warning(f"Content too short for analysis ({content_length} chars): {doc_path}")
            return None

        logger.debug(f"Analyzing document with {content_length} characters: {doc_path}")

        # Truncate content if too long but keep more for comprehensive analysis
        max_content_length = 5000  # Reduced for better reliability
        original_length = len(content)

        if len(content) > max_content_length:
            # Try to keep complete sections
            content = content[:max_content_length]
            # Find last complete paragraph
            last_paragraph = content.rfind('\n\n')
            if last_paragraph > max_content_length * 0.7:
                content = content[:last_paragraph] + "\n\n[Content truncated...]"
            else:
                content = content + "\n\n[Content truncated...]"

            logger.debug(f"Truncated content from {original_length} to {len(content)} characters")

        # Prepare prompt with error handling
        try:
            prompt = COMPREHENSIVE_ANALYSIS_PROMPT.format(
                title=doc_summary.title or "Untitled",
                path=doc_summary.path,
                content=content
            )
        except Exception as e:
            logger.error(f"Failed to format prompt for {doc_path}: {e}")
            return None

        system_prompt = """You are an expert technical documentation analyst and optimizer. Your task is to analyze documents comprehensively to identify all opportunities for improvement, optimization, and cleaning. Always respond with valid JSON only, no additional text."""

        logger.debug(f"Sending analysis request for {doc_path}")
        response = ollama_client.generate_completion(prompt, system_prompt)

        if not response:
            logger.error(f"No response from LLM for {doc_path}")
            return None

        logger.debug(f"Received response for {doc_path}, parsing JSON...")

        result = _extract_json_from_response(response)

        if result is None:
            logger.error(f"Could not extract valid JSON from LLM response for {doc_path}")
            logger.debug(f"Raw response preview: {response[:300]}...")
            return None

        # Validate that we got a reasonable response
        if not isinstance(result, dict):
            logger.error(f"LLM response is not a dictionary for {doc_path}")
            return None

        try:
            # Add metadata
            result['document_path'] = doc_summary.path
            result['document_title'] = doc_summary.title
            result['document_size'] = getattr(doc_summary, 'size', 0)
            result['word_count'] = len(content.split())
            result['last_modified'] = getattr(doc_summary, 'last_modified', None)

            # Add computed metrics
            try:
                result['computed_metrics'] = _compute_additional_metrics(content, doc_summary)
            except Exception as e:
                logger.warning(f"Failed to compute metrics for {doc_path}: {e}")
                result['computed_metrics'] = {}

            logger.info(f"Successfully analyzed {doc_path}")
            return result

        except Exception as e:
            logger.error(f"Failed to add metadata to result for {doc_path}: {e}")
            return None

    except Exception as e:
        logger.error(f"Unexpected error analyzing {doc_path}: {e}")
        logger.debug("Full exception:", exc_info=True)
        return None

def _extract_json_from_response(response_str: str) -> Optional[Dict[str, Any]]:
    """
    Extracts a JSON object from a potentially malformed LLM response string.
    Tries to find JSON within ```json blocks, then any {...} block.
    """
    # 1. Try to find JSON within ```json ... ``` block
    json_match = re.search(r'```json\s*(\{.*\})\s*```', response_str, re.DOTALL)
    if json_match:
        json_str = json_match.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.debug(f"JSON decode error in ```json block: {e}")
            pass # Fallback to other methods

    # 2. Try to find any {...} block
    json_match = re.search(r'(\{.*\})', response_str, re.DOTALL)
    if json_match:
        json_str = json_match.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.debug(f"JSON decode error in generic {{...}} block: {e}")
            pass # Fallback to other methods

    # 3. Try to parse the whole string directly after basic cleanup
    cleaned_response = response_str.strip()
    if cleaned_response.startswith('```json'):
        cleaned_response = cleaned_response[7:]
    if cleaned_response.endswith('```'):
        cleaned_response = cleaned_response[:-3]
    cleaned_response = cleaned_response.strip()
    
    try:
        return json.loads(cleaned_response)
    except json.JSONDecodeError as e:
        logger.debug(f"JSON decode error in cleaned response: {e}")
        pass

    return None

def _compute_additional_metrics(content: str, doc_summary: Any) -> Dict[str, Any]:
    """Compute additional metrics for the document."""
    
    # Basic metrics
    lines = content.split('\n')
    words = content.split()
    
    # Count different elements
    headers = len([line for line in lines if line.strip().startswith('#')])
    code_blocks = len(re.findall(r'```', content)) // 2
    links = len(re.findall(r'\[.*?\]\(.*?\)', content))
    images = len(re.findall(r'!\[.*?\]\(.*?\)', content))
    
    # Estimate reading time (average 200 words per minute)
    reading_time_minutes = len(words) / 200
    
    # Check for common issues
    issues = []
    if len(words) < 100:
        issues.append("very_short_content")
    if len(words) > 5000:
        issues.append("very_long_content")
    if headers == 0:
        issues.append("no_headers")
    if headers > len(words) / 50:  # Too many headers relative to content
        issues.append("too_many_headers")
    if 'TODO' in content.upper() or 'FIXME' in content.upper():
        issues.append("contains_todos")
    if re.search(r'\b(http://|www\.)', content):
        issues.append("insecure_links")
    
    # Check for outdated patterns
    outdated_patterns = [
        r'\b20(1[0-9]|20)\b',  # Years 2010-2020
        r'\bpython\s*2\b',
        r'\bnode\s*[0-9]\b',
        r'\bjquery\s*1\b'
    ]
    
    for pattern in outdated_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("potentially_outdated")
            break
    
    return {
        'line_count': len(lines),
        'word_count': len(words),
        'character_count': len(content),
        'header_count': headers,
        'code_block_count': code_blocks,
        'link_count': links,
        'image_count': images,
        'reading_time_minutes': round(reading_time_minutes, 1),
        'content_density': len(words) / max(len(lines), 1),  # Words per line
        'potential_issues': issues,
        'file_extension': os.path.splitext(doc_summary.path)[1],
        'directory_depth': len(doc_summary.path.split('/')) - 1
    }

def analyze_documentation_optimization(
    docs_data: List[Dict[str, Any]], 
    doc_store: Any, 
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Perform comprehensive documentation optimization analysis.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        options: Dictionary of analyzer options.

    Returns:
        A dictionary containing comprehensive optimization analysis results.
    """
    logger.info("Starting comprehensive documentation optimization analysis...")
    start_time = time.time()

    # Initialize Ollama client
    ollama_client = None
    ollama_host = options.get('ollama_host', DEFAULT_OLLAMA_HOST)
    ollama_model = options.get('ollama_model', DEFAULT_OLLAMA_MODEL)
    
    try:
        ollama_client = OllamaClient(host=ollama_host, model=ollama_model)
        if not ollama_client.is_available():
            logger.error(f"Ollama not available at {ollama_host} with model {ollama_model}")
            return {
                "error": "Ollama is required for comprehensive analysis",
                "analysis_results": {},
                "analysis_time": 0
            }
        else:
            logger.info(f"Using Ollama for comprehensive analysis: {ollama_model} at {ollama_host}")
    except Exception as e:
        logger.error(f"Failed to initialize Ollama client: {e}")
        return {
            "error": f"Failed to initialize Ollama: {e}",
            "analysis_results": {},
            "analysis_time": 0
        }

    # Filter documents
    summaries = []
    for doc_item in docs_data:
        path = doc_item['path']
        summary = doc_store.get_summary(path, doc_item)
        if summary.size < MIN_DOC_BYTES:
            continue
        summaries.append(summary)

    logger.info(f"Analyzing {len(summaries)} documents for optimization opportunities...")

    # Perform comprehensive analysis
    document_analyses = []
    max_docs = options.get('max_docs_for_analysis', 200)  # Higher limit for optimization
    
    if len(summaries) > max_docs:
        logger.warning(f"Limiting analysis to first {max_docs} documents for performance")
        summaries = summaries[:max_docs]

    # Use ThreadPoolExecutor for parallel analysis with enhanced error handling
    num_workers = min(4, options.get('workers', 4))  # Conservative worker count
    timeout_per_doc = options.get('timeout', 300)  # 5 minute default timeout

    logger.info(f"Starting parallel analysis with {num_workers} workers, {timeout_per_doc}s timeout per document")

    successful_analyses = 0
    failed_analyses = 0

    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        analysis_futures = {
            executor.submit(analyze_document_comprehensive, summary, doc_store, ollama_client): summary.path
            for summary in summaries
        }

        logger.info(f"Submitted {len(analysis_futures)} analysis tasks")

        for i, future in enumerate(analysis_futures, 1):
            path = analysis_futures[future]
            try:
                logger.debug(f"Processing result {i}/{len(analysis_futures)}: {path}")
                result = future.result(timeout=timeout_per_doc)
                if result:
                    document_analyses.append(result)
                    successful_analyses += 1
                    logger.debug(f"✓ Successfully analyzed {path}")
                else:
                    failed_analyses += 1
                    logger.warning(f"✗ No result returned for {path}")

            except TimeoutError:
                failed_analyses += 1
                logger.error(f"✗ Timeout analyzing {path} after {timeout_per_doc}s")
            except Exception as e:
                failed_analyses += 1
                logger.error(f"✗ Failed to analyze {path}: {e}")
                logger.debug("Full exception:", exc_info=True)

        # Log Ollama statistics if available
        if hasattr(ollama_client, 'get_stats'):
            stats = ollama_client.get_stats()
            logger.info(f"Ollama stats: {stats['requests_successful']}/{stats['requests_made']} successful requests")

    logger.info(f"Analysis complete: {successful_analyses} successful, {failed_analyses} failed")

    if successful_analyses == 0:
        logger.error("No documents were successfully analyzed!")
        return {
            "error": "No documents could be analyzed. Check Ollama connectivity and model availability.",
            "analysis_results": {},
            "analysis_time": time.time() - start_time
        }

    # Perform cross-document analysis
    cross_analysis = _perform_cross_document_analysis(document_analyses)
    
    # Generate optimization recommendations
    optimization_plan = _generate_optimization_plan(document_analyses, cross_analysis)
    
    # Calculate summary statistics
    summary_stats = _calculate_summary_statistics(document_analyses)

    duration = time.time() - start_time
    logger.info(f"Comprehensive optimization analysis completed in {duration:.2f}s")

    return {
        "document_analyses": document_analyses,
        "cross_analysis": cross_analysis,
        "optimization_plan": optimization_plan,
        "summary_statistics": summary_stats,
        "documents_analyzed": len(document_analyses),
        "analysis_time": duration,
        "analysis_timestamp": time.time()
    }

def _perform_cross_document_analysis(document_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze relationships and patterns across all documents."""

    # Group documents by various criteria
    by_subject = defaultdict(list)
    by_content_type = defaultdict(list)
    by_audience = defaultdict(list)
    by_quality = defaultdict(list)

    # Track issues across documents
    all_issues = defaultdict(list)
    quality_issues = []

    for analysis in document_analyses:
        if not analysis:
            continue

        path = analysis['document_path']

        # Group by subject
        main_subject = analysis.get('content_analysis', {}).get('main_subject', 'unknown')
        by_subject[main_subject.lower()].append(analysis)

        # Group by content type
        content_type = analysis.get('content_analysis', {}).get('content_type', 'unknown')
        by_content_type[content_type].append(analysis)

        # Group by audience
        audience = analysis.get('audience_analysis', {}).get('primary_audience', 'unknown')
        by_audience[audience].append(analysis)

        # Group by quality
        quality_scores = analysis.get('quality_assessment', {})
        avg_quality = sum([
            quality_scores.get('content_depth', 3),
            quality_scores.get('completeness', 3),
            quality_scores.get('clarity', 3),
            quality_scores.get('accuracy', 3),
            quality_scores.get('usefulness', 3)
        ]) / 5

        if avg_quality >= 4:
            by_quality['high'].append(analysis)
        elif avg_quality >= 3:
            by_quality['medium'].append(analysis)
        else:
            by_quality['low'].append(analysis)

        # Collect issues
        issues = analysis.get('content_issues', {})
        for issue_type, issue_list in issues.items():
            if issue_list:
                all_issues[issue_type].extend([(path, issue) for issue in issue_list])

        # Check for quality issues
        if avg_quality < 3:
            quality_issues.append({
                'path': path,
                'quality_score': avg_quality,
                'issues': issues
            })

    # Find content overlaps
    subjects_with_multiple_docs = {
        subject: docs for subject, docs in by_subject.items()
        if len(docs) > 1 and subject != 'unknown'
    }

    # Identify documentation gaps
    content_type_distribution = {ct: len(docs) for ct, docs in by_content_type.items()}
    audience_coverage = {aud: len(docs) for aud, docs in by_audience.items()}

    return {
        'subject_groups': dict(by_subject),
        'content_type_groups': dict(by_content_type),
        'audience_groups': dict(by_audience),
        'quality_groups': dict(by_quality),
        'subjects_with_multiple_docs': subjects_with_multiple_docs,
        'content_type_distribution': content_type_distribution,
        'audience_coverage': audience_coverage,
        'common_issues': dict(all_issues),
        'quality_issues': quality_issues,
        'total_subjects': len(by_subject),
        'total_content_types': len(by_content_type),
        'total_audiences': len(by_audience)
    }

def _generate_optimization_plan(document_analyses: List[Dict[str, Any]],
                              cross_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Generate a comprehensive optimization plan."""

    immediate_actions = []
    short_term_improvements = []
    long_term_strategy = []
    deletion_candidates = []
    merge_opportunities = []
    split_recommendations = []

    # Analyze each document for specific recommendations
    for analysis in document_analyses:
        path = analysis['document_path']

        # Get recommendations from LLM analysis
        recommendations = analysis.get('actionable_recommendations', {})

        # Add to appropriate categories
        immediate_actions.extend([
            {'path': path, 'action': action}
            for action in recommendations.get('immediate_actions', [])
        ])

        short_term_improvements.extend([
            {'path': path, 'improvement': improvement}
            for improvement in recommendations.get('short_term_improvements', [])
        ])

        long_term_strategy.extend([
            {'path': path, 'strategy': strategy}
            for strategy in recommendations.get('long_term_strategy', [])
        ])

        # Check deletion consideration
        deletion = recommendations.get('deletion_consideration', '')
        if deletion.lower().startswith('yes'):
            deletion_candidates.append({
                'path': path,
                'reason': deletion
            })

        # Check optimization opportunities
        opt_opportunities = analysis.get('optimization_opportunities', {})

        merge_candidates = opt_opportunities.get('merge_candidates', [])
        if merge_candidates:
            merge_opportunities.append({
                'path': path,
                'candidates': merge_candidates
            })

        split_recs = opt_opportunities.get('split_recommendations', [])
        if split_recs:
            split_recommendations.append({
                'path': path,
                'recommendations': split_recs
            })

    # Add cross-document recommendations
    quality_issues = cross_analysis.get('quality_issues', [])
    subjects_with_multiple = cross_analysis.get('subjects_with_multiple_docs', {})

    # Priority matrix based on impact and effort
    priority_matrix = {
        'critical': [],  # High impact, low effort
        'important': [], # High impact, high effort
        'quick_wins': [], # Low impact, low effort
        'consider': []   # Low impact, high effort
    }

    # Categorize actions by priority
    for action in immediate_actions:
        if any(keyword in action['action'].lower() for keyword in ['delete', 'remove', 'fix broken']):
            priority_matrix['critical'].append(action)
        else:
            priority_matrix['quick_wins'].append(action)

    for improvement in short_term_improvements:
        priority_matrix['important'].append(improvement)

    return {
        'immediate_actions': immediate_actions,
        'short_term_improvements': short_term_improvements,
        'long_term_strategy': long_term_strategy,
        'deletion_candidates': deletion_candidates,
        'merge_opportunities': merge_opportunities,
        'split_recommendations': split_recommendations,
        'priority_matrix': priority_matrix,
        'subjects_needing_consolidation': list(subjects_with_multiple.keys()),
        'quality_improvement_targets': [issue['path'] for issue in quality_issues]
    }

def _calculate_summary_statistics(document_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate summary statistics for the documentation set."""

    if not document_analyses:
        return {}

    # Quality metrics
    quality_scores = []
    completeness_scores = []
    clarity_scores = []
    usefulness_scores = []

    # Content metrics
    word_counts = []
    reading_times = []

    # Issue counts
    issue_counts = defaultdict(int)

    for analysis in document_analyses:
        quality_assessment = analysis.get('quality_assessment', {})

        # Collect quality scores
        if quality_assessment:
            quality_scores.append(quality_assessment.get('content_depth', 3))
            completeness_scores.append(quality_assessment.get('completeness', 3))
            clarity_scores.append(quality_assessment.get('clarity', 3))
            usefulness_scores.append(quality_assessment.get('usefulness', 3))

        # Collect content metrics
        computed_metrics = analysis.get('computed_metrics', {})
        if computed_metrics:
            word_counts.append(computed_metrics.get('word_count', 0))
            reading_times.append(computed_metrics.get('reading_time_minutes', 0))

            # Count issues
            for issue in computed_metrics.get('potential_issues', []):
                issue_counts[issue] += 1

    def safe_avg(lst):
        return sum(lst) / len(lst) if lst else 0

    return {
        'total_documents': len(document_analyses),
        'average_quality_score': safe_avg(quality_scores),
        'average_completeness': safe_avg(completeness_scores),
        'average_clarity': safe_avg(clarity_scores),
        'average_usefulness': safe_avg(usefulness_scores),
        'total_word_count': sum(word_counts),
        'average_word_count': safe_avg(word_counts),
        'total_reading_time_hours': sum(reading_times) / 60,
        'average_reading_time_minutes': safe_avg(reading_times),
        'common_issues': dict(issue_counts),
        'documents_with_issues': len([a for a in document_analyses if a.get('computed_metrics', {}).get('potential_issues')]),
        'high_quality_docs': len([a for a in document_analyses if safe_avg([
            a.get('quality_assessment', {}).get('content_depth', 3),
            a.get('quality_assessment', {}).get('completeness', 3),
            a.get('quality_assessment', {}).get('clarity', 3)
        ]) >= 4]),
        'low_quality_docs': len([a for a in document_analyses if safe_avg([
            a.get('quality_assessment', {}).get('content_depth', 3),
            a.get('quality_assessment', {}).get('completeness', 3),
            a.get('quality_assessment', {}).get('clarity', 3)
        ]) < 3])
    }
