"""
Deep content analysis for identifying documents that should be merged.
"""
import time
import logging
from collections import defaultdict, Counter
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import json

from ..core.constants import (
    MIN_DOC_BYTES, DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST, DOCUMENT_ANGLES
)
from ..core.ollama_client import OllamaClient

logger = logging.getLogger(__name__)

# Prompt for detailed content analysis
CONTENT_ANALYSIS_PROMPT = """
Analyze this document in detail and provide a comprehensive breakdown:

Document Title: {title}
Document Path: {path}
Content: {content}

Please respond with a JSON object containing:
1. "main_subject": The primary subject/topic this document covers (be specific)
2. "secondary_subjects": List of secondary topics covered
3. "approach": Primary approach (conceptual, technical, practical, reference, troubleshooting, strategic)
4. "content_type": Type of content (tutorial, guide, reference, overview, specification, etc.)
5. "target_audience": Who this is written for (beginner, intermediate, advanced, developer, user, admin)
6. "key_concepts": List of 5-10 key concepts or terms covered
7. "content_depth": How deep the content goes (1-5, where 1=surface level, 5=very detailed)
8. "completeness": How complete the coverage is (1-5, where 1=incomplete, 5=comprehensive)
9. "unique_value": What unique value this document provides (what makes it different)
10. "overlap_indicators": Keywords or phrases that might indicate overlap with other docs
11. "merge_potential": Likelihood this could be merged with similar docs (low/medium/high)
12. "content_quality": Overall quality assessment (1-5)

Example response:
{
  "main_subject": "API authentication using OAuth 2.0",
  "secondary_subjects": ["security best practices", "token management"],
  "approach": "technical",
  "content_type": "guide",
  "target_audience": "developer",
  "key_concepts": ["OAuth 2.0", "access tokens", "refresh tokens", "client credentials"],
  "content_depth": 4,
  "completeness": 3,
  "unique_value": "Provides specific implementation examples for our API",
  "overlap_indicators": ["authentication", "OAuth", "API security", "tokens"],
  "merge_potential": "medium",
  "content_quality": 4
}
"""

def analyze_document_content_deep(doc_summary: Any, doc_store: Any, ollama_client: OllamaClient) -> Optional[Dict[str, Any]]:
    """
    Perform deep content analysis on a single document.

    Args:
        doc_summary: Document summary object
        doc_store: Document store for content access
        ollama_client: Ollama client for LLM analysis

    Returns:
        Dictionary with detailed content analysis or None if failed
    """
    try:
        content = doc_store.get_content(doc_summary.path)
        if not content or len(content.strip()) < 100:
            return None

        # Truncate content if too long (keep more for deep analysis)
        max_content_length = 6000  # Longer for detailed analysis
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."

        prompt = CONTENT_ANALYSIS_PROMPT.format(
            title=doc_summary.title or "Untitled",
            path=doc_summary.path,
            content=content
        )

        system_prompt = """You are an expert technical writer and content analyst. Your task is to analyze documents deeply to understand their content, purpose, and potential for consolidation. Always respond with valid JSON only, no additional text."""

        response = ollama_client.generate_completion(prompt, system_prompt)

        if not response:
            return None

        # Try to parse JSON response
        try:
            # Clean up response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()

            result = json.loads(response)

            # Add metadata
            result['document_path'] = doc_summary.path
            result['document_title'] = doc_summary.title
            result['document_size'] = doc_summary.size
            result['word_count'] = len(content.split())

            return result

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM JSON response for {doc_summary.path}: {e}")
            return None

    except Exception as e:
        logger.warning(f"Failed to analyze content for {doc_summary.path}: {e}")
        return None

def identify_merge_candidates(content_analyses: List[Dict[str, Any]],
                            similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
    """
    Identify groups of documents that are candidates for merging.

    Args:
        content_analyses: List of content analysis results
        similarity_threshold: Threshold for considering documents similar

    Returns:
        List of merge candidate groups
    """
    merge_candidates = []

    # Group by main subject and approach
    subject_approach_groups = defaultdict(list)

    for analysis in content_analyses:
        if not analysis:
            continue

        main_subject = analysis.get('main_subject', '').lower().strip()
        approach = analysis.get('approach', 'unknown')
        content_type = analysis.get('content_type', 'unknown')

        # Create a key for grouping
        group_key = f"{main_subject}|{approach}|{content_type}"
        subject_approach_groups[group_key].append(analysis)

    # Analyze each group for merge potential
    for group_key, docs in subject_approach_groups.items():
        if len(docs) < 2:
            continue

        main_subject, approach, content_type = group_key.split('|')

        # Calculate overlap scores
        overlap_scores = []
        for i, doc1 in enumerate(docs):
            for doc2 in docs[i+1:]:
                overlap_score = _calculate_content_overlap(doc1, doc2)
                if overlap_score >= similarity_threshold:
                    overlap_scores.append({
                        'doc1': doc1,
                        'doc2': doc2,
                        'overlap_score': overlap_score,
                        'merge_reasoning': _generate_merge_reasoning(doc1, doc2, overlap_score)
                    })

        if overlap_scores:
            merge_candidates.append({
                'group_subject': main_subject,
                'group_approach': approach,
                'group_content_type': content_type,
                'candidate_pairs': overlap_scores,
                'total_documents': len(docs),
                'merge_priority': _calculate_merge_priority(docs, overlap_scores)
            })

    # Sort by merge priority (highest first)
    merge_candidates.sort(key=lambda x: x['merge_priority'], reverse=True)

    return merge_candidates

def _calculate_content_overlap(doc1: Dict[str, Any], doc2: Dict[str, Any]) -> float:
    """Calculate content overlap between two documents."""

    # Subject similarity (40% weight)
    subject1 = set(doc1.get('main_subject', '').lower().split())
    subject2 = set(doc2.get('main_subject', '').lower().split())
    subject_overlap = len(subject1.intersection(subject2)) / max(len(subject1.union(subject2)), 1)

    # Key concepts overlap (30% weight)
    concepts1 = set(c.lower() for c in doc1.get('key_concepts', []))
    concepts2 = set(c.lower() for c in doc2.get('key_concepts', []))
    concepts_overlap = len(concepts1.intersection(concepts2)) / max(len(concepts1.union(concepts2)), 1)

    # Overlap indicators (20% weight)
    indicators1 = set(i.lower() for i in doc1.get('overlap_indicators', []))
    indicators2 = set(i.lower() for i in doc2.get('overlap_indicators', []))
    indicators_overlap = len(indicators1.intersection(indicators2)) / max(len(indicators1.union(indicators2)), 1)

    # Same approach and content type (10% weight)
    same_approach = 1.0 if doc1.get('approach') == doc2.get('approach') else 0.0
    same_content_type = 1.0 if doc1.get('content_type') == doc2.get('content_type') else 0.0
    approach_type_score = (same_approach + same_content_type) / 2

    # Calculate weighted overlap
    total_overlap = (
        subject_overlap * 0.4 +
        concepts_overlap * 0.3 +
        indicators_overlap * 0.2 +
        approach_type_score * 0.1
    )

    return total_overlap

def _generate_merge_reasoning(doc1: Dict[str, Any], doc2: Dict[str, Any], overlap_score: float) -> str:
    """Generate reasoning for why documents should be merged."""
    reasons = []

    # Check subject similarity
    if doc1.get('main_subject', '').lower() == doc2.get('main_subject', '').lower():
        reasons.append("identical main subject")

    # Check approach and content type
    if doc1.get('approach') == doc2.get('approach'):
        reasons.append(f"same approach ({doc1.get('approach')})")

    if doc1.get('content_type') == doc2.get('content_type'):
        reasons.append(f"same content type ({doc1.get('content_type')})")

    # Check target audience
    if doc1.get('target_audience') == doc2.get('target_audience'):
        reasons.append(f"same target audience ({doc1.get('target_audience')})")

    # Check completeness - suggest merging incomplete docs
    completeness1 = doc1.get('completeness', 0)
    completeness2 = doc2.get('completeness', 0)
    if completeness1 < 4 and completeness2 < 4:
        reasons.append("both documents are incomplete and could benefit from consolidation")

    # Check content depth
    depth1 = doc1.get('content_depth', 0)
    depth2 = doc2.get('content_depth', 0)
    if abs(depth1 - depth2) <= 1:
        reasons.append("similar content depth")

    reasoning = f"Overlap score: {overlap_score:.2f}. Reasons: {', '.join(reasons)}"

    return reasoning

def _calculate_merge_priority(docs: List[Dict[str, Any]], overlap_scores: List[Dict[str, Any]]) -> float:
    """Calculate priority for merging this group of documents."""

    if not overlap_scores:
        return 0.0

    # Average overlap score (40% weight)
    avg_overlap = sum(pair['overlap_score'] for pair in overlap_scores) / len(overlap_scores)

    # Number of documents involved (20% weight) - more docs = higher priority
    doc_count_score = min(len(docs) / 5.0, 1.0)  # Normalize to max 5 docs

    # Average completeness (20% weight) - lower completeness = higher merge priority
    avg_completeness = sum(doc.get('completeness', 3) for doc in docs) / len(docs)
    completeness_score = (5 - avg_completeness) / 4  # Invert so lower completeness = higher score

    # Average quality (20% weight) - consider quality in merge decisions
    avg_quality = sum(doc.get('content_quality', 3) for doc in docs) / len(docs)
    quality_score = avg_quality / 5

    priority = (
        avg_overlap * 0.4 +
        doc_count_score * 0.2 +
        completeness_score * 0.2 +
        quality_score * 0.2
    )

    return priority

def analyze_merge_candidates(
    docs_data: List[Dict[str, Any]],
    doc_store: Any,
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Perform deep content analysis to identify merge candidates.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        options: Dictionary of analyzer options.

    Returns:
        A dictionary containing merge analysis results.
    """
    logger.info("Starting deep content analysis for merge candidates...")
    start_time = time.time()

    # Initialize Ollama client
    ollama_client = None
    ollama_host = options.get('ollama_host', DEFAULT_OLLAMA_HOST)
    ollama_model = options.get('ollama_model', DEFAULT_OLLAMA_MODEL)

    try:
        ollama_client = OllamaClient(host=ollama_host, model=ollama_model)
        if not ollama_client.is_available():
            logger.error(f"Ollama not available at {ollama_host} with model {ollama_model}")
            return {
                "error": "Ollama is required for deep content analysis",
                "merge_candidates": [],
                "analysis_time": 0
            }
        else:
            logger.info(f"Using Ollama for deep analysis: {ollama_model} at {ollama_host}")
    except Exception as e:
        logger.error(f"Failed to initialize Ollama client: {e}")
        return {
            "error": f"Failed to initialize Ollama: {e}",
            "merge_candidates": [],
            "analysis_time": 0
        }

    # Filter documents
    summaries = []
    for doc_item in docs_data:
        path = doc_item['path']
        summary = doc_store.get_summary(path, doc_item)
        if summary.size < MIN_DOC_BYTES:
            continue
        summaries.append(summary)

    logger.info(f"Analyzing {len(summaries)} documents for merge potential...")

    # Perform deep content analysis
    content_analyses = []
    max_docs = options.get('max_docs_for_deep_analysis', 100)  # Limit for performance

    if len(summaries) > max_docs:
        logger.warning(f"Limiting analysis to first {max_docs} documents for performance")
        summaries = summaries[:max_docs]

    # Use ThreadPoolExecutor for parallel analysis
    num_workers = min(4, options.get('workers', 4))  # Limit concurrent LLM calls

    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        analysis_futures = {
            executor.submit(analyze_document_content_deep, summary, doc_store, ollama_client): summary.path
            for summary in summaries
        }

        for future in analysis_futures:
            try:
                result = future.result(timeout=120)  # 2 minute timeout per document
                if result:
                    content_analyses.append(result)
            except Exception as e:
                path = analysis_futures[future]
                logger.warning(f"Failed to analyze {path}: {e}")

    logger.info(f"Successfully analyzed {len(content_analyses)} documents")

    # Identify merge candidates
    similarity_threshold = options.get('merge_similarity_threshold', 0.7)
    merge_candidates = identify_merge_candidates(content_analyses, similarity_threshold)

    # Generate summary statistics
    total_merge_groups = len(merge_candidates)
    total_merge_pairs = sum(len(group['candidate_pairs']) for group in merge_candidates)

    # Calculate potential space savings
    potential_merges = []
    for group in merge_candidates:
        for pair in group['candidate_pairs']:
            potential_merges.append({
                'doc1_path': pair['doc1']['document_path'],
                'doc1_title': pair['doc1']['document_title'],
                'doc2_path': pair['doc2']['document_path'],
                'doc2_title': pair['doc2']['document_title'],
                'overlap_score': pair['overlap_score'],
                'reasoning': pair['merge_reasoning'],
                'subject': group['group_subject'],
                'approach': group['group_approach'],
                'priority': group['merge_priority']
            })

    # Sort potential merges by priority
    potential_merges.sort(key=lambda x: x['priority'], reverse=True)

    duration = time.time() - start_time
    logger.info(f"Merge analysis completed in {duration:.2f}s")

    return {
        "merge_candidates": merge_candidates,
        "potential_merges": potential_merges,
        "total_merge_groups": total_merge_groups,
        "total_merge_pairs": total_merge_pairs,
        "documents_analyzed": len(content_analyses),
        "content_analyses": content_analyses,  # Full analysis for detailed reporting
        "analysis_time": duration,
        "similarity_threshold": similarity_threshold
    }
