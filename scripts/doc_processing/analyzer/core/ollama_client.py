"""
Ollama client for local LLM integration in document analysis.
Enhanced with comprehensive error handling and logging.
"""

import json
import logging
import requests
from typing import Dict, List, Any, Optional
import time
import re
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for interacting with Ollama local LLM server."""
    
    def __init__(self, host: str = "http://localhost:11434", model: str = "mistral"):
        """
        Initialize Ollama client with enhanced error handling.

        Args:
            host: Ollama server host URL
            model: Model name to use for analysis
        """
        self.host = host.rstrip('/')
        self.model = model
        self.session = requests.Session()
        self.session.timeout = 60  # 60 second timeout

        # Enhanced logging
        logger.info(f"Initializing Ollama client: {self.host} with model {self.model}")

        # Validate host URL format
        try:
            parsed = urlparse(self.host)
            if not parsed.scheme or not parsed.netloc:
                logger.warning(f"Invalid host URL format: {self.host}")
        except Exception as e:
            logger.warning(f"Could not parse host URL {self.host}: {e}")

        # Track statistics
        self.stats = {
            'requests_made': 0,
            'requests_successful': 0,
            'requests_failed': 0,
            'total_tokens_processed': 0,
            'average_response_time': 0.0
        }
        
    def is_available(self) -> bool:
        """Check if Ollama server is available and model is loaded with enhanced logging."""
        logger.debug(f"Checking Ollama availability at {self.host}")

        try:
            # Check if server is running
            logger.debug("Testing server connectivity...")
            response = self.session.get(f"{self.host}/api/tags", timeout=10)

            if response.status_code != 200:
                logger.error(f"Ollama server returned status {response.status_code}: {response.text}")
                return False

            logger.debug("Server is responding, checking available models...")

            # Check if our model is available
            try:
                models_data = response.json()
                models = models_data.get('models', [])
                logger.debug(f"Found {len(models)} models on server")

                model_names = []
                for model in models:
                    model_name = model.get('name', '')
                    # Handle both "model:tag" and "model" formats
                    base_name = model_name.split(':')[0] if ':' in model_name else model_name
                    model_names.append(base_name)
                    logger.debug(f"Available model: {model_name} (base: {base_name})")

                if self.model not in model_names:
                    logger.warning(f"Model '{self.model}' not found. Available models: {model_names}")
                    logger.info(f"Attempting to pull model '{self.model}'...")
                    return self._pull_model()
                else:
                    logger.info(f"Model '{self.model}' is available")
                    return True

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse models response: {e}")
                logger.debug(f"Raw response: {response.text}")
                return False

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Cannot connect to Ollama server at {self.host}: {e}")
            return False
        except requests.exceptions.Timeout as e:
            logger.error(f"Timeout connecting to Ollama server: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking Ollama availability: {e}")
            logger.debug("Full exception:", exc_info=True)
            return False
    
    def _pull_model(self) -> bool:
        """Attempt to pull the model if it's not available with enhanced logging."""
        try:
            logger.info(f"Attempting to pull model '{self.model}' (this may take several minutes)...")

            start_time = time.time()
            response = self.session.post(
                f"{self.host}/api/pull",
                json={"name": self.model},
                timeout=600  # 10 minute timeout for model pulling
            )

            duration = time.time() - start_time

            if response.status_code == 200:
                logger.info(f"Successfully pulled model '{self.model}' in {duration:.1f} seconds")
                return True
            else:
                logger.error(f"Failed to pull model '{self.model}': HTTP {response.status_code}")
                logger.debug(f"Response: {response.text}")
                return False

        except requests.exceptions.Timeout as e:
            logger.error(f"Timeout pulling model '{self.model}': {e}")
            logger.info("Model pulling can take a long time. Consider pulling manually: ollama pull {self.model}")
            return False
        except Exception as e:
            logger.error(f"Failed to pull model '{self.model}': {e}")
            logger.debug("Full exception:", exc_info=True)
            return False
    
    def generate_completion(self, prompt: str, system_prompt: Optional[str] = None) -> Optional[str]:
        """
        Generate a completion using Ollama with enhanced error handling and logging.

        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt

        Returns:
            Generated text or None if failed
        """
        self.stats['requests_made'] += 1
        start_time = time.time()

        try:
            # Validate inputs
            if not prompt or not prompt.strip():
                logger.error("Empty or invalid prompt provided")
                self.stats['requests_failed'] += 1
                return None

            # Log request details
            prompt_length = len(prompt)
            logger.debug(f"Generating completion for prompt of {prompt_length} characters")

            # Prepare messages
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
                logger.debug(f"Using system prompt of {len(system_prompt)} characters")
            messages.append({"role": "user", "content": prompt})

            # Prepare payload with robust options
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Very low temperature for consistent analysis
                    "top_p": 0.9,
                    "num_predict": 4096,  # Increased token limit
                    "repeat_penalty": 1.1,
                    "num_ctx": 8192  # Increased context window
                }
            }

            logger.info(f"Sending request to {self.host}/api/chat with model {self.model}")
            logger.debug(f"Request payload: {json.dumps(payload, indent=2)}")

            # Make the request with retries
            max_retries = 2
            for attempt in range(max_retries):
                try:
                    response = self.session.post(
                        f"{self.host}/api/chat",
                        json=payload,
                        timeout=240  # 4 minute timeout
                    )
                    break
                except requests.exceptions.Timeout as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Request timeout (attempt {attempt + 1}/{max_retries}), retrying...")
                        time.sleep(5)  # Wait before retry
                        continue
                    else:
                        raise e

            duration = time.time() - start_time

            # Handle response
            logger.info(f"Received response with status {response.status_code} in {duration:.2f}s")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.debug(f"Full response JSON: {json.dumps(result, indent=2)}")

                    content = result.get('message', {}).get('content', '')

                    if not content:
                        logger.error("Empty response content from Ollama")
                        logger.debug(f"Full result structure: {result}")
                        self.stats['requests_failed'] += 1
                        return None

                    # Log success
                    response_length = len(content)
                    logger.info(f"Successfully received {response_length} characters")
                    logger.debug(f"Response preview: {content[:200]}...")

                    self.stats['requests_successful'] += 1
                    self.stats['total_tokens_processed'] += response_length

                    return content

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Ollama response JSON: {e}")
                    logger.debug(f"Raw response: {response.text[:1000]}...")
                    self.stats['requests_failed'] += 1
                    return None

            else:
                logger.error(f"Ollama API error: HTTP {response.status_code}")
                logger.error(f"Response body: {response.text}")
                self.stats['requests_failed'] += 1
                return None

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error to Ollama server at {self.host}: {e}")
            logger.info("Make sure Ollama is running: ollama serve")
            self.stats['requests_failed'] += 1
            return None
        except requests.exceptions.Timeout as e:
            logger.error(f"Timeout waiting for Ollama response after {duration:.1f}s: {e}")
            logger.info("Consider using a smaller model or reducing content length")
            self.stats['requests_failed'] += 1
            return None
        except Exception as e:
            logger.error(f"Unexpected error generating completion: {e}")
            logger.debug("Full exception:", exc_info=True)
            self.stats['requests_failed'] += 1
            return None

    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics for debugging."""
        return self.stats.copy()
    
    def extract_topics_from_document(self, title: str, content: str, max_content_length: int = 4000) -> Optional[Dict[str, Any]]:
        """
        Extract topics and analyze document angle using LLM.
        
        Args:
            title: Document title
            content: Document content
            max_content_length: Maximum content length to send to LLM
            
        Returns:
            Dictionary with topic analysis or None if failed
        """
        # Truncate content if too long
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        from .constants import TOPIC_EXTRACTION_PROMPT
        
        prompt = TOPIC_EXTRACTION_PROMPT.format(
            title=title or "Untitled",
            content=content
        )
        
        system_prompt = """You are an expert document analyst. Your task is to analyze documents and extract their main topics and determine their approach/angle. Always respond with valid JSON only, no additional text."""
        
        response = self.generate_completion(prompt, system_prompt)
        
        if not response:
            return None
            
        # Try to parse JSON response
        try:
            # Clean up response - remove any markdown formatting
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            result = json.loads(response)
            
            # Validate required fields
            required_fields = ['main_topics', 'document_angle', 'topic_coverage', 'technical_depth']
            if all(field in result for field in required_fields):
                return result
            else:
                logger.warning(f"LLM response missing required fields: {result}")
                return None
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM JSON response: {e}")
            logger.debug(f"Raw response: {response}")
            return None
    
    def analyze_topic_similarity(self, doc1_topics: List[str], doc2_topics: List[str]) -> float:
        """
        Analyze semantic similarity between two sets of topics using LLM.
        
        Args:
            doc1_topics: Topics from first document
            doc2_topics: Topics from second document
            
        Returns:
            Similarity score between 0 and 1
        """
        if not doc1_topics or not doc2_topics:
            return 0.0
            
        prompt = f"""
        Compare these two sets of document topics and rate their semantic similarity on a scale of 0.0 to 1.0.
        
        Document 1 topics: {', '.join(doc1_topics)}
        Document 2 topics: {', '.join(doc2_topics)}
        
        Consider:
        - Exact topic matches (highest similarity)
        - Semantically related topics (medium-high similarity)
        - Topics in the same domain but different aspects (medium similarity)
        - Completely unrelated topics (low similarity)
        
        Respond with only a number between 0.0 and 1.0, no additional text.
        """
        
        system_prompt = "You are a semantic similarity expert. Respond only with a decimal number between 0.0 and 1.0."
        
        response = self.generate_completion(prompt, system_prompt)
        
        if response:
            try:
                similarity = float(response.strip())
                return max(0.0, min(1.0, similarity))  # Clamp to valid range
            except ValueError:
                logger.warning(f"Invalid similarity response: {response}")
        
        return 0.0
