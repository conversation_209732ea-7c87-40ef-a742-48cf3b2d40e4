"""
Report generator for merge analysis results.
"""
import json
from typing import Dict, List, Any
from datetime import datetime

def generate_merge_report(analysis_results: Dict[str, Any], output_format: str = "markdown") -> str:
    """
    Generate a comprehensive merge analysis report.
    
    Args:
        analysis_results: Results from merge analysis
        output_format: Output format ('markdown' or 'json')
        
    Returns:
        Formatted report as string
    """
    if output_format == "json":
        return json.dumps(analysis_results, indent=2)
    
    return _generate_markdown_merge_report(analysis_results)

def _generate_markdown_merge_report(results: Dict[str, Any]) -> str:
    """Generate detailed markdown report for merge analysis."""
    
    report = []
    
    # Header
    report.append("# Document Merge Analysis Report")
    report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Executive Summary
    report.append("## Executive Summary")
    report.append("")
    
    if "error" in results:
        report.append(f"❌ **Analysis Failed**: {results['error']}")
        return "\n".join(report)
    
    total_docs = results.get('documents_analyzed', 0)
    total_groups = results.get('total_merge_groups', 0)
    total_pairs = results.get('total_merge_pairs', 0)
    analysis_time = results.get('analysis_time', 0)
    
    report.append(f"- **Documents Analyzed**: {total_docs}")
    report.append(f"- **Merge Groups Identified**: {total_groups}")
    report.append(f"- **Potential Merge Pairs**: {total_pairs}")
    report.append(f"- **Analysis Time**: {analysis_time:.2f} seconds")
    report.append(f"- **Similarity Threshold**: {results.get('similarity_threshold', 0.7)}")
    report.append("")
    
    if total_pairs == 0:
        report.append("✅ **No merge candidates found** - Your documentation appears to be well-organized with minimal redundancy.")
        return "\n".join(report)
    
    # Priority Merge Recommendations
    report.append("## 🔥 Priority Merge Recommendations")
    report.append("")
    report.append("These document pairs have the highest potential for merging:")
    report.append("")
    
    potential_merges = results.get('potential_merges', [])
    
    for i, merge in enumerate(potential_merges[:10], 1):  # Top 10
        report.append(f"### {i}. Priority Score: {merge['priority']:.2f}")
        report.append("")
        report.append(f"**Subject**: {merge['subject'].title()}")
        report.append(f"**Approach**: {merge['approach'].title()}")
        report.append(f"**Overlap Score**: {merge['overlap_score']:.2f}")
        report.append("")
        report.append("**Documents to Merge:**")
        report.append(f"1. `{merge['doc1_path']}` - {merge['doc1_title']}")
        report.append(f"2. `{merge['doc2_path']}` - {merge['doc2_title']}")
        report.append("")
        report.append(f"**Reasoning**: {merge['reasoning']}")
        report.append("")
        report.append("---")
        report.append("")
    
    # Detailed Analysis by Subject
    report.append("## 📊 Detailed Analysis by Subject")
    report.append("")
    
    merge_candidates = results.get('merge_candidates', [])
    
    for group in merge_candidates:
        subject = group['group_subject'].title()
        approach = group['group_approach'].title()
        content_type = group['group_content_type'].title()
        
        report.append(f"### {subject} ({approach} - {content_type})")
        report.append("")
        report.append(f"- **Total Documents**: {group['total_documents']}")
        report.append(f"- **Merge Priority**: {group['merge_priority']:.2f}")
        report.append(f"- **Candidate Pairs**: {len(group['candidate_pairs'])}")
        report.append("")
        
        if group['candidate_pairs']:
            report.append("**Merge Candidates:**")
            report.append("")
            
            for pair in group['candidate_pairs']:
                doc1 = pair['doc1']
                doc2 = pair['doc2']
                
                report.append(f"- **Pair Overlap**: {pair['overlap_score']:.2f}")
                report.append(f"  - `{doc1['document_path']}` ({doc1.get('completeness', 'N/A')}/5 completeness)")
                report.append(f"  - `{doc2['document_path']}` ({doc2.get('completeness', 'N/A')}/5 completeness)")
                report.append(f"  - *{pair['merge_reasoning']}*")
                report.append("")
        
        report.append("---")
        report.append("")
    
    # Document Content Analysis
    report.append("## 📋 Individual Document Analysis")
    report.append("")
    report.append("Detailed analysis of each document's content and characteristics:")
    report.append("")
    
    content_analyses = results.get('content_analyses', [])
    
    # Group by subject for better organization
    by_subject = {}
    for analysis in content_analyses:
        subject = analysis.get('main_subject', 'Unknown').title()
        if subject not in by_subject:
            by_subject[subject] = []
        by_subject[subject].append(analysis)
    
    for subject, docs in by_subject.items():
        report.append(f"### {subject}")
        report.append("")
        
        for doc in docs:
            report.append(f"#### `{doc['document_path']}`")
            report.append("")
            report.append(f"- **Title**: {doc.get('document_title', 'N/A')}")
            report.append(f"- **Approach**: {doc.get('approach', 'N/A').title()}")
            report.append(f"- **Content Type**: {doc.get('content_type', 'N/A').title()}")
            report.append(f"- **Target Audience**: {doc.get('target_audience', 'N/A').title()}")
            report.append(f"- **Content Depth**: {doc.get('content_depth', 'N/A')}/5")
            report.append(f"- **Completeness**: {doc.get('completeness', 'N/A')}/5")
            report.append(f"- **Quality**: {doc.get('content_quality', 'N/A')}/5")
            report.append(f"- **Word Count**: {doc.get('word_count', 'N/A')}")
            report.append(f"- **Merge Potential**: {doc.get('merge_potential', 'N/A').title()}")
            report.append("")
            
            # Secondary subjects
            secondary = doc.get('secondary_subjects', [])
            if secondary:
                report.append(f"- **Secondary Subjects**: {', '.join(secondary)}")
                report.append("")
            
            # Key concepts
            concepts = doc.get('key_concepts', [])
            if concepts:
                report.append(f"- **Key Concepts**: {', '.join(concepts)}")
                report.append("")
            
            # Unique value
            unique_value = doc.get('unique_value', '')
            if unique_value:
                report.append(f"- **Unique Value**: {unique_value}")
                report.append("")
            
            report.append("")
    
    # Recommendations
    report.append("## 💡 Recommendations")
    report.append("")
    
    if total_pairs > 0:
        report.append("### Immediate Actions")
        report.append("")
        
        high_priority = [m for m in potential_merges if m['priority'] > 0.8]
        medium_priority = [m for m in potential_merges if 0.6 <= m['priority'] <= 0.8]
        
        if high_priority:
            report.append(f"1. **High Priority Merges** ({len(high_priority)} pairs):")
            report.append("   - These documents have very high overlap and should be merged immediately")
            report.append("   - Focus on the top 3-5 pairs to start")
            report.append("")
        
        if medium_priority:
            report.append(f"2. **Medium Priority Merges** ({len(medium_priority)} pairs):")
            report.append("   - Review these pairs for potential consolidation")
            report.append("   - Consider merging after addressing high priority items")
            report.append("")
        
        report.append("3. **Merge Strategy**:")
        report.append("   - Start with documents that have low completeness scores")
        report.append("   - Preserve the highest quality content from each document")
        report.append("   - Update internal links after merging")
        report.append("   - Consider creating redirects for removed documents")
        report.append("")
    
    report.append("### Quality Improvements")
    report.append("")
    
    # Find documents with low quality/completeness
    low_quality = [doc for doc in content_analyses if doc.get('content_quality', 5) < 3]
    incomplete = [doc for doc in content_analyses if doc.get('completeness', 5) < 3]
    
    if low_quality:
        report.append(f"- **{len(low_quality)} documents** have low quality scores and need improvement")
    
    if incomplete:
        report.append(f"- **{len(incomplete)} documents** are incomplete and need additional content")
    
    report.append("")
    report.append("---")
    report.append("")
    report.append("*This report was generated using deep content analysis with local LLM processing.*")
    
    return "\n".join(report)
