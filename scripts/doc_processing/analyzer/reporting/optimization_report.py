"""
Comprehensive report generator for documentation optimization analysis.
"""
import json
from typing import Dict, List, Any
from datetime import datetime

def generate_optimization_report(analysis_results: Dict[str, Any], output_format: str = "markdown") -> str:
    """
    Generate a comprehensive documentation optimization report.
    
    Args:
        analysis_results: Results from optimization analysis
        output_format: Output format ('markdown' or 'json')
        
    Returns:
        Formatted report as string
    """
    if output_format == "json":
        return json.dumps(analysis_results, indent=2)
    
    return _generate_markdown_optimization_report(analysis_results)

def _generate_markdown_optimization_report(results: Dict[str, Any]) -> str:
    """Generate detailed markdown report for optimization analysis."""
    
    report = []
    
    # Header
    report.append("# Documentation Optimization Analysis Report")
    report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Executive Summary
    report.append("## 📊 Executive Summary")
    report.append("")
    
    if "error" in results:
        report.append(f"❌ **Analysis Failed**: {results['error']}")
        return "\n".join(report)
    
    summary_stats = results.get('summary_statistics', {})
    cross_analysis = results.get('cross_analysis', {})
    optimization_plan = results.get('optimization_plan', {})
    
    total_docs = summary_stats.get('total_documents', 0)
    analysis_time = results.get('analysis_time', 0)
    
    report.append(f"- **Total Documents Analyzed**: {total_docs}")
    report.append(f"- **Analysis Time**: {analysis_time:.2f} seconds")
    report.append(f"- **Average Quality Score**: {summary_stats.get('average_quality_score', 0):.2f}/5")
    report.append(f"- **High Quality Documents**: {summary_stats.get('high_quality_docs', 0)} ({summary_stats.get('high_quality_docs', 0)/max(total_docs, 1)*100:.1f}%)")
    report.append(f"- **Low Quality Documents**: {summary_stats.get('low_quality_docs', 0)} ({summary_stats.get('low_quality_docs', 0)/max(total_docs, 1)*100:.1f}%)")
    report.append(f"- **Total Word Count**: {summary_stats.get('total_word_count', 0):,}")
    report.append(f"- **Total Reading Time**: {summary_stats.get('total_reading_time_hours', 0):.1f} hours")
    report.append("")
    
    # Critical Actions
    immediate_actions = optimization_plan.get('immediate_actions', [])
    deletion_candidates = optimization_plan.get('deletion_candidates', [])
    
    if immediate_actions or deletion_candidates:
        report.append("## 🚨 Critical Actions Required")
        report.append("")
        
        if deletion_candidates:
            report.append(f"### Documents Recommended for Deletion ({len(deletion_candidates)})")
            report.append("")
            for candidate in deletion_candidates[:10]:  # Top 10
                report.append(f"- **`{candidate['path']}`**")
                report.append(f"  - Reason: {candidate['reason']}")
                report.append("")
        
        if immediate_actions:
            report.append(f"### Immediate Actions ({len(immediate_actions)})")
            report.append("")
            for action in immediate_actions[:15]:  # Top 15
                report.append(f"- **`{action['path']}`**")
                report.append(f"  - Action: {action['action']}")
                report.append("")
    
    # Priority Matrix
    priority_matrix = optimization_plan.get('priority_matrix', {})
    
    report.append("## 🎯 Priority Matrix")
    report.append("")
    report.append("Actions categorized by impact and effort:")
    report.append("")
    
    for priority, items in priority_matrix.items():
        if items:
            priority_name = priority.replace('_', ' ').title()
            report.append(f"### {priority_name} ({len(items)} items)")
            report.append("")
            for item in items[:5]:  # Top 5 per category
                path = item.get('path', 'Unknown')
                action = item.get('action', item.get('improvement', 'No description'))
                report.append(f"- `{path}`: {action}")
            if len(items) > 5:
                report.append(f"- ... and {len(items) - 5} more items")
            report.append("")
    
    # Quality Analysis
    report.append("## 📈 Quality Analysis")
    report.append("")
    
    quality_groups = cross_analysis.get('quality_groups', {})
    
    for quality_level in ['low', 'medium', 'high']:
        docs = quality_groups.get(quality_level, [])
        if docs:
            report.append(f"### {quality_level.title()} Quality Documents ({len(docs)})")
            report.append("")
            
            if quality_level == 'low':
                report.append("These documents need immediate attention:")
                report.append("")
                for doc in docs[:10]:
                    path = doc['document_path']
                    quality_scores = doc.get('quality_assessment', {})
                    avg_score = sum([
                        quality_scores.get('content_depth', 3),
                        quality_scores.get('completeness', 3),
                        quality_scores.get('clarity', 3)
                    ]) / 3
                    report.append(f"- `{path}` (Score: {avg_score:.1f}/5)")
                    
                    # Show specific issues
                    issues = doc.get('content_issues', {})
                    for issue_type, issue_list in issues.items():
                        if issue_list:
                            report.append(f"  - {issue_type.replace('_', ' ').title()}: {len(issue_list)} issues")
                    report.append("")
            else:
                # Just list the documents for medium/high quality
                for doc in docs[:5]:
                    report.append(f"- `{doc['document_path']}`")
                if len(docs) > 5:
                    report.append(f"- ... and {len(docs) - 5} more documents")
                report.append("")
    
    # Content Organization Analysis
    report.append("## 📚 Content Organization Analysis")
    report.append("")
    
    subjects_with_multiple = cross_analysis.get('subjects_with_multiple_docs', {})
    content_type_distribution = cross_analysis.get('content_type_distribution', {})
    audience_coverage = cross_analysis.get('audience_coverage', {})
    
    if subjects_with_multiple:
        report.append(f"### Subjects with Multiple Documents ({len(subjects_with_multiple)})")
        report.append("")
        report.append("These subjects have multiple documents that might need consolidation:")
        report.append("")
        
        for subject, docs in list(subjects_with_multiple.items())[:10]:
            report.append(f"**{subject.title()}** ({len(docs)} documents):")
            for doc in docs:
                path = doc['document_path']
                content_type = doc.get('content_analysis', {}).get('content_type', 'unknown')
                report.append(f"- `{path}` ({content_type})")
            report.append("")
    
    report.append("### Content Type Distribution")
    report.append("")
    for content_type, count in sorted(content_type_distribution.items(), key=lambda x: x[1], reverse=True):
        report.append(f"- **{content_type.title()}**: {count} documents")
    report.append("")
    
    report.append("### Audience Coverage")
    report.append("")
    for audience, count in sorted(audience_coverage.items(), key=lambda x: x[1], reverse=True):
        report.append(f"- **{audience.title()}**: {count} documents")
    report.append("")
    
    # Common Issues
    common_issues = cross_analysis.get('common_issues', {})
    
    if common_issues:
        report.append("## ⚠️ Common Issues Across Documentation")
        report.append("")
        
        for issue_type, issues in common_issues.items():
            if issues:
                issue_name = issue_type.replace('_', ' ').title()
                report.append(f"### {issue_name} ({len(issues)} occurrences)")
                report.append("")
                
                # Group by document
                by_doc = {}
                for path, issue in issues:
                    if path not in by_doc:
                        by_doc[path] = []
                    by_doc[path].append(issue)
                
                for path, doc_issues in list(by_doc.items())[:10]:
                    report.append(f"**`{path}`**:")
                    for issue in doc_issues[:3]:  # Top 3 issues per doc
                        report.append(f"- {issue}")
                    if len(doc_issues) > 3:
                        report.append(f"- ... and {len(doc_issues) - 3} more issues")
                    report.append("")
                
                if len(by_doc) > 10:
                    report.append(f"... and {len(by_doc) - 10} more documents with this issue")
                    report.append("")
    
    # Optimization Opportunities
    merge_opportunities = optimization_plan.get('merge_opportunities', [])
    split_recommendations = optimization_plan.get('split_recommendations', [])
    
    if merge_opportunities or split_recommendations:
        report.append("## 🔄 Optimization Opportunities")
        report.append("")
        
        if merge_opportunities:
            report.append(f"### Merge Opportunities ({len(merge_opportunities)})")
            report.append("")
            for opportunity in merge_opportunities[:10]:
                path = opportunity['path']
                candidates = opportunity['candidates']
                report.append(f"**`{path}`** could be merged with:")
                for candidate in candidates[:3]:
                    report.append(f"- {candidate}")
                if len(candidates) > 3:
                    report.append(f"- ... and {len(candidates) - 3} more candidates")
                report.append("")
        
        if split_recommendations:
            report.append(f"### Split Recommendations ({len(split_recommendations)})")
            report.append("")
            for recommendation in split_recommendations[:10]:
                path = recommendation['path']
                recs = recommendation['recommendations']
                report.append(f"**`{path}`** should be split:")
                for rec in recs[:3]:
                    report.append(f"- {rec}")
                if len(recs) > 3:
                    report.append(f"- ... and {len(recs) - 3} more recommendations")
                report.append("")
    
    # Action Plan
    short_term = optimization_plan.get('short_term_improvements', [])
    long_term = optimization_plan.get('long_term_strategy', [])
    
    if short_term or long_term:
        report.append("## 📋 Action Plan")
        report.append("")
        
        if short_term:
            report.append(f"### Short-term Improvements ({len(short_term)})")
            report.append("*Complete within the next month*")
            report.append("")
            for improvement in short_term[:15]:
                path = improvement['path']
                action = improvement['improvement']
                report.append(f"- `{path}`: {action}")
            if len(short_term) > 15:
                report.append(f"- ... and {len(short_term) - 15} more improvements")
            report.append("")
        
        if long_term:
            report.append(f"### Long-term Strategy ({len(long_term)})")
            report.append("*Strategic improvements for the next quarter*")
            report.append("")
            for strategy in long_term[:10]:
                path = strategy['path']
                action = strategy['strategy']
                report.append(f"- `{path}`: {action}")
            if len(long_term) > 10:
                report.append(f"- ... and {len(long_term) - 10} more strategic items")
            report.append("")
    
    # Summary Statistics
    report.append("## 📊 Detailed Statistics")
    report.append("")
    
    computed_issues = summary_stats.get('common_issues', {})
    if computed_issues:
        report.append("### Technical Issues Found")
        report.append("")
        for issue, count in sorted(computed_issues.items(), key=lambda x: x[1], reverse=True):
            issue_name = issue.replace('_', ' ').title()
            report.append(f"- **{issue_name}**: {count} documents")
        report.append("")
    
    report.append("### Content Metrics")
    report.append("")
    report.append(f"- **Average Document Length**: {summary_stats.get('average_word_count', 0):.0f} words")
    report.append(f"- **Average Reading Time**: {summary_stats.get('average_reading_time_minutes', 0):.1f} minutes")
    report.append(f"- **Documents with Issues**: {summary_stats.get('documents_with_issues', 0)}")
    report.append("")
    
    # Recommendations
    report.append("## 💡 Key Recommendations")
    report.append("")
    
    report.append("### Immediate Priorities")
    report.append("1. **Address Critical Issues**: Fix broken links, remove outdated content")
    report.append("2. **Improve Low Quality Documents**: Focus on documents with scores < 3/5")
    report.append("3. **Consolidate Redundant Content**: Merge documents covering the same topics")
    report.append("")
    
    report.append("### Long-term Goals")
    report.append("1. **Standardize Documentation Structure**: Ensure consistent formatting and organization")
    report.append("2. **Improve Content Quality**: Aim for average quality score > 4/5")
    report.append("3. **Optimize for Audience**: Ensure content matches intended audience level")
    report.append("4. **Establish Maintenance Schedule**: Regular reviews based on content type")
    report.append("")
    
    report.append("---")
    report.append("")
    report.append("*This report was generated using comprehensive AI-powered documentation analysis.*")
    
    return "\n".join(report)
