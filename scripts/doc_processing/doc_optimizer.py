#!/usr/bin/env python3
"""
Comprehensive documentation optimization tool.

This tool performs deep analysis of documentation to identify all opportunities
for cleaning, organizing, and optimizing the documentation set.
"""

import json
import os
import argparse
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.analyzers.doc_optimization import analyze_documentation_optimization
from scripts.doc_processing.analyzer.reporting.optimization_report import generate_optimization_report
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
from scripts.doc_processing.analyzer.core.constants import DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the documentation optimizer."""
    parser = argparse.ArgumentParser(
        description='Comprehensive documentation optimization analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic optimization analysis
  python doc_optimizer.py --inventory doc_index.json

  # With custom settings and filters
  python doc_optimizer.py --inventory doc_index.json --max-docs 100 --exclude-dirs tests temp

  # Focus on specific content types
  python doc_optimizer.py --inventory doc_index.json --include-pattern ".*\\.md$"

  # Generate JSON output for further processing
  python doc_optimizer.py --inventory doc_index.json --format json --output optimization_data

  # Verbose analysis with detailed logging
  python doc_optimizer.py --inventory doc_index.json --verbose --max-docs 50
        """
    )
    
    # Input/Output options
    parser.add_argument('--inventory', default='doc_index.json', 
                       help='Path to document inventory JSON file')
    parser.add_argument('--output', default='documentation_optimization_report', 
                       help='Output file base name (without extension)')
    parser.add_argument('--format', choices=['md', 'json'], default='md',
                       help='Output format: markdown or JSON')
    
    # Ollama configuration
    parser.add_argument('--ollama-host', default=DEFAULT_OLLAMA_HOST,
                       help='Ollama server host URL')
    parser.add_argument('--ollama-model', default=DEFAULT_OLLAMA_MODEL,
                       help='Ollama model to use for analysis')
    
    # Analysis parameters
    parser.add_argument('--max-docs', type=int, default=200,
                       help='Maximum number of documents to analyze')
    parser.add_argument('--workers', type=int, default=6,
                       help='Number of parallel workers for analysis')
    parser.add_argument('--timeout', type=int, default=180,
                       help='Timeout per document analysis in seconds')
    
    # Filtering options
    parser.add_argument('--exclude-dirs', nargs='+', default=[],
                       help='Directories to exclude from analysis')
    parser.add_argument('--include-pattern', 
                       help='Only analyze files matching this pattern (regex)')
    parser.add_argument('--exclude-pattern',
                       help='Exclude files matching this pattern (regex)')
    parser.add_argument('--min-size', type=int, default=100,
                       help='Minimum file size in bytes to analyze')
    parser.add_argument('--max-size', type=int, default=1000000,
                       help='Maximum file size in bytes to analyze')
    
    # Analysis focus options
    parser.add_argument('--focus-quality', action='store_true',
                       help='Focus analysis on quality assessment')
    parser.add_argument('--focus-organization', action='store_true',
                       help='Focus analysis on content organization')
    parser.add_argument('--focus-maintenance', action='store_true',
                       help='Focus analysis on maintenance needs')
    
    # Output options
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--quiet', action='store_true',
                       help='Suppress non-error output')
    parser.add_argument('--save-raw-data', action='store_true',
                       help='Save raw analysis data as JSON')
    
    # Robustness options
    parser.add_argument('--retry-failed', action='store_true',
                       help='Retry failed document analyses')
    parser.add_argument('--continue-on-error', action='store_true',
                       help='Continue analysis even if some documents fail')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Process documents in batches of this size')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    elif args.quiet:
        logger.setLevel(logging.ERROR)
    
    # Validate arguments
    if args.max_docs <= 0:
        logger.error("max-docs must be positive")
        return 1
    
    if args.workers <= 0:
        logger.error("workers must be positive")
        return 1
    
    # Check if inventory file exists
    if not os.path.exists(args.inventory):
        logger.error(f"Inventory file not found: {args.inventory}")
        logger.info("Run doc_inventory.py first to generate the inventory file")
        return 1
    
    # Load document inventory
    try:
        logger.info(f"Loading document inventory from {args.inventory}")
        with open(args.inventory, 'r') as f:
            docs_data = json.load(f)
        logger.info(f"Loaded {len(docs_data)} documents")
    except Exception as e:
        logger.error(f"Failed to load inventory: {e}")
        return 1
    
    # Apply filters
    original_count = len(docs_data)
    
    # Size filters
    docs_data = [
        doc for doc in docs_data
        if args.min_size <= doc.get('bytes', 0) <= args.max_size
    ]
    if len(docs_data) != original_count:
        logger.info(f"Size filter: {original_count} -> {len(docs_data)} documents")
    
    # Directory filters
    if args.exclude_dirs:
        docs_data = [
            doc for doc in docs_data
            if not any(excluded_dir in doc['path'] for excluded_dir in args.exclude_dirs)
        ]
        logger.info(f"Directory filter: excluded {original_count - len(docs_data)} documents")
    
    # Pattern filters
    if args.include_pattern:
        import re
        pattern = re.compile(args.include_pattern)
        docs_data = [doc for doc in docs_data if pattern.search(doc['path'])]
        logger.info(f"Include pattern filter: {len(docs_data)} documents match")
    
    if args.exclude_pattern:
        import re
        pattern = re.compile(args.exclude_pattern)
        docs_data = [doc for doc in docs_data if not pattern.search(doc['path'])]
        logger.info(f"Exclude pattern filter: {len(docs_data)} documents remain")
    
    if not docs_data:
        logger.error("No documents remain after filtering")
        return 1
    
    # Initialize document store
    doc_store = CachedDocumentStore()
    
    # Configure analysis options
    options = {
        'ollama_host': args.ollama_host,
        'ollama_model': args.ollama_model,
        'max_docs_for_analysis': min(args.max_docs, len(docs_data)),
        'workers': args.workers,
        'timeout': args.timeout,
        'batch_size': args.batch_size,
        'retry_failed': args.retry_failed,
        'continue_on_error': args.continue_on_error,
        'focus_quality': args.focus_quality,
        'focus_organization': args.focus_organization,
        'focus_maintenance': args.focus_maintenance
    }
    
    logger.info("Starting comprehensive documentation optimization analysis...")
    logger.info(f"Using Ollama: {args.ollama_model} at {args.ollama_host}")
    logger.info(f"Analyzing up to {options['max_docs_for_analysis']} documents")
    logger.info(f"Using {args.workers} parallel workers")
    
    # Run optimization analysis
    try:
        results = analyze_documentation_optimization(docs_data, doc_store, options)
        
        if "error" in results:
            logger.error(f"Analysis failed: {results['error']}")
            return 1
        
        # Save raw data if requested
        if args.save_raw_data:
            raw_data_file = f"{args.output}_raw_data.json"
            with open(raw_data_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Raw analysis data saved to {raw_data_file}")
        
        # Generate report
        logger.info("Generating optimization report...")
        report = generate_optimization_report(results, args.format)
        
        # Write output
        if args.format == 'md':
            output_file = f"{args.output}.md"
        else:
            output_file = f"{args.output}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Optimization report written to {output_file}")
        
        # Print summary
        if not args.quiet:
            summary_stats = results.get('summary_statistics', {})
            optimization_plan = results.get('optimization_plan', {})
            
            print(f"\n📊 Documentation Optimization Summary:")
            print(f"   Documents analyzed: {summary_stats.get('total_documents', 0)}")
            print(f"   Average quality score: {summary_stats.get('average_quality_score', 0):.2f}/5")
            print(f"   High quality documents: {summary_stats.get('high_quality_docs', 0)}")
            print(f"   Low quality documents: {summary_stats.get('low_quality_docs', 0)}")
            print(f"   Total word count: {summary_stats.get('total_word_count', 0):,}")
            
            immediate_actions = len(optimization_plan.get('immediate_actions', []))
            deletion_candidates = len(optimization_plan.get('deletion_candidates', []))
            merge_opportunities = len(optimization_plan.get('merge_opportunities', []))
            
            print(f"\n🎯 Action Items:")
            print(f"   Immediate actions needed: {immediate_actions}")
            print(f"   Deletion candidates: {deletion_candidates}")
            print(f"   Merge opportunities: {merge_opportunities}")
            
            if immediate_actions > 0:
                print(f"\n🚨 Priority: Review immediate actions in the report")
            
            print(f"\n📄 Full report: {output_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
