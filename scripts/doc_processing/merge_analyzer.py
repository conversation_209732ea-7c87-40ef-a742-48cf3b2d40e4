#!/usr/bin/env python3
"""
Dedicated merge analysis tool for identifying documents that should be merged.

This tool performs deep content analysis using local LLMs to identify documents
that cover the same subject with the same approach and could be consolidated.
"""

import json
import os
import argparse
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.analyzers.merge_analysis import analyze_merge_candidates
from scripts.doc_processing.analyzer.reporting.merge_report import generate_merge_report
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
from scripts.doc_processing.analyzer.core.constants import DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the merge analyzer."""
    parser = argparse.ArgumentParser(
        description='Deep content analysis for identifying merge candidates',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic merge analysis
  python merge_analyzer.py --inventory doc_index.json

  # With custom Ollama settings
  python merge_analyzer.py --inventory doc_index.json --ollama-model llama3.1 --similarity-threshold 0.8

  # Limit analysis to specific number of documents
  python merge_analyzer.py --inventory doc_index.json --max-docs 50

  # Generate JSON output
  python merge_analyzer.py --inventory doc_index.json --format json
        """
    )
    
    parser.add_argument('--inventory', default='doc_index.json', 
                       help='Path to document inventory JSON file')
    parser.add_argument('--output', default='merge_analysis_report', 
                       help='Output file base name (without extension)')
    parser.add_argument('--format', choices=['md', 'json'], default='md',
                       help='Output format: markdown or JSON')
    
    # Ollama configuration
    parser.add_argument('--ollama-host', default=DEFAULT_OLLAMA_HOST,
                       help='Ollama server host URL')
    parser.add_argument('--ollama-model', default=DEFAULT_OLLAMA_MODEL,
                       help='Ollama model to use for analysis')
    
    # Analysis parameters
    parser.add_argument('--similarity-threshold', type=float, default=0.7,
                       help='Similarity threshold for merge candidates (0.0-1.0)')
    parser.add_argument('--max-docs', type=int, default=100,
                       help='Maximum number of documents to analyze (for performance)')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of parallel workers for analysis')
    
    # Filtering options
    parser.add_argument('--exclude-dirs', nargs='+', default=[],
                       help='Directories to exclude from analysis')
    parser.add_argument('--include-pattern', 
                       help='Only analyze files matching this pattern (regex)')
    parser.add_argument('--exclude-pattern',
                       help='Exclude files matching this pattern (regex)')
    
    # Output options
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--quiet', action='store_true',
                       help='Suppress non-error output')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    elif args.quiet:
        logger.setLevel(logging.ERROR)
    
    # Check if inventory file exists
    if not os.path.exists(args.inventory):
        logger.error(f"Inventory file not found: {args.inventory}")
        logger.info("Run doc_inventory.py first to generate the inventory file")
        return 1
    
    # Load document inventory
    try:
        logger.info(f"Loading document inventory from {args.inventory}")
        with open(args.inventory, 'r') as f:
            docs_data = json.load(f)
        logger.info(f"Loaded {len(docs_data)} documents")
    except Exception as e:
        logger.error(f"Failed to load inventory: {e}")
        return 1
    
    # Filter documents if requested
    if args.exclude_dirs:
        original_count = len(docs_data)
        docs_data = [
            doc for doc in docs_data
            if not any(excluded_dir in doc['path'] for excluded_dir in args.exclude_dirs)
        ]
        logger.info(f"Excluded {original_count - len(docs_data)} documents from excluded directories")
    
    if args.include_pattern:
        import re
        pattern = re.compile(args.include_pattern)
        original_count = len(docs_data)
        docs_data = [doc for doc in docs_data if pattern.search(doc['path'])]
        logger.info(f"Filtered to {len(docs_data)} documents matching include pattern")
    
    if args.exclude_pattern:
        import re
        pattern = re.compile(args.exclude_pattern)
        original_count = len(docs_data)
        docs_data = [doc for doc in docs_data if not pattern.search(doc['path'])]
        logger.info(f"Excluded {original_count - len(docs_data)} documents matching exclude pattern")
    
    # Initialize document store
    doc_store = CachedDocumentStore()
    
    # Configure analysis options
    options = {
        'ollama_host': args.ollama_host,
        'ollama_model': args.ollama_model,
        'merge_similarity_threshold': args.similarity_threshold,
        'max_docs_for_deep_analysis': args.max_docs,
        'workers': args.workers
    }
    
    logger.info("Starting merge analysis...")
    logger.info(f"Using Ollama: {args.ollama_model} at {args.ollama_host}")
    logger.info(f"Similarity threshold: {args.similarity_threshold}")
    logger.info(f"Max documents: {args.max_docs}")
    
    # Run merge analysis
    try:
        results = analyze_merge_candidates(docs_data, doc_store, options)
        
        if "error" in results:
            logger.error(f"Analysis failed: {results['error']}")
            return 1
        
        # Generate report
        logger.info("Generating report...")
        report = generate_merge_report(results, args.format)
        
        # Write output
        if args.format == 'md':
            output_file = f"{args.output}.md"
        else:
            output_file = f"{args.output}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Report written to {output_file}")
        
        # Print summary
        if not args.quiet:
            total_groups = results.get('total_merge_groups', 0)
            total_pairs = results.get('total_merge_pairs', 0)
            docs_analyzed = results.get('documents_analyzed', 0)
            
            print(f"\n📊 Merge Analysis Summary:")
            print(f"   Documents analyzed: {docs_analyzed}")
            print(f"   Merge groups found: {total_groups}")
            print(f"   Potential merge pairs: {total_pairs}")
            
            if total_pairs > 0:
                print(f"\n🔥 Top merge recommendations:")
                potential_merges = results.get('potential_merges', [])
                for i, merge in enumerate(potential_merges[:3], 1):
                    print(f"   {i}. {merge['subject']} ({merge['approach']}) - Score: {merge['priority']:.2f}")
                    print(f"      • {merge['doc1_path']}")
                    print(f"      • {merge['doc2_path']}")
            else:
                print(f"\n✅ No merge candidates found - documentation is well-organized!")
            
            print(f"\n📄 Full report: {output_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
