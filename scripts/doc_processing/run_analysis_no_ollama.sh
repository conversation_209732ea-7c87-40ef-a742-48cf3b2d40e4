#!/bin/bash

# <PERSON><PERSON>t to run the document analysis process without <PERSON><PERSON><PERSON>
# This script should be executed from the root of the repository,
# or it will navigate to the root assuming it's in scripts/doc_processing/

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )" # Navigate two levels up to the repo root

echo "Changing to repository root: $REPO_ROOT_DIR"
cd "$REPO_ROOT_DIR" || exit 1

echo "Running Document Analysis (without Ollama)..."
python3 -m scripts.doc_processing.inventory_analyzer \
    --inventory doc_index.json \
    --output analysis_report_no_ollama \
    --format md \
    --visualize \
    --use-default-excludes

echo "Document analysis script finished."
echo "Check for analysis_report_no_ollama.md and the 'visualizations' directory in the repository root."
