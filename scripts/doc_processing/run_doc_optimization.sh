#!/bin/bash

# <PERSON>ript to run comprehensive documentation optimization analysis
# This script should be executed from the root of the repository,
# or it will navigate to the root assuming it's in scripts/doc_processing/

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )" # Navigate two levels up to the repo root

echo "Changing to repository root: $REPO_ROOT_DIR"
cd "$REPO_ROOT_DIR" || exit 1

echo "🚀 Starting Comprehensive Documentation Optimization Analysis"
echo "=============================================================="
echo ""
echo "This analysis will:"
echo "• Perform deep content analysis of each document"
echo "• Identify quality issues and improvement opportunities"
echo "• Suggest merges, splits, and deletions"
echo "• Provide actionable optimization recommendations"
echo "• Generate a comprehensive optimization report"
echo ""

# Check if doc_index.json exists
if [ ! -f "doc_index.json" ]; then
    echo "❌ doc_index.json not found. Running document inventory first..."
    python3 -m scripts.doc_processing.doc_inventory
    echo ""
fi

# Check if Ollama is running
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "⚠️  Warning: Ollama doesn't appear to be running at localhost:11434"
    echo "   Please start Ollama with: ollama serve"
    echo "   And ensure you have a model installed: ollama pull mistral"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Get user preferences
echo "📋 Configuration Options:"
echo ""

# Ask for analysis scope
read -p "Maximum documents to analyze (default: 100, max recommended: 200): " MAX_DOCS
MAX_DOCS=${MAX_DOCS:-100}

# Ask for focus areas
echo ""
echo "Focus areas (you can select multiple):"
read -p "Focus on quality assessment? (y/N): " FOCUS_QUALITY
read -p "Focus on content organization? (y/N): " FOCUS_ORG
read -p "Focus on maintenance needs? (y/N): " FOCUS_MAINT

# Build command
CMD="python3 -m scripts.doc_processing.doc_optimizer"
CMD="$CMD --inventory doc_index.json"
CMD="$CMD --output documentation_optimization_report"
CMD="$CMD --format md"
CMD="$CMD --max-docs $MAX_DOCS"
CMD="$CMD --ollama-model mistral"
CMD="$CMD --workers 6"
CMD="$CMD --save-raw-data"

# Add focus options
if [[ $FOCUS_QUALITY =~ ^[Yy]$ ]]; then
    CMD="$CMD --focus-quality"
fi
if [[ $FOCUS_ORG =~ ^[Yy]$ ]]; then
    CMD="$CMD --focus-organization"
fi
if [[ $FOCUS_MAINT =~ ^[Yy]$ ]]; then
    CMD="$CMD --focus-maintenance"
fi

echo ""
echo "🔄 Starting analysis with the following configuration:"
echo "   Max documents: $MAX_DOCS"
echo "   Model: mistral"
echo "   Workers: 6"
echo "   Output: documentation_optimization_report.md"
echo ""

# Run the analysis
eval $CMD

EXIT_CODE=$?

echo ""
if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Documentation optimization analysis completed successfully!"
    echo ""
    echo "📄 Reports generated:"
    echo "   • documentation_optimization_report.md - Main optimization report"
    echo "   • documentation_optimization_report_raw_data.json - Raw analysis data"
    echo ""
    echo "🎯 Next Steps:"
    echo "   1. Review the 'Critical Actions Required' section first"
    echo "   2. Address immediate actions and deletion candidates"
    echo "   3. Plan short-term improvements for next month"
    echo "   4. Consider long-term strategic changes"
    echo ""
    echo "💡 Pro Tips:"
    echo "   • Start with high-impact, low-effort improvements"
    echo "   • Focus on documents with quality scores < 3/5"
    echo "   • Use the priority matrix to guide your efforts"
    echo "   • Consider automating common fixes"
else
    echo "❌ Analysis failed with exit code $EXIT_CODE"
    echo "   Check the logs above for error details"
    echo "   Common issues:"
    echo "   • Ollama not running or model not available"
    echo "   • Insufficient memory for large document sets"
    echo "   • Network connectivity issues"
    echo ""
    echo "💡 Troubleshooting:"
    echo "   • Try reducing --max-docs to 50 or fewer"
    echo "   • Ensure Ollama is running: ollama serve"
    echo "   • Check available models: ollama list"
    echo "   • Run with --verbose for detailed logging"
fi
