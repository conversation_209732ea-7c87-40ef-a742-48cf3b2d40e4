#!/bin/bash

# <PERSON>ript to run the merge analysis for identifying documents that should be merged
# This script should be executed from the root of the repository,
# or it will navigate to the root assuming it's in scripts/doc_processing/

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )" # Navigate two levels up to the repo root

echo "Changing to repository root: $REPO_ROOT_DIR"
cd "$REPO_ROOT_DIR" || exit 1

echo "Running Deep Content Analysis for Merge Candidates..."
echo "This analysis uses Ollama to perform deep content analysis and identify"
echo "documents that cover the same subject with the same approach."
echo ""

# Check if doc_index.json exists
if [ ! -f "doc_index.json" ]; then
    echo "❌ doc_index.json not found. Running document inventory first..."
    python3 -m scripts.doc_processing.doc_inventory
    echo ""
fi

# Check if <PERSON>lla<PERSON> is running
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "⚠️  Warning: Ollama doesn't appear to be running at localhost:11434"
    echo "   Please start Ollama with: ollama serve"
    echo "   And ensure you have a model installed: ollama pull mistral"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "Starting merge analysis..."
python3 -m scripts.doc_processing.merge_analyzer \
    --inventory doc_index.json \
    --output merge_analysis_report \
    --format md \
    --similarity-threshold 0.7 \
    --max-docs 100 \
    --ollama-model mistral

echo ""
echo "Merge analysis completed!"
echo "Check for merge_analysis_report.md in the repository root."
echo ""
echo "💡 Tips:"
echo "   - Review the priority merge recommendations first"
echo "   - Start with high-priority pairs (score > 0.8)"
echo "   - Consider the unique value of each document before merging"
echo "   - Update internal links after merging documents"
