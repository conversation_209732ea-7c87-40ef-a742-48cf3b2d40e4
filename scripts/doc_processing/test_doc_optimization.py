#!/usr/bin/env python3
"""
Test script for the comprehensive documentation optimization functionality.
"""

import json
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.analyzers.doc_optimization import analyze_documentation_optimization
from scripts.doc_processing.analyzer.reporting.optimization_report import generate_optimization_report
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_comprehensive_optimization():
    """Test comprehensive optimization analysis with sample documents."""
    logger.info("Testing comprehensive documentation optimization...")
    
    # Sample documents with various quality levels and issues
    sample_docs = [
        {
            "path": "docs/auth/oauth-complete-guide.md",
            "title": "Complete OAuth 2.0 Implementation Guide",
            "size": 5000,
            "content": """
# Complete OAuth 2.0 Implementation Guide

This comprehensive guide covers everything you need to know about implementing OAuth 2.0 authentication in your applications.

## Table of Contents
1. Introduction to OAuth 2.0
2. Setting up OAuth
3. Implementation Examples
4. Security Best Practices
5. Troubleshooting

## Introduction to OAuth 2.0

OAuth 2.0 is an authorization framework that enables applications to obtain limited access to user accounts on an HTTP service. It works by delegating user authentication to the service that hosts the user account.

## Setting up OAuth

### Prerequisites
- A registered application
- Client ID and secret
- Redirect URI configuration

### Step-by-Step Setup
1. Register your application with the OAuth provider
2. Configure your redirect URIs
3. Implement the authorization flow
4. Handle token exchange

## Implementation Examples

Here are practical examples for different programming languages:

### Python Example
```python
import requests

def get_access_token(code):
    # Implementation here
    pass
```

### JavaScript Example
```javascript
function handleOAuthCallback(code) {
    // Implementation here
}
```

## Security Best Practices

- Always use HTTPS
- Validate redirect URIs
- Implement proper token storage
- Handle token expiration
- Use state parameters to prevent CSRF

## Troubleshooting

Common issues and solutions:
- Invalid redirect URI
- Token expiration handling
- Scope permissions
            """
        },
        {
            "path": "docs/auth/oauth-quick-start.md", 
            "title": "OAuth Quick Start",
            "size": 800,
            "content": """
# OAuth Quick Start

TODO: This document needs to be completed.

## What is OAuth?

OAuth is for authentication. It's secure.

## How to use it

1. Get credentials
2. Make requests
3. ???
4. Profit

FIXME: Add proper examples and explanations.

This document is incomplete and needs work.
            """
        },
        {
            "path": "docs/api/authentication-methods.md",
            "title": "API Authentication Methods Overview",
            "size": 3000,
            "content": """
# API Authentication Methods Overview

Our API supports several authentication methods to suit different use cases and security requirements.

## Supported Methods

### 1. OAuth 2.0 (Recommended)
The most secure method for production applications. Provides token-based authentication with proper scope management.

### 2. API Keys
Simple authentication suitable for server-to-server communication. Less secure than OAuth but easier to implement.

### 3. Basic Authentication
Legacy method using username/password. Not recommended for production use due to security concerns.

### 4. JWT Tokens
JSON Web Tokens for stateless authentication. Good for microservices architectures.

## Choosing the Right Method

- **OAuth 2.0**: Use for user-facing applications requiring secure access
- **API Keys**: Use for internal services and simple integrations
- **JWT**: Use for distributed systems and microservices
- **Basic Auth**: Avoid in production, use only for testing

## Implementation Guides

Each authentication method has detailed implementation guides:
- [OAuth 2.0 Complete Guide](oauth-complete-guide.md)
- [API Keys Documentation](api-keys.md)
- [JWT Implementation](jwt-auth.md)

## Security Considerations

Always follow these security practices:
- Use HTTPS for all authentication flows
- Implement proper token storage
- Regular security audits
- Monitor for suspicious activity
            """
        },
        {
            "path": "docs/tutorials/outdated-tutorial.md",
            "title": "Getting Started with API v1 (Deprecated)",
            "size": 2000,
            "content": """
# Getting Started with API v1 (Deprecated)

⚠️ WARNING: This tutorial is for API v1 which was deprecated in 2019.

## Introduction

This tutorial shows how to use our old API from 2018. The examples use Python 2.7 and jQuery 1.x.

## Setup

First, install the old dependencies:
```bash
pip install requests==2.18.0
```

## Making Requests

Use this old pattern:
```python
import urllib2
response = urllib2.urlopen('http://api.example.com/v1/data')
```

## Authentication

Use the old authentication method:
```javascript
$.ajax({
    url: 'http://api.example.com/v1/auth',
    type: 'POST',
    // ... old jQuery patterns
});
```

This document should probably be deleted or completely rewritten for the current API.
            """
        },
        {
            "path": "docs/reference/api-endpoints.md",
            "title": "API Endpoints Reference",
            "size": 4500,
            "content": """
# API Endpoints Reference

Complete reference for all available API endpoints.

## Authentication Endpoints

### POST /auth/oauth/token
Exchange authorization code for access token.

**Parameters:**
- `grant_type`: Authorization grant type
- `code`: Authorization code
- `client_id`: Application client ID
- `client_secret`: Application client secret
- `redirect_uri`: Redirect URI

**Response:**
```json
{
  "access_token": "string",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "string"
}
```

### POST /auth/refresh
Refresh an expired access token.

**Parameters:**
- `grant_type`: Must be "refresh_token"
- `refresh_token`: Valid refresh token

## User Endpoints

### GET /users/me
Get current user information.

**Headers:**
- `Authorization`: Bearer {access_token}

**Response:**
```json
{
  "id": "string",
  "email": "string",
  "name": "string",
  "created_at": "2023-01-01T00:00:00Z"
}
```

### PUT /users/me
Update current user information.

**Headers:**
- `Authorization`: Bearer {access_token}

**Body:**
```json
{
  "name": "string",
  "email": "string"
}
```

## Data Endpoints

### GET /data
Retrieve data with optional filtering.

**Query Parameters:**
- `limit`: Number of results (default: 50, max: 100)
- `offset`: Pagination offset
- `filter`: Filter criteria

**Response:**
```json
{
  "data": [],
  "total": 0,
  "limit": 50,
  "offset": 0
}
```

This reference is comprehensive and well-structured.
            """
        }
    ]
    
    # Create a simple document store
    doc_store = CachedDocumentStore()
    
    # Add sample content to the store
    for doc in sample_docs:
        doc_store._content_cache[doc["path"]] = doc["content"]
    
    # Test the optimization analysis
    options = {
        'ollama_host': 'http://localhost:11434',
        'ollama_model': 'mistral',
        'max_docs_for_analysis': 10,
        'workers': 2,
        'timeout': 120
    }
    
    try:
        # Test Ollama availability first
        ollama_client = OllamaClient()
        if not ollama_client.is_available():
            logger.warning("Ollama not available - this test requires Ollama to be running")
            logger.info("Please start Ollama with: ollama serve")
            logger.info("And ensure you have a model: ollama pull mistral")
            return False
        
        logger.info("✓ Ollama is available, running comprehensive optimization analysis...")
        
        results = analyze_documentation_optimization(sample_docs, doc_store, options)
        
        if "error" in results:
            logger.error(f"✗ Optimization analysis failed: {results['error']}")
            return False
        
        logger.info("✓ Comprehensive optimization analysis completed successfully!")
        
        # Display results
        summary_stats = results.get('summary_statistics', {})
        optimization_plan = results.get('optimization_plan', {})
        cross_analysis = results.get('cross_analysis', {})
        
        logger.info(f"  - Documents analyzed: {summary_stats.get('total_documents', 0)}")
        logger.info(f"  - Average quality score: {summary_stats.get('average_quality_score', 0):.2f}/5")
        logger.info(f"  - High quality docs: {summary_stats.get('high_quality_docs', 0)}")
        logger.info(f"  - Low quality docs: {summary_stats.get('low_quality_docs', 0)}")
        logger.info(f"  - Total word count: {summary_stats.get('total_word_count', 0):,}")
        
        # Show optimization opportunities
        immediate_actions = optimization_plan.get('immediate_actions', [])
        deletion_candidates = optimization_plan.get('deletion_candidates', [])
        merge_opportunities = optimization_plan.get('merge_opportunities', [])
        
        logger.info(f"\n🎯 Optimization Opportunities:")
        logger.info(f"  - Immediate actions: {len(immediate_actions)}")
        logger.info(f"  - Deletion candidates: {len(deletion_candidates)}")
        logger.info(f"  - Merge opportunities: {len(merge_opportunities)}")
        
        if deletion_candidates:
            logger.info(f"\n🗑️  Deletion Candidates:")
            for candidate in deletion_candidates[:3]:
                logger.info(f"    • {candidate['path']}: {candidate['reason'][:100]}...")
        
        if immediate_actions:
            logger.info(f"\n⚡ Immediate Actions:")
            for action in immediate_actions[:3]:
                logger.info(f"    • {action['path']}: {action['action'][:100]}...")
        
        # Show cross-analysis insights
        subjects_with_multiple = cross_analysis.get('subjects_with_multiple_docs', {})
        if subjects_with_multiple:
            logger.info(f"\n📚 Subjects with Multiple Documents:")
            for subject, docs in list(subjects_with_multiple.items())[:3]:
                logger.info(f"    • {subject}: {len(docs)} documents")
        
        # Test report generation
        logger.info("\nTesting report generation...")
        report = generate_optimization_report(results, "markdown")
        
        # Save test report
        with open("test_optimization_report.md", "w") as f:
            f.write(report)
        
        logger.info("✓ Optimization report generated successfully: test_optimization_report.md")
        
        # Show sample analysis
        document_analyses = results.get('document_analyses', [])
        if document_analyses:
            logger.info("\n📋 Sample Document Analysis:")
            sample_analysis = document_analyses[0]
            logger.info(f"  Document: {sample_analysis.get('document_path')}")
            
            content_analysis = sample_analysis.get('content_analysis', {})
            logger.info(f"  Subject: {content_analysis.get('main_subject')}")
            logger.info(f"  Content Type: {content_analysis.get('content_type')}")
            logger.info(f"  Purpose: {content_analysis.get('document_purpose', '')[:100]}...")
            
            quality_assessment = sample_analysis.get('quality_assessment', {})
            logger.info(f"  Quality Scores:")
            logger.info(f"    - Depth: {quality_assessment.get('content_depth')}/5")
            logger.info(f"    - Completeness: {quality_assessment.get('completeness')}/5")
            logger.info(f"    - Clarity: {quality_assessment.get('clarity')}/5")
            logger.info(f"    - Usefulness: {quality_assessment.get('usefulness')}/5")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Optimization analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🚀 Testing Comprehensive Documentation Optimization")
    logger.info("=" * 65)
    
    success = test_comprehensive_optimization()
    
    logger.info("")
    logger.info("=" * 65)
    
    if success:
        logger.info("🎉 All tests passed! Documentation optimization is working correctly.")
        logger.info("\n💡 To use the documentation optimizer:")
        logger.info("   1. Ensure Ollama is running: ollama serve")
        logger.info("   2. Run: ./scripts/doc_processing/run_doc_optimization.sh")
        logger.info("   3. Or: python scripts/doc_processing/doc_optimizer.py --inventory doc_index.json")
        logger.info("\n📊 The optimizer provides:")
        logger.info("   • Deep content analysis of each document")
        logger.info("   • Quality assessment and improvement suggestions")
        logger.info("   • Merge, split, and deletion recommendations")
        logger.info("   • Cross-document relationship analysis")
        logger.info("   • Actionable optimization plans")
    else:
        logger.error("❌ Tests failed. Check the logs above for details.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
