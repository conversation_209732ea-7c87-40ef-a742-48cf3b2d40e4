#!/usr/bin/env python3
"""
Test script for the enhanced topic analysis functionality.
"""

import json
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ollama_connection():
    """Test if Ollama is available and working."""
    logger.info("Testing Ollama connection...")
    
    try:
        client = OllamaClient()
        if client.is_available():
            logger.info(f"✓ Ollama is available with model {client.model}")
            
            # Test a simple completion
            response = client.generate_completion(
                "What is the capital of France?",
                "You are a helpful assistant. Answer briefly."
            )
            
            if response:
                logger.info(f"✓ Ollama test completion: {response[:100]}...")
                return True
            else:
                logger.warning("✗ Ollama completion failed")
                return False
        else:
            logger.warning("✗ Ollama is not available")
            return False
            
    except Exception as e:
        logger.error(f"✗ Ollama test failed: {e}")
        return False

def test_topic_extraction():
    """Test topic extraction on sample documents."""
    logger.info("Testing topic extraction...")
    
    # Sample documents for testing
    sample_docs = [
        {
            "path": "docs/api/authentication.md",
            "title": "API Authentication Guide",
            "content": """
# API Authentication Guide

This document explains how to authenticate with our API using various methods.

## OAuth 2.0 Flow

OAuth 2.0 is the recommended authentication method for production applications.

### Setup
1. Register your application
2. Get client credentials
3. Implement the OAuth flow

## API Keys

For simple integrations, you can use API keys.

### Security Best Practices
- Never expose API keys in client-side code
- Rotate keys regularly
- Use environment variables
            """
        },
        {
            "path": "docs/tutorials/getting-started.md", 
            "title": "Getting Started Tutorial",
            "content": """
# Getting Started with Our Platform

Welcome! This tutorial will walk you through the basics.

## Prerequisites
- Basic programming knowledge
- A development environment

## Step 1: Installation
Install the SDK using your package manager.

## Step 2: Authentication
Set up authentication using the methods described in our API documentation.

## Step 3: Your First Request
Make your first API call to test the connection.
            """
        },
        {
            "path": "docs/concepts/architecture.md",
            "title": "System Architecture Overview", 
            "content": """
# System Architecture Overview

This document provides a high-level view of our system architecture.

## Core Components

### API Gateway
The API gateway handles all incoming requests and routes them appropriately.

### Authentication Service
Centralized authentication and authorization.

### Data Layer
Our data persistence and caching strategies.

## Design Principles
- Microservices architecture
- Event-driven communication
- Horizontal scalability
            """
        }
    ]
    
    # Create a simple document store
    doc_store = CachedDocumentStore()
    
    # Add sample content to the store
    for doc in sample_docs:
        doc_store.content_cache[doc["path"]] = doc["content"]
    
    # Test the enhanced analysis
    options = {
        'workers': 2,
        'use_ollama': True,
        'ollama_host': 'http://localhost:11434',
        'ollama_model': 'mistral'
    }
    
    try:
        results = analyze_similar_content(sample_docs, doc_store, options)
        
        logger.info("✓ Enhanced analysis completed successfully!")
        logger.info(f"  - Found {results['count']} similar document pairs")
        logger.info(f"  - Analyzed {results['documents_analyzed']} documents")
        logger.info(f"  - Documents with topics: {results['documents_with_topics']}")
        logger.info(f"  - Ollama used: {results['ollama_used']}")
        
        # Display topic coverage analysis
        topic_coverage = results.get('topic_coverage', {})
        if topic_coverage:
            logger.info("\n📊 Topic Coverage Analysis:")
            logger.info(f"  - Total unique topics: {topic_coverage.get('total_unique_topics', 0)}")
            logger.info(f"  - Well-covered topics: {len(topic_coverage.get('well_covered_topics', []))}")
            logger.info(f"  - Topic coverage gaps: {len(topic_coverage.get('topic_coverage_gaps', []))}")
            
            # Show angle distribution
            angle_dist = topic_coverage.get('angle_distribution', {})
            if angle_dist:
                logger.info("  - Document angles:")
                for angle, count in angle_dist.items():
                    logger.info(f"    * {angle}: {count} documents")
        
        # Display similar pairs with topic information
        if results['similar_pairs']:
            logger.info("\n🔗 Similar Document Pairs:")
            for i, pair in enumerate(results['similar_pairs'][:3]):  # Show first 3
                logger.info(f"  Pair {i+1}:")
                logger.info(f"    Doc 1: {pair['doc1']['title']} (angle: {pair.get('doc1_angle', 'unknown')})")
                logger.info(f"    Doc 2: {pair['doc2']['title']} (angle: {pair.get('doc2_angle', 'unknown')})")
                logger.info(f"    Similarity: {pair['similarity']:.3f}")
                if pair.get('doc1_topics') and pair.get('doc2_topics'):
                    logger.info(f"    Topics 1: {', '.join(pair['doc1_topics'])}")
                    logger.info(f"    Topics 2: {', '.join(pair['doc2_topics'])}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Enhanced analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🚀 Testing Enhanced Document Analysis")
    logger.info("=" * 50)
    
    # Test 1: Ollama connection
    ollama_available = test_ollama_connection()
    
    logger.info("")
    logger.info("-" * 50)
    
    # Test 2: Topic extraction and analysis
    analysis_success = test_topic_extraction()
    
    logger.info("")
    logger.info("=" * 50)
    
    if ollama_available and analysis_success:
        logger.info("🎉 All tests passed! Enhanced analysis is working.")
    elif analysis_success:
        logger.info("⚠️  Analysis works but Ollama is not available. Using fallback methods.")
    else:
        logger.error("❌ Tests failed. Check the logs above for details.")
        return 1
    
    logger.info("\n💡 To use the enhanced analysis:")
    logger.info("   1. Make sure Ollama is running: ollama serve")
    logger.info("   2. Pull a model: ollama pull mistral")
    logger.info("   3. Run the analysis with --use-ollama option")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
