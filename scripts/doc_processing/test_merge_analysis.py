#!/usr/bin/env python3
"""
Test script for the merge analysis functionality.
"""

import json
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.analyzers.merge_analysis import analyze_merge_candidates
from scripts.doc_processing.analyzer.reporting.merge_report import generate_merge_report
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_merge_analysis():
    """Test merge analysis with sample documents."""
    logger.info("Testing merge analysis functionality...")
    
    # Sample documents that should be identified as merge candidates
    sample_docs = [
        {
            "path": "docs/auth/oauth-guide.md",
            "title": "OAuth 2.0 Authentication Guide",
            "content": """
# OAuth 2.0 Authentication Guide

This guide explains how to implement OAuth 2.0 authentication in your application.

## Overview
OAuth 2.0 is an authorization framework that enables applications to obtain limited access to user accounts.

## Implementation Steps
1. Register your application
2. Configure OAuth endpoints
3. Handle the authorization flow
4. Manage access tokens

## Security Best Practices
- Use HTTPS for all OAuth flows
- Validate redirect URIs
- Implement proper token storage
- Handle token expiration gracefully

## Code Examples
Here are some basic implementation examples for common scenarios.
            """
        },
        {
            "path": "docs/auth/oauth-tutorial.md", 
            "title": "OAuth Implementation Tutorial",
            "content": """
# OAuth Implementation Tutorial

Learn how to implement OAuth 2.0 step by step.

## What is OAuth?
OAuth 2.0 is a protocol that allows applications to access user data without exposing passwords.

## Step-by-Step Implementation
1. Set up your OAuth application
2. Configure authorization endpoints
3. Implement the OAuth flow
4. Handle access tokens and refresh tokens

## Security Considerations
- Always use HTTPS
- Validate all redirect URIs
- Store tokens securely
- Implement token refresh logic

## Example Code
This tutorial includes practical examples for implementing OAuth in your application.
            """
        },
        {
            "path": "docs/api/authentication.md",
            "title": "API Authentication Methods", 
            "content": """
# API Authentication Methods

Our API supports multiple authentication methods for different use cases.

## OAuth 2.0
The recommended method for production applications. Provides secure, token-based authentication.

## API Keys
Simple authentication method suitable for server-to-server communication.

## Basic Authentication
Legacy method, not recommended for production use.

## JWT Tokens
JSON Web Tokens for stateless authentication.

## Implementation Guide
Choose the authentication method that best fits your use case and follow our implementation guides.
            """
        },
        {
            "path": "docs/tutorials/getting-started.md",
            "title": "Getting Started with Our Platform",
            "content": """
# Getting Started with Our Platform

Welcome to our platform! This guide will help you get up and running quickly.

## Prerequisites
- Basic programming knowledge
- A development environment
- An account on our platform

## Quick Start
1. Create an account
2. Generate API credentials
3. Install our SDK
4. Make your first API call

## Next Steps
After completing this tutorial, explore our advanced guides and API documentation.

## Authentication
You'll need to authenticate your requests. We support OAuth 2.0 and API keys.
            """
        },
        {
            "path": "docs/concepts/security-overview.md",
            "title": "Security Architecture Overview",
            "content": """
# Security Architecture Overview

This document provides a high-level overview of our security architecture and principles.

## Core Security Principles
- Defense in depth
- Principle of least privilege
- Zero trust architecture
- Continuous monitoring

## Authentication & Authorization
We use modern authentication protocols including OAuth 2.0 and JWT tokens.

## Data Protection
All data is encrypted in transit and at rest using industry-standard encryption.

## Compliance
Our platform meets various compliance standards including SOC 2 and GDPR.

## Security Best Practices
Follow these guidelines to ensure your integration is secure.
            """
        }
    ]
    
    # Create a simple document store
    doc_store = CachedDocumentStore()
    
    # Add sample content to the store
    for doc in sample_docs:
        doc_store._content_cache[doc["path"]] = doc["content"]
    
    # Test the merge analysis
    options = {
        'ollama_host': 'http://localhost:11434',
        'ollama_model': 'mistral',
        'merge_similarity_threshold': 0.6,  # Lower threshold for testing
        'max_docs_for_deep_analysis': 10,
        'workers': 2
    }
    
    try:
        # Test Ollama availability first
        ollama_client = OllamaClient()
        if not ollama_client.is_available():
            logger.warning("Ollama not available - this test requires Ollama to be running")
            logger.info("Please start Ollama with: ollama serve")
            logger.info("And ensure you have a model: ollama pull mistral")
            return False
        
        logger.info("✓ Ollama is available, running merge analysis...")
        
        results = analyze_merge_candidates(sample_docs, doc_store, options)
        
        if "error" in results:
            logger.error(f"✗ Merge analysis failed: {results['error']}")
            return False
        
        logger.info("✓ Merge analysis completed successfully!")
        
        # Display results
        total_groups = results.get('total_merge_groups', 0)
        total_pairs = results.get('total_merge_pairs', 0)
        docs_analyzed = results.get('documents_analyzed', 0)
        
        logger.info(f"  - Documents analyzed: {docs_analyzed}")
        logger.info(f"  - Merge groups found: {total_groups}")
        logger.info(f"  - Potential merge pairs: {total_pairs}")
        
        # Show merge candidates
        if total_pairs > 0:
            logger.info("\n🔗 Merge Candidates Found:")
            potential_merges = results.get('potential_merges', [])
            for i, merge in enumerate(potential_merges[:3], 1):
                logger.info(f"  {i}. Priority: {merge['priority']:.2f}")
                logger.info(f"     Subject: {merge['subject']}")
                logger.info(f"     Approach: {merge['approach']}")
                logger.info(f"     Doc 1: {merge['doc1_path']}")
                logger.info(f"     Doc 2: {merge['doc2_path']}")
                logger.info(f"     Overlap: {merge['overlap_score']:.2f}")
                logger.info("")
        
        # Test report generation
        logger.info("Testing report generation...")
        report = generate_merge_report(results, "markdown")
        
        # Save test report
        with open("test_merge_report.md", "w") as f:
            f.write(report)
        
        logger.info("✓ Report generated successfully: test_merge_report.md")
        
        # Show content analysis sample
        content_analyses = results.get('content_analyses', [])
        if content_analyses:
            logger.info("\n📋 Sample Content Analysis:")
            sample_analysis = content_analyses[0]
            logger.info(f"  Document: {sample_analysis.get('document_path')}")
            logger.info(f"  Main Subject: {sample_analysis.get('main_subject')}")
            logger.info(f"  Approach: {sample_analysis.get('approach')}")
            logger.info(f"  Content Type: {sample_analysis.get('content_type')}")
            logger.info(f"  Completeness: {sample_analysis.get('completeness')}/5")
            logger.info(f"  Quality: {sample_analysis.get('content_quality')}/5")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Merge analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🚀 Testing Deep Content Analysis for Merge Candidates")
    logger.info("=" * 60)
    
    success = test_merge_analysis()
    
    logger.info("")
    logger.info("=" * 60)
    
    if success:
        logger.info("🎉 All tests passed! Merge analysis is working correctly.")
        logger.info("\n💡 To use the merge analysis:")
        logger.info("   1. Ensure Ollama is running: ollama serve")
        logger.info("   2. Run: ./scripts/doc_processing/run_merge_analysis.sh")
        logger.info("   3. Or: python scripts/doc_processing/merge_analyzer.py --inventory doc_index.json")
    else:
        logger.error("❌ Tests failed. Check the logs above for details.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
