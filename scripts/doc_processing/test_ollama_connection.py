#!/usr/bin/env python3
"""
Simple test script to verify Ollama connection and basic functionality.
"""

import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test Ollama connection and basic functionality."""
    logger.info("🔍 Testing Ollama Connection")
    logger.info("=" * 40)
    
    try:
        # Test with default settings
        client = OllamaClient()
        logger.info(f"Testing connection to: {client.host}")
        logger.info(f"Using model: {client.model}")
        
        # Test availability
        logger.info("Checking Ollama availability...")
        is_available = client.is_available()
        
        if is_available:
            logger.info("✅ Ollama is available and ready!")
            
            # Test a simple completion
            logger.info("Testing simple completion...")
            response = client.generate_completion(
                "What is the capital of France? Answer in one word.",
                "You are a helpful assistant."
            )
            
            if response:
                logger.info(f"✅ Simple completion successful: '{response.strip()}'")
                
                # Test JSON completion
                logger.info("Testing JSON completion...")
                json_response = client.generate_completion(
                    'Respond with JSON: {"test": "success", "number": 42}',
                    "You are a helpful assistant. Always respond with valid JSON only."
                )
                
                if json_response:
                    logger.info(f"✅ JSON completion successful: {json_response.strip()}")
                    
                    # Show statistics
                    stats = client.get_stats()
                    logger.info(f"📊 Client statistics: {stats}")
                    
                    logger.info("\n🎉 All tests passed! Ollama is working correctly.")
                    return 0
                else:
                    logger.error("❌ JSON completion failed")
                    return 1
            else:
                logger.error("❌ Simple completion failed")
                return 1
        else:
            logger.error("❌ Ollama is not available")
            logger.info("\n💡 Troubleshooting:")
            logger.info("   1. Make sure Ollama is running: ollama serve")
            logger.info("   2. Install a model: ollama pull mistral")
            logger.info("   3. Check if the service is accessible: curl http://localhost:11434/api/tags")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        logger.debug("Full exception:", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
