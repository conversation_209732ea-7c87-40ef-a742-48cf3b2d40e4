#!/usr/bin/env python3
"""
Test script to verify the prompt formatting fix.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def test_prompt_formatting():
    """Test that the prompt formatting works correctly."""
    print("Testing prompt formatting fix...")
    
    try:
        from scripts.doc_processing.analyzer.core.constants import COMPREHENSIVE_ANALYSIS_PROMPT
        
        # Test formatting with sample data
        test_prompt = COMPREHENSIVE_ANALYSIS_PROMPT.format(
            title="Test Document",
            path="test.md",
            content="This is test content for formatting."
        )
        
        print("✓ Prompt formatting successful!")
        print(f"✓ Prompt length: {len(test_prompt)} characters")
        print(f"✓ Contains title: {'Test Document' in test_prompt}")
        print(f"✓ Contains path: {'test.md' in test_prompt}")
        print(f"✓ Contains content: {'This is test content' in test_prompt}")
        
        # Check that JSON structure is preserved
        print(f"✓ Contains JSON structure: {'content_analysis' in test_prompt}")
        print(f"✓ Contains quality assessment: {'quality_assessment' in test_prompt}")
        
        return True
        
    except Exception as e:
        print(f"✗ Prompt formatting failed: {e}")
        return False

def test_ollama_availability():
    """Test Ollama availability."""
    print("\nTesting Ollama availability...")
    
    try:
        from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient
        
        client = OllamaClient()
        is_available = client.is_available()
        
        if is_available:
            print("✓ Ollama is available and ready")
            return True
        else:
            print("✗ Ollama is not available")
            print("  Make sure Ollama is running: ollama serve")
            print("  And a model is installed: ollama pull llama3.2")
            return False
            
    except Exception as e:
        print(f"✗ Ollama test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 Testing Documentation Optimizer Fixes")
    print("=" * 45)
    
    # Test 1: Prompt formatting
    prompt_ok = test_prompt_formatting()
    
    # Test 2: Ollama availability
    ollama_ok = test_ollama_availability()
    
    print("\n" + "=" * 45)
    
    if prompt_ok and ollama_ok:
        print("🎉 All tests passed! The optimizer should work now.")
        print("\n💡 Try running:")
        print("   python scripts/doc_processing/doc_optimizer.py \\")
        print("       --inventory doc_index.json \\")
        print("       --verbose \\")
        print("       --exclude-dirs tests temp bmad-agent \\")
        print("       --workers 2 \\")
        print("       --max-docs 3 \\")
        print("       --timeout 180")
    elif prompt_ok:
        print("⚠️  Prompt formatting is fixed, but Ollama is not available.")
        print("   Start Ollama and try again.")
    else:
        print("❌ Tests failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
