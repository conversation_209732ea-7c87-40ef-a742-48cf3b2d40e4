#!/usr/bin/env python3
"""
Test script for the robust documentation analysis with enhanced error handling.
"""

import json
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.doc_processing.analyzer.core.ollama_client import OllamaClient
from scripts.doc_processing.analyzer.analyzers.doc_optimization import analyze_document_comprehensive
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_ollama_robustness():
    """Test Ollama client robustness and error handling."""
    logger.info("Testing Ollama client robustness...")
    
    try:
        client = OllamaClient()
        
        # Test availability
        logger.info("Testing Ollama availability...")
        is_available = client.is_available()
        logger.info(f"Ollama available: {is_available}")
        
        if not is_available:
            logger.error("Ollama is not available. Please start Ollama and ensure a model is installed.")
            return False
        
        # Test simple completion
        logger.info("Testing simple completion...")
        response = client.generate_completion(
            "What is 2+2? Respond with just the number.",
            "You are a helpful assistant."
        )
        
        if response:
            logger.info(f"Simple completion successful: {response.strip()}")
        else:
            logger.error("Simple completion failed")
            return False
        
        # Test JSON completion
        logger.info("Testing JSON completion...")
        json_prompt = """
        Analyze this text and respond with JSON:
        
        Text: "This is a simple test document about API authentication."
        
        Respond with:
        {
          "main_topic": "the main topic",
          "content_type": "the type of content",
          "quality": 3
        }
        """
        
        json_response = client.generate_completion(
            json_prompt,
            "You are a document analyst. Always respond with valid JSON only."
        )
        
        if json_response:
            logger.info(f"JSON completion response: {json_response}")
            
            # Try to parse the JSON
            try:
                from scripts.doc_processing.analyzer.analyzers.doc_optimization import _extract_json_from_response
                parsed = _extract_json_from_response(json_response)
                if parsed:
                    logger.info(f"Successfully parsed JSON: {parsed}")
                else:
                    logger.warning("Could not parse JSON response")
            except Exception as e:
                logger.error(f"Error parsing JSON: {e}")
        else:
            logger.error("JSON completion failed")
            return False
        
        # Show statistics
        stats = client.get_stats()
        logger.info(f"Client statistics: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Ollama robustness test failed: {e}")
        logger.debug("Full exception:", exc_info=True)
        return False

def test_document_analysis_robustness():
    """Test document analysis with various edge cases."""
    logger.info("Testing document analysis robustness...")
    
    # Test documents with various issues
    test_docs = [
        {
            "path": "test/good_doc.md",
            "title": "Good Documentation Example",
            "size": 1000,
            "content": """
# Good Documentation Example

This is a well-structured document with clear content.

## Overview
This document provides comprehensive information about our API.

## Getting Started
Follow these steps to get started:
1. Install the SDK
2. Configure authentication
3. Make your first request

## Examples
Here are some practical examples:

```python
import api_client
client = api_client.Client(api_key="your_key")
response = client.get_data()
```

## Best Practices
- Always use HTTPS
- Handle errors gracefully
- Cache responses when appropriate

This document is complete and useful.
            """
        },
        {
            "path": "test/short_doc.md",
            "title": "Very Short Doc",
            "size": 50,
            "content": "# Short\n\nThis is too short."
        },
        {
            "path": "test/empty_doc.md",
            "title": "Empty Document",
            "size": 0,
            "content": ""
        },
        {
            "path": "test/malformed_doc.md",
            "title": "Malformed Document",
            "size": 200,
            "content": "# Malformed\n\nThis document has weird characters: \x00\x01\x02 and unicode: 🚀📊"
        },
        {
            "path": "test/long_doc.md",
            "title": "Very Long Document",
            "size": 10000,
            "content": "# Long Document\n\n" + "This is a very long document. " * 500 + "\n\nEnd of document."
        }
    ]
    
    try:
        # Initialize components
        ollama_client = OllamaClient()
        if not ollama_client.is_available():
            logger.error("Ollama not available for document analysis test")
            return False
        
        doc_store = CachedDocumentStore()
        
        # Add test content to store
        for doc in test_docs:
            doc_store._content_cache[doc["path"]] = doc["content"]
        
        # Test analysis on each document
        successful = 0
        failed = 0
        
        for doc in test_docs:
            logger.info(f"Testing analysis of: {doc['path']}")
            
            # Create mock summary
            class MockSummary:
                def __init__(self, path, title, size):
                    self.path = path
                    self.title = title
                    self.size = size
            
            summary = MockSummary(doc["path"], doc["title"], doc["size"])
            
            try:
                result = analyze_document_comprehensive(summary, doc_store, ollama_client)
                
                if result:
                    logger.info(f"✓ Successfully analyzed {doc['path']}")
                    logger.debug(f"Result keys: {list(result.keys())}")
                    successful += 1
                else:
                    logger.warning(f"✗ No result for {doc['path']} (expected for some test cases)")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"✗ Exception analyzing {doc['path']}: {e}")
                failed += 1
        
        logger.info(f"Document analysis test complete: {successful} successful, {failed} failed")
        
        # Show final statistics
        stats = ollama_client.get_stats()
        logger.info(f"Final Ollama statistics: {stats}")
        
        return successful > 0  # Success if at least one document was analyzed
        
    except Exception as e:
        logger.error(f"Document analysis robustness test failed: {e}")
        logger.debug("Full exception:", exc_info=True)
        return False

def main():
    """Main test function."""
    logger.info("🔧 Testing Robust Documentation Analysis")
    logger.info("=" * 50)
    
    # Test 1: Ollama robustness
    ollama_ok = test_ollama_robustness()
    
    logger.info("")
    logger.info("-" * 50)
    
    # Test 2: Document analysis robustness
    analysis_ok = test_document_analysis_robustness()
    
    logger.info("")
    logger.info("=" * 50)
    
    if ollama_ok and analysis_ok:
        logger.info("🎉 All robustness tests passed!")
        logger.info("\n💡 The enhanced system should now:")
        logger.info("   • Provide detailed logging for debugging")
        logger.info("   • Handle Ollama connection issues gracefully")
        logger.info("   • Parse malformed JSON responses")
        logger.info("   • Continue analysis even if some documents fail")
        logger.info("   • Provide clear error messages and statistics")
    else:
        logger.error("❌ Some tests failed. Check the logs above for details.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
